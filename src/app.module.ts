import { BullModule } from '@nestjs/bull';
import { Inject, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { APP_FILTER, RouterModule } from "@nestjs/core";
import { MongooseModule } from '@nestjs/mongoose';

import { AccesstradeModule } from './accesstrade/accesstrade.module';
import { AffiliateConfigurationModule } from './affiliate-configuration/affiliate-configuration.module';
import { AffiliateMobileModule } from './affiliate-mobile/affiliate-mobile.module';
import { BrandConfigurationModule } from './brand-configuration/brand-configuration.module';
import { ConnectionNameEnum } from './common/core/constants';
import { ConfigurationModule } from './configuration/configuration.module';
import { CoreTransactionModule } from './core-transaction/core-transaction.module';
import { MerchantModule } from './merchant/merchant.module';
import { UserModule } from './user/user.module';
import { AffiliateConfigurationService } from './affiliate-configuration/affiliate-configuration.service';
import { getLoggerModule } from './pino.config';
import { CrawlDataModule } from './crawl-data/crawl-data.module';
import { TransactionV2Module } from './transaction-v2/transaction.module';
import { AffiliateMobileV5Module } from './affiliate-mobile-v5/affiliate-mobile-v5.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { EventListenersModule } from "./event-listeners/event-listeners.module";
import { CommissionPolicyModule } from "./commission-policy/commission-policy.module";
import { FileSizeExceptionFilter } from "./common/filter/file-size-exception.filter";
import { COMMANDS } from './system-command/command';
import { CommonModule } from './common/common.module';
import { RedisModule } from "./redis/redis.module";
import { AccessTradeFakeModule } from "./accesstrade-fake/accesstrade-fake.module";

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('mongodb'),
      }),
      inject: [ConfigService],
      connectionName: ConnectionNameEnum.AFFILIATE_V2,
    }),
    BullModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        redis: configService.get('redis'),
        prefix: configService.get('redis.prefix'),
      }),
      inject: [ConfigService],
    }),
    RouterModule.register([
      {
        path: 'api',
        children: [
          {
            path: 'v1',
            children: [
              {
                path: 'brand-config',
                module: BrandConfigurationModule,
              },
              {
                path: 'commission-policies',
                module: CommissionPolicyModule,
              },
              {
                path: 'config',
                module: AffiliateConfigurationModule,
              },
              {
                path: 'accesstrade',
                module: AccesstradeModule,
              },
              {
                path: 'redis',
                module: RedisModule,
              },
              {
                path: 'accesstrade-fake',
                module: AccessTradeFakeModule
              }
            ],
          },
          {
            path: 'v2',
            children: [
              {
                path: 'transactions',
                module: TransactionV2Module,
              },
            ],
          },
        ],
      },
      // For mobile
      {
        path: 'v1',
        module: AffiliateMobileModule,
      },
      {
        path: 'api/v5',
        module: AffiliateMobileV5Module,
      },
    ]),
    ConfigurationModule,
    BrandConfigurationModule,
    MerchantModule,
    AffiliateConfigurationModule,
    AccesstradeModule,
    UserModule,
    CoreTransactionModule,
    AffiliateMobileModule,
    CrawlDataModule,
    TransactionV2Module,
    AffiliateMobileV5Module,
    EventListenersModule,
    CommissionPolicyModule,
    CommonModule,
    RedisModule,
    AccessTradeFakeModule,
    ...getLoggerModule(),
  ],
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: FileSizeExceptionFilter,
    },
    ...COMMANDS
  ],
})
export class AppModule {
  @Inject() private readonly affiliateConfigurationService: AffiliateConfigurationService;

  onModuleInit() {
    this.affiliateConfigurationService.init();
  }
}
