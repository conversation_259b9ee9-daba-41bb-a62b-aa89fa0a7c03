import * as process from "node:process";

export default () => ({
  // app port
  env: process.env.NODE_ENV,
  name: process.env.APP_NAME,
  port: parseInt(process.env.PORT, 10),
  version: process.env.VERSION,
  api_logger: process.env.API_LOGGER,

  // mongo connection string
  mongodb: process.env.DB_MONGO_URI,

  // kafka connection string
  kafka: {
    uri: process.env.KAFKA_URI,
    // name: process.env.KAFKA_NAME,
    clientId: process.env.KAFKA_CLIENT_ID,
    consumerGroupId: process.env.KAFKA_CONSUMER_GROUP_ID,
  },

  // redis connection string
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT, 10),
    prefix: process.env.REDIS_PREFIX + ':',
    expire_time_config: parseInt(process.env.REDIS_EXPIRE_TIME_CONFIG, 10),
  },

  // id_service
  id_service: {
    base_url: process.env.ID_SERVICE_BASE_URL,
    url: process.env.ID_SERVICE_HOST,
    url_token: process.env.ID_SERVICE_HOST + '/token',
    secret_key: process.env.ID_SERVICE_SECRET_KEY,
    client_name: process.env.ID_SERVICE_NAME,
  },
  
  // tcl url service private network
  tcl: {
    tcl_user_service_host: process.env.TCL_USER_SERVICE_HOST,
    tcl_merchant_service_host: process.env.TCL_MERCHANT_SERVICE_HOST,
    tcl_transaction_service_host: process.env.TCL_TRANSACTION_SERVICE_HOST,
  },
  TCL_B2B_GATEWAY_HOST: process.env.TCL_B2B_GATEWAY_HOST,
  accesstrade: {
    public_key: process.env.ACCESSTRADE_PUBLIC_KEY,
    segment_host: process.env.ACCESSTRADE_SEGMENT_HOST,
    api_url: process.env.ACCESSTRADE_API_URL,
    api_key: process.env.ACCESSTRADE_API_KEY,
    fake_data_active: process.env.ACCESSTRADE_FAKE_DATA_ACTIVE === 'true' ? true : false,
  },

  affiliate_v1_host: process.env.AFFILIATE_VERSION_1_HOST,

  bull: {
    base_path: process.env.BULL_BASE_PATH,
    username: process.env.BULL_USERNAME,
    password: process.env.BULL_PASSWORD,
  },

  cron: {
    cron_crawl_task_disable: process.env.CRON_CRAWL_TASK_DISABLED === 'true' ? true : false,
    cron_crawl_task_time: process.env.CRON_CRAWL_TASK_TIME,
    cron_init_transaction_disabled: process.env.CRON_INIT_TRANSACTION_DISABLED === 'true' ? true : false,
    cron_init_transaction_time: process.env.CRON_INIT_TRANSACTION_TIME,
    cron_temp_approved_disabled: process.env.CRON_TEMP_APPROVED_DISABLED === 'true' ? true : false,
    cron_temp_approved_time: process.env.CRON_TEMP_APPROVED_TIME,
    cron_approved_disabled: process.env.CRON_APPROVED_DISABLED === 'true' ? true : false,
    cron_approved_time: process.env.CRON_APPROVED_TIME,
    cron_rejected_disabled: process.env.CRON_REJECTED_DISABLED === 'true' ? true : false,
    cron_rejected_time: process.env.CRON_REJECTED_TIME,
    cron_refund_disabled: process.env.CRON_REFUND_DISABLED === 'true' ? true : false,
    cron_refund_time: process.env.CRON_REFUND_TIME
  },
});
