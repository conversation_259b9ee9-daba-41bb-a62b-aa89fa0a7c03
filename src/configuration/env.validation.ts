import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // App
  NODE_ENV: Joi.string().default('development'),
  APP_NAME: Joi.string().default('affiliate_v2'),
  APP_NAME_WORKER: Joi.string().default('worker_affiliate_v2'),
  PORT: Joi.number().default(3000),
  VERSION: Joi.string().default('1.0'),
  API_LOGGER: Joi.string().default('true'),

  // Mongo
  DB_MONGO_URI: Joi.string().required(),

  // Kafka

  // // Redis
  REDIS_HOST: Joi.string().required(),
  REDIS_PORT: Joi.number().required(),
  REDIS_PREFIX: Joi.string().default('base-service'),
  REDIS_EXPIRE_TIME_CONFIG: Joi.number().default(300000),

  // id_service
  ID_SERVICE_BASE_URL: Joi.string().required(),
  ID_SERVICE_HOST: Joi.string().required(),
  ID_SERVICE_SECRET_KEY: Joi.string().required(),

  // tcl
  TCL_USER_SERVICE_HOST: Joi.string().required(),
  TCL_MERCHANT_SERVICE_HOST: Joi.string().required(),
  TCL_TRANSACTION_SERVICE_HOST: Joi.string().required(),

  // accesstrade
  ACCESSTRADE_PUBLIC_KEY: Joi.string().required(),
  ACCESSTRADE_SEGMENT_HOST: Joi.string().required(),
  ACCESSTRADE_FAKE_DATA_ACTIVE: Joi.string().required(),

  //Affiliate v1 host
  AFFILIATE_VERSION_1_HOST: Joi.string().required(),

  // bull
  BULL_BASE_PATH: Joi.string().required(),
  BULL_USERNAME: Joi.string().required(),
  BULL_PASSWORD: Joi.string().required(),

  //Cron
  CRON_CRAWL_TASK_DISABLED: Joi.string().required(),
  CRON_CRAWL_TASK_TIME: Joi.string().required(),
  CRON_INIT_TRANSACTION_DISABLED: Joi.string().required(),
  CRON_INIT_TRANSACTION_TIME: Joi.string().required(),
  CRON_TEMP_APPROVED_DISABLED: Joi.string().required(),
  CRON_TEMP_APPROVED_TIME: Joi.string().required(),
  CRON_APPROVED_DISABLED: Joi.string().required(),
  CRON_APPROVED_TIME: Joi.string().required(),
  CRON_REJECTED_DISABLED: Joi.string().required(),
  CRON_REJECTED_TIME: Joi.string().required(),
  CRON_REFUND_DISABLED: Joi.string().required(),
  CRON_REFUND_TIME: Joi.string().required()
});
