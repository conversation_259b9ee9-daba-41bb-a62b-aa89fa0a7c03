// pino.config.ts
import { INestApplication, Logger as NestLogger } from '@nestjs/common';
import { Logger, LoggerModule } from 'nestjs-pino';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const ecsFormat = require('@elastic/ecs-pino-format');

export const isElasticEnable = () => process.env.ELASTIC_APM_ACTIVE === 'true';

export const getLoggerService = (app: INestApplication) => {
  return isElasticEnable() ? app.get(Logger) : new NestLogger();
};

export const getLoggerModule = () => {
  return isElasticEnable() ? [LoggerModule.forRoot({ pinoHttp: { ...ecsFormat() } })] : [];
};
