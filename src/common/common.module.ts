import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './services/redis.service';
import { NotificationService } from './services/notification.service';

@Module({
  imports: [ConfigModule],
  providers: [
    RedisService,
    ConfigService,
    NotificationService
  ],
  exports: [
    RedisService,
    NotificationService
  ],
})
export class CommonModule {}
