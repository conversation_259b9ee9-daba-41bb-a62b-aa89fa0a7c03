export class TransactionStatusHelper {
  constructor(private readonly status: number, private readonly isConfirmed: number) {}

  isPending(): boolean {
    return this.status === 0 && this.isConfirmed === 0;
  }

  isTemporarilyApproved(): boolean {
    return this.status === 1 && this.isConfirmed === 0;
  }

  isApproved(): boolean {
    return this.status === 1 && this.isConfirmed === 1;
  }

  isRejected(): boolean {
    return this.status === 2 && this.isConfirmed === 0;
  }

  isRefunded(): boolean {
    return this.status === 3 && this.isConfirmed === 0;
  }

  getStatusLabel(): string {
    if (this.isPending()) {
      return 'Chờ xử lý';
    }
    if (this.isTemporarilyApproved()) {
      return 'Tạm duyệt';
    }
    if (this.isApproved()) {
      return 'Được duyệt';
    }
    if (this.isRejected()) {
      return 'Đã hủy';
    }
    if (this.isRefunded()) {
      return 'Refund';
    }
    return 'Trạng thái không xác định';
  }

  canTransitionTo(newStatus: number, newIsConfirmed: number): boolean {
    const currentStatus = this.getStatusLabel();

    const target = new TransactionStatusHelper(newStatus, newIsConfirmed);
    const newStatusLabel = target.getStatusLabel();

    switch (currentStatus) {
      case 'Chờ xử lý':
        return ['Tạm duyệt', 'Đã hủy'].includes(newStatusLabel);
      case 'Tạm duyệt':
        return ['Được duyệt', 'Refund'].includes(newStatusLabel);
      case 'Được duyệt':
        return false;
      case 'Đã hủy':
      case 'Refund':
        return false;
      default:
    }
  }
}
