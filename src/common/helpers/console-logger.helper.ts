export class ConsoleLogger {
  constructor(private readonly context: string) {}

  log(message: string) {
    console.log(`[${new Date().toISOString()}] [${this.context}] ${message}`);
  }

  error(message: string, error?: any) {
    console.error(`[${new Date().toISOString()}] [${this.context}] ERROR: ${message}`, error || '');
  }

  warn(message: string) {
    console.warn(`[${new Date().toISOString()}] [${this.context}] WARN: ${message}`);
  }

  debug(message: string) {
    console.debug(`[${new Date().toISOString()}] [${this.context}] DEBUG: ${message}`);
  }
}
