export enum AppEvent {
  AffiliateTransactionCreated = 'affiliate.transaction.created',
  AffiliateTransactionUpdated = 'affiliate.transaction.updated',
  AffiliateTransactionDeleted = 'affiliate.transaction.deleted',
}

export interface AffiliateTransaction {
  orderId?: string;
  userId?: string;
  merchantId?: string;
  merchantName?: string;
  merchantLogo?: string;
  totalCommission?: number;
  totalOrderAmount?: number;
  totalVUI?: number;
  status?: 'TEMP_APPROVED' | 'APPROVED';
  transactions: {
    transactionId?: string;
    commission?: number;
    orderAmount?: number;
    totalVUI?: number;
    firstAmount?: number;
    secondAmount?: number;
    firstAmountStatus?: string;
    secondAmountStatus?: string;
    status?: 'PENDING' | 'TEMP_APPROVED' | 'APPROVED' | 'CANCELLED' | 'REFUNDED';
    transactionDate?: Date;
  }[];
  orderDate?: Date;
}

export type AppEventPayload = {
  [AppEvent.AffiliateTransactionCreated]: AffiliateTransaction;
  [AppEvent.AffiliateTransactionUpdated]: Partial<AffiliateTransaction>;
  [AppEvent.AffiliateTransactionDeleted]: {
    transactionId: string;
    userId: string;
  };
};
