import { HttpException } from '@nestjs/common';
import { ICalculateIssue } from './interfaces';

class Utils {
  public sanitizePageSize = function (page: number, size: number) {
    if (page < 1 || size < 0) throw new HttpException('error page size', 400);
    const limit = size || 10;
    const skip = (page - 1) * size;

    return {
      limit,
      skip,
    };
  };

  public calculateTotalIssue = function (data: ICalculateIssue, shouldCovertToPoints: boolean = false) {
    const { totalCommission, earnCommissionRate } = data;
    const sum = totalCommission * (earnCommissionRate / 100);
    return shouldCovertToPoints ? Math.floor(sum / 1000) : Math.floor(sum);
  };

  public calculate1stIssue = function (data: ICalculateIssue, shouldCovertToPoints: boolean = false) {
    const { totalCommission, earnRate, earnCommissionRate, firstIssueRate } = data;
    const sum = totalCommission * (earnCommissionRate / 100) * (firstIssueRate / 100);
    return shouldCovertToPoints ? Math.floor(sum / 1000) : Math.floor(sum);
  };

  /**
   * Max_1st_issue_points = get from config
   * total_1st_issued_points = (sum fistAmount of all orders have status = issued_1st)/1000
   * remain_1st_issue_points =  Max_1st_issue - total_1st_issued
   * current_order_1st_issue_points = calculate1stIssue()
   * @param max1stIssuePoints
   * @param total1stIssuedPoints
   * @returns
   */
  public calculateBillForCoreTransaction = function (max1stIssuedPoints: number, total1stIssuedPoints: number, calculateIssueData: ICalculateIssue) {
    const currentOrder1stIssuedPoints = this.calculate1stIssue(calculateIssueData, false);

    const remain1stIssuePoints = max1stIssuedPoints - total1stIssuedPoints <= 0 ? 0 : max1stIssuedPoints - total1stIssuedPoints;

    let expectedBillPoints: number = 0;
    let billAmount: number = 0;
    if (currentOrder1stIssuedPoints <= remain1stIssuePoints) {
      expectedBillPoints = currentOrder1stIssuedPoints;
      billAmount = this.calculate1stIssue(calculateIssueData, false);
    }
    // else {
    //   expectedBillPoints = remain1stIssuePoints;
    //   billAmount = remain1stIssuePoints * 1000;
    // }
    return {
      expectedBillPoints,
      billAmount: Math.floor(billAmount),
      currentOrder1stIssuedPoints
    };
  };
}

export default new Utils();
