import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsNumber, IsObject, IsOptional, IsString, Min } from 'class-validator';

export default class QueryCommonDto {
  @Transform(({ value }) => Number.parseInt(value))
  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({
    type: 'number',
    example: 1,
    required: false,
  })
  page? = 1;

  @Transform(({ value }) => Number.parseInt(value))
  @IsNumber()
  @IsOptional()
  @Min(1)
  @ApiProperty({
    type: 'number',
    default: 10,
    example: 10,
    required: false,
  })
  size? = 10;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    example: '-createdAt',
    description: `"": ascending,\n "-" : descending`,
  })
  sort?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    example: 'test',
    description: `relative search by input value`,
  })
  search?: string;

  @Transform(({ value }) => (typeof value === 'string' ? [value] : value))
  @IsArray()
  @IsOptional()
  @ApiProperty({
    required: false,
    default: [],
    example: ['name', 'brandCode'],
    description: 'list of fields to search',
  })
  field?: string[];

  @IsObject()
  @IsOptional()
  @ApiProperty({
    required: false,
    default: {},
    type: 'object',
    description: 'extended query',
  })
  filter?: object = {};
}
