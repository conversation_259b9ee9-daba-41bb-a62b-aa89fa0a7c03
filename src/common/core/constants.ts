export enum OrderStatusEnum {
  HOLD = 'HOLD',
  ISSUING_1ST = 'ISSUING_1ST',
  ISSUING_2ND = 'ISSUING_2ND',
  ISSUE_FAILED_1ST = 'ISSUE_FAILED_1ST',
  ISSUE_FAILED_2ND = 'ISSUE_FAILED_2ND',
  ISSUED_1ST = 'ISSUED_1ST',
  DONE = 'DONE',
  REJECTED = 'REJECTED',
  REFUNDING = 'REFUNDING',
  REFUNDED = 'REFUNDED',
  REFUND_FAILED = 'REFUND_FAILED',
}

export enum TransactionStatusEnum {
  HOLD,
  TEMP_REVIEW,
  REJECTED,
  REFUNDED,
}

export enum ConnectionNameEnum {
  AFFILIATE_V2 = 'AFFILIATE_V2',
}

export enum CrawlHistoryStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum CrawlHistoryType {
  TRANSACTION = 'TRANSACTION'
}

export enum CACHE_TIME {
  ONE_MINUTES = 60,
  FIVE_MINUTES = 5 * 60,
  THIRTY_MINUTES = 30 * 60,
  THIRTY_FIVE_MINUTES = 35 * 60,
  ONE_HOUR = 60 * 60,
  TWO_HOURS = 2 * 60 * 60,
  ONE_DAY = 24 * 60 * 60
}

export enum TransactionIssueStatus {
  HOLD = 'HOLD',
  ISSUING_1ST = 'ISSUING_1ST',
  ISSUING_2ND = 'ISSUING_2ND',
  ISSUED_1ST = 'ISSUED_1ST',
  ISSUE_FAILED_1ST = 'ISSUE_FAILED_1ST',
  ISSUE_FAILED_2ND = 'ISSUE_FAILED_2ND',
  REJECTED = 'REJECTED',
  REFUNDING = 'REFUNDING',
  REFUNDED = 'REFUNDED',
  REFUND_FAILED = 'REFUND_FAILED',
  DONE = 'DONE',
}

export enum StatusCommon {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export enum TransactionInitLogKey {
  INIT_TRANSACTION = 'INIT_TRANSACTION',
  UPDATE_TRANSACTION_STATUS = 'UPDATE_TRANSACTION_STATUS',
  UPDATE_TRANSACTION_COMMISSION = 'UPDATE_TRANSACTION_COMMISSION'
}

/* WORKER */
export enum QUEUE_PROCESSOR {
  CALCULATE_VUI = 'CALCULATE_VUI',
  TRANSACTION_OPERATION = 'TRANSACTION_OPERATION',
  UPDATE_REAL_VUI = 'UPDATE_REAL_VUI',
  UPDATE_STATUS_TRANSACTION = 'UPDATE_STATUS_TRANSACTION',
  ACCESSTRADE_CRAWL_API = 'ACCESSTRADE_CRAWL_API', //QUEUE_CONSUMER
  UPDATE_TRANSACTION = 'UPDATE_TRANSACTION',
  TRANSACTION_TASK = 'TRANSACTION_TASK',
  REISSUE_VUI = 'REISSUE_VUI'
}

export enum QUEUE_CONSUMER {
  CRAWL_DAY = "CRAWL_DAY",
  CRAWL_PAGE = "CRAWL_PAGE",
  CALCULATE_VUI = 'CALCULATE_VUI',
  TRANSACTION_OPERATION = 'TRANSACTION_OPERATION',
  UPDATE_REAL_VUI = 'UPDATE_REAL_VUI',
  UPDATE_STATUS_TRANSACTION = 'UPDATE_STATUS_TRANSACTION',
  ACCESSTRADE_POSTBACK = 'ACCESSTRADE_POSTBACK',
  UPDATE_TRANSACTION = 'UPDATE_TRANSACTION',
  TRANSACTION_TASK = 'TRANSACTION_TASK',
  REISSUE_VUI = 'REISSUE_VUI'
}

export enum TransactionOperationAction {
  RETRY = "RETRY",
  ROLLBACK_DUPLICATE = "ROLLBACK_DUPLICATE",
  RECALCULATE = "RECALCULATE"
}

export enum TransactionOperationStatus {
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export enum TransactionFieldType {
  COMMISSION = 'commission',
  STATUS = 'status',
}

export enum TransactionTaskAction {
  INIT = 'INIT',
  REJECT = 'REJECT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVAL = 'APPROVAL',
  REFUND = 'REFUND'
}