export interface ICalculateIssue {
  totalCommission: number;
  earnRate: number;
  earnCommissionRate: number;
  firstIssueRate?: number;
}

export interface ITransaction {
  merchant?: string | null;
  status: number;
  update_time?: Date | null;
  click_url?: string | null;
  conversion_platform?: string | null;
  utm_campaign?: string | null;
  product_category?: string | null;
  utm_content?: string | null;
  transaction_time?: Date | null;
  product_image?: string | null;
  utm_source?: string | null;
  transaction_value?: number | null;
  _extra?: Object | null;
  reason_rejected?: string | null;
  category_name?: string | null;
  utm_term?: string | null;
  product_id?: string | null;
  is_confirmed: number;
  confirmed_time?: Date | null;
  product_price?: number;
  id: string;
  commission?: number | null;
  customer_type?: string | null;
  conversion_id: string;
  utm_medium?: string | null;
  product_quantity?: number | null;
  click_time?: Date | null;
  product_name?: string | null;
  transaction_id: string;
}