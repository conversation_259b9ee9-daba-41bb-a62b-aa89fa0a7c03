import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsPositive, IsString, Max, Min } from 'class-validator';
import { HydratedDocument } from 'mongoose';

export class PaginationCursorDto<T> {
  data: Array<HydratedDocument<T, {}, {}>>;
  meta: {
    cursors: {
      before: string | null;
      after: string | null;
    };
    total: number
  };
}

export class PaginationCursorQueryDto {
  @IsOptional()
  @IsString()
  after?: string | null;

  @IsOptional()
  @IsString()
  before?: string | null;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(value, 10))
  limit: number = 20;
}

export class PaginationQueryDto {
  @IsOptional()
  @IsPositive()
  @Min(1)
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @IsPositive()
  @Min(1)
  @Max(50)
  @Type(() => Number)
  limit?: number;
}

export class PaginationMetaDto {
  total: number;
  currentPage: number;
  lastPage: number;
  limit: number;
}

export class PaginationDto<T> {
  data: T[];
  meta: PaginationMetaDto;
}


export interface PopulateOption {
  path: string;
  model?: string;
  select?: string;
  populate?: PopulateOption[];
}
