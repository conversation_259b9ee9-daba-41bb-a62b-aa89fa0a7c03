import { CanActivate, ExecutionContext, HttpException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MobileTokenAuth } from '@taptap-discovery/credentials';

@Injectable()
export class AccessTokenMobileGuard implements CanActivate {
  private mobileTokenAuth: MobileTokenAuth;
  private readonly logger = new Logger(AccessTokenMobileGuard.name);
  constructor(private readonly configService: ConfigService) {
    this.mobileTokenAuth = new MobileTokenAuth({
      baseUrl: this.configService.get<string>('id_service.base_url'),
      idServiceHost: this.configService.get<string>('id_service.url'),
      idServiceSecret: this.configService.get<string>('id_service.secret_key'),
      idServiceName: 'authentication-service',
    });
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    let token = null;
    const authorization = request.headers.authorization;
    if (authorization) {
      token = authorization.replace(/(bearer)/i, '').trim();
    } else if (request.cookies && request.cookies['accessToken']) {
      token = request.cookies['accessToken'].trim();
    }
    if (!token) {
      this.logger.error('No token provided');
      throw new HttpException('No token provided', 403);
    }
    try {
      const info = await this.mobileTokenAuth.verifyToken(token);
      if (!info || !info.success) {
        this.logger.error(info.message, token);
        throw new HttpException(info.message || 'Invalid token', info.code || 400);
      }
      request.authorization = {
        userId: info.userId,
        mobile: info.mobile,
        deviceId: info.deviceId,
        brand: info.brand,
        expiresAt: info.expiresAt,
        issueAt: info.issueAt,
        platform: info.platform,
        version: info.version,
        remainingTime: info.remainingTime,
      };
      return true;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(error.message || 'validation error', error.status || 400);
    }
  }
}
