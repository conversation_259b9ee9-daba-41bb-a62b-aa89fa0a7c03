import { configStub } from './../../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AccessTokenGuard } from './access-token.guard';

describe('AccessGuard', () => {
  let accessTokenGuard: AccessTokenGuard;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();
    configService = module.get<ConfigService>(ConfigService);
    accessTokenGuard = new AccessTokenGuard(configService);
  });

  it('should be defined', () => {
    expect(accessTokenGuard).toBeDefined();
  });
});
