import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { InternalAuth } from '@taptap-discovery/credentials';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class IdTokenAuthenGuard implements CanActivate {
  private internalAuth: InternalAuth;
  constructor(private configService: ConfigService) {
    this.internalAuth = new InternalAuth({
      idServiceHost: this.configService.get<string>('ID_SERVICE_HOST'),
      idServiceSecret: this.configService.get<string>('ID_SERVICE_SECRET_KEY'),
      idServiceName: this.configService.get<string>('ID_SERVICE_NAME'),
    });
  }
  async canActivate(context: ExecutionContext): Promise<any> {
    const request = context.switchToHttp().getRequest();
    const token = this.getToken(request);
    const status = await this.internalAuth.verifyToken(token);
    if (status) {
      return true;
    }
    throw new ForbiddenException();
  }
  getToken(request: Request): string {
    const header = request.header('Authorization');
    if (header) {
      return header.replace('Bearer', '').trim();
    }
    throw new UnauthorizedException();
  }
}
