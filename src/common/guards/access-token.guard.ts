import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InternalAuth } from '@taptap-discovery/credentials';

@Injectable()
export class AccessTokenGuard implements CanActivate {
  private internalAuth: InternalAuth;
  private readonly logger = new Logger(AccessTokenGuard.name);
  constructor(private readonly configService: ConfigService) {
    this.internalAuth = new InternalAuth({
      idServiceHost: this.configService.get<string>('id_service.url'),
      idServiceSecret: this.configService.get<string>('id_service.secret_key'),
      idServiceName: 'authentication-service',
    });
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    let token = null;
    const authorization = request.headers.authorization;
    if (authorization) {
      token = authorization.replace(/(bearer)/i, '').trim();
    } else if (request.cookies && request.cookies['accessToken']) {
      token = request.cookies['accessToken'].trim();
    }
    if (!token) {
      this.logger.error('No token provided');
      return false;
    }
    try {
      const info = await this.internalAuth.verifyToken(token);
      if (!info) {
        this.logger.error('Invalid token ', token);
        return false;
      }
      request.authorization = {
        clientId: info.clientId,
        clientName: info.clientName,
        roles: info.roles,
        groups: info.groups,
        rawToken: token,
      };
      return true;
    } catch (error) {
      this.logger.error('error guard ', error);
      return false;
    }
  }
}
