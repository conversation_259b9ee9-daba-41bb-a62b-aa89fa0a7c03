import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
} from '@nestjs/common';
import { MulterError } from 'multer';

@Catch(MulterError)
export class FileSizeExceptionFilter implements ExceptionFilter {
  catch(exception: MulterError, host: ArgumentsHost) {
    const response = host.switchToHttp().getResponse();

    if (exception.code === 'LIMIT_FILE_SIZE') {
      response.status(400).json({
        statusCode: 400,
        message: 'File size exceeds the limit of 10MB',
      });
    } else {
      response.status(400).json({
        statusCode: 400,
        message: exception.message,
      });
    }
  }
}
