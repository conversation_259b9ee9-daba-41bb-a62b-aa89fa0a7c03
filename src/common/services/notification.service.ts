import {
  NotificationApiResponse,
  NotificationRequest,
  NotificationService as NotificationLYTService,
} from '@taptap-lyt/sdk';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  private readonly notificationService: NotificationLYTService;

  constructor(private readonly configService: ConfigService) {
    const baseURL = this.configService.get<string>('TCL_B2B_GATEWAY_HOST');

    this.notificationService = new NotificationLYTService(baseURL, {
      url: this.configService.get<string>('ID_SERVICE_HOST') + '/token',
      clientName: this.configService.get<string>('ID_SERVICE_NAME'),
      secret: this.configService.get<string>('ID_SERVICE_SECRET_KEY'),
      method: 'POST',
    });

  }

  async sendNotification(payload: NotificationRequest): Promise<NotificationApiResponse> {
    try {
      const data = await this.notificationService.sendInbox(payload);
      return data;
    } catch (error) {
      this.logger.error('Error sendNotification:', error);
      throw new Error('Failed to retrieve point details.');
    }
  }
}
