import {
  Injectable,
  Logger,
  OnApplicationBootstrap,
  OnApplicationShutdown,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import Redlock from 'redlock';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnApplicationShutdown, OnApplicationBootstrap {
  private client: Redis;
  private redlock: Redlock;
  private readonly logger = new Logger(RedisService.name);

  constructor(private readonly configService: ConfigService) { }

  async onApplicationBootstrap() {
    await this.connect();
  }

  async onApplicationShutdown() {
    await this.disconnect();
  }

  private async connect() {
    try {
      const options = this.configService.get('redis');
      this.client = new Redis(options);

      this.redlock = new Redlock([this.client], {
        driftFactor: 0.01,
        retryCount: 10,
        retryDelay: 200,
        retryJitter: 50,
      });

      this.client.on('connect', () => this.logger.log('<PERSON><PERSON> is connecting'));
      this.client.on('ready', () => this.logger.log('Redis is ready'));
      this.client.on('error', (error) => this.logger.error(`Redis error: ${error.message}`));

      this.redlock.on('clientError', (error) =>
        this.logger.error(`Redlock error: ${error.message}`),
      );

      this.logger.log('Redis client connected');
    } catch (error) {
      this.logger.error(`Failed to connect to Redis: ${error.message}`);
    }
  }

  private async disconnect() {
    try {
      if (this.client) {
        await this.client.quit();
        this.logger.log('Redis client disconnected');
      }
    } catch (error) {
      this.logger.error(`Failed to disconnect Redis: ${error.message}`);
    }
  }

  async getAsync(key: string) {
    try {
      const result = await this.client.get(key);
      return JSON.parse(result);
    } catch (error) {
      this.logger.error(`Failed to get key "${key}": ${error.message}`);
      return null;
    }
  }

  async setAsync(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.client.set(key, value, 'EX', ttl);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      this.logger.error(`Failed to set key "${key}": ${error.message}`);
    }
  }

  async delAsync(key: string) {
    try {
      await this.client.del(key);
    } catch (error) {
      this.logger.error(`Failed to delete key "${key}": ${error.message}`);
    }
  }

  async setKeyWithTTL(key: string, value: string, ttl: number): Promise<boolean> {
    try {
      const exists = await this.client.get(key);
      if (exists) {
        return false;
      }

      const result = await this.client.set(key, value, 'EX', ttl);

      if (result) {
        return true;
      } else {
        this.logger.warn(`Failed to set key ${key} because it already exists.`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Failed to set key ${key}:`, error);
      return false
    }
  }


  async createKeysWithTag(tag: string, keyPrefix: string, keyCount: number): Promise<void> {
    const pipeline = this.client.pipeline();
    const tagKey = `tags:${tag}`;

    for (let i = 0; i < keyCount; i++) {
      const key = `${keyPrefix}:${i}`;
      pipeline.set(key, `value_${i}`);
      pipeline.sadd(tagKey, key);
    }

    await pipeline.exec();
    await this.client.expire(tagKey, 3600);
    this.logger.log(`Created ${keyCount} keys with tag "${tag}"`);
  }

  async cacheWithTags<T>(
    tags: string[],
    key: string,
    seconds: number,
    callback: () => Promise<T> | T,
  ): Promise<T> {
    const fullKey = key;

    const cachedValue = await this.client.get(fullKey);
    if (cachedValue) {
      try {
        return JSON.parse(cachedValue) as T;
      } catch (error) {
        this.logger.error(`Error parsing cache for key: ${fullKey}`, error);
        await this.client.del(fullKey); // Xóa cache nếu parse bị lỗi
      }
    }

    const result = await callback();
    if (result !== null && result !== undefined) {
      await this.client.set(fullKey, JSON.stringify(result), 'EX', seconds);
      this.logger.log(`Cache set for key: ${fullKey} with expire time: ${seconds}s`);

      for (const tag of tags) {
        const tagKey = `tags:${tag}`;
        await this.client.sadd(tagKey, fullKey);
        await this.client.expire(tagKey, seconds);
      }
    } else {
      this.logger.warn(`Callback returned null or undefined for key: ${fullKey}`);
    }

    return result;
  }

  async clearCacheByTags(...tags: string[]): Promise<void> {
    try {
      console.time('New ClearCacheByTags Execution Time');
      const keys = [].concat.apply(
        [],
        await Promise.all(tags.map((tag) => this.client.smembers(`tags:${tag}`))),
      );

      const pipeline = this.client.pipeline();

      keys.forEach((key) => {
        pipeline.del(key);
      });

      tags.forEach((tag) => {
        pipeline.del(`tags:${tag}`);
      });

      await pipeline.exec();
      console.timeEnd('New ClearCacheByTags Execution Time');

      this.logger.log(`Cache cleared for tags: ${tags.join(', ')}`);
    } catch (error) {
      console.timeEnd('New ClearCacheByTags Execution Time');
      this.logger.error(`Failed to clear cache for tags: ${tags.join(', ')}. Error: ${error.message}`);
      throw error;
    }
  }

  async clearCacheByTagsOld(...tags: string[]): Promise<void> {
    console.time('Old ClearCacheByTags Execution Time');
    try {
      for (const tag of tags) {
        const tagKey = `tags:${tag}`;
        const keys = await this.client.smembers(tagKey);

        for (const key of keys) {
          await this.client.del(key);
        }

        await this.client.del(tagKey);
      }
      console.timeEnd('Old ClearCacheByTags Execution Time');
    } catch (error) {
      console.timeEnd('Old ClearCacheByTags Execution Time');
      this.logger.error(`Failed to clear cache for tags: ${tags.join(', ')}. Error: ${error.message}`);
      throw error;
    }
  }
}