interface ExtraParameters {
  utm_campaign: string;
  click_url: string;
  click_user_agent: string;
  at_unique_id: string;
  utm_tool: string;
  utm_source: string;
}

interface Extra {
  parameters: ExtraParameters;
  device_model: string;
  device_family: string;
  device_brand: string;
  device_type: string;
  device: string;
  os: string;
  browser: string;
}
// https://developers.accesstrade.vn/api-publisher-vietnamese/lay-danh-sach-giao-dich
interface TransactionData {
  merchant: string;
  status: number;
  update_time: string;
  click_url: string;
  conversion_platform: string;
  utm_campaign: string;
  product_category: string;
  utm_content: string;
  transaction_time: string;
  product_image: string;
  utm_source: string;
  transaction_value: number;
  _extra: Extra;
  reason_rejected: string;
  category_name: string;
  utm_term: string;
  product_id: string;
  is_confirmed: number;
  confirmed_time: string;
  product_price: number;
  id: string;
  commission: number;
  customer_type: string;
  conversion_id: number;
  utm_medium: string;
  product_quantity: number;
  click_time: string;
  product_name: string;
  transaction_id: string;
}
// https://developers.accesstrade.vn/api-publisher-vietnamese/lay-danh-sach-don-hang-v2
export interface OrderData {
  at_product_link: string;
  billing: number;
  browser: string;
  category_name: string;
  click_time: string;
  client_platform: string;
  confirmed_time: string;
  conversion_platform: string | null;
  customer_type: string | null;
  is_confirmed: number;
  landing_page: string;
  merchant: string;
  order_id: string;
  order_pending: number;
  order_reject: number;
  order_approved: number;
  product_category: string;
  products_count: number;
  pub_commission: number;
  sales_time: string;
  update_time: string;
  utm_campaign: string | null;
  utm_content: string | null;
  utm_medium: string | null;
  utm_source: string;
  website: string;
  website_url: string;
}

interface At {
  banner_id: number;
  commission_type: number;
  goods_id: string;
  result_id: number;
  reward_type: number;
  seq_no: number;
  vn_click_id: string;
}

interface ExtraParameters {
  at_unique_id: string;
  click_url: string;
  click_user_agent: string;
  utm_campaign: string;
  utm_source: string;
  utm_tool: string;
}

interface Extra {
  browser: string;
  device: string;
  device_brand: string;
  device_family: string;
  device_model: string;
  device_type: string;
  os: string;
  parameters: ExtraParameters;
}

interface Billing {
  approved: number;
  pending: number;
  reject: number;
}

interface Commission {
  approved: number;
  pending: number;
  reject: number;
}

interface Quantity {
  approved: number;
  pending: number;
  reject: number;
}

// https://developers.accesstrade.vn/api-publisher-vietnamese/lay-thong-tin-san-pham-cua-don-hang
export interface OrderProductData {
  _at: At;
  _extra: Extra;
  _id: string;
  billing: Billing;
  campaign_id: string;
  click_time: string;
  commission: Commission;
  confirmed_time: string;
  merchant: string;
  product_price: number;
  product_quantity: number;
  quantity: Quantity;
  reason_rejected: string;
  sales_time: string;
}

// https://developers.accesstrade.vn/api-publisher-vietnamese/lay-thong-tin-chi-tiet-san-pham
export interface OrderProductDetailData {
  name: string;
  price: number;
  short_desc: string;
  discount: number;
  link: string;
  image: string;
  desc: string;
  category_id: string;
  brand: string;
  category_name: string;
}