import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ConfigService } from '@nestjs/config'; // Sử dụng để lấy token từ config
import {
  GetCampaignsDto,
  GetOrderListDto,
  GetOrderProductsDto,
  GetProductDetailDto,
  GetTransactionsDto
} from "./accesstrade-api.dto";
import { ITransaction } from "../common/core/interfaces";

@Injectable()
export class AccessTradeApiService {
  private readonly logger = new Logger(AccessTradeApiService.name);
  private readonly axiosInstance: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
  ) {
    const baseURL = this.configService.get<string>('accesstrade.api_url'); // Lấy baseURL từ config
    const token = this.configService.get<string>('accesstrade.api_key'); // L<PERSON><PERSON> token từ config

    // Thiết lập axios instance với baseURL và token
    this.axiosInstance = axios.create({
      baseURL: baseURL,
      headers: {
        Authorization: `Token ${token}`,
      },
    });
  }

  async getTransactions(params: GetTransactionsDto): Promise<{ data: ITransaction[], total: number }> {
    const queryParams = new URLSearchParams();

    queryParams.append('since', params.since);
    queryParams.append('until', params.until);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.offset) queryParams.append('offset', params.offset.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.merchant) queryParams.append('merchant', params.merchant);
    if (params.utm_source) queryParams.append('utm_source', params.utm_source);
    if (params.utm_campaign) queryParams.append('utm_campaign', params.utm_campaign);
    if (params.utm_medium) queryParams.append('utm_medium', params.utm_medium);
    if (params.utm_content) queryParams.append('utm_content', params.utm_content);
    if (params.status !== undefined) queryParams.append('status', params.status.toString());
    if (params.is_confirmed !== undefined) queryParams.append('is_confirmed', params.is_confirmed.toString());
    if (params.transaction_id) queryParams.append('transaction_id', params.transaction_id.join(','));
    if (params.update_time_start) queryParams.append('update_time_start', params.update_time_start);
    if (params.update_time_end) queryParams.append('update_time_end', params.update_time_end);

    const url = `/v1/transactions?${queryParams.toString()}`;

    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  async getOrderList(params: GetOrderListDto) {
    const queryParams = new URLSearchParams();

    queryParams.append('since', params.since);
    queryParams.append('until', params.until);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.utm_source) queryParams.append('utm_source', params.utm_source);
    if (params.utm_campaign) queryParams.append('utm_campaign', params.utm_campaign);
    if (params.utm_medium) queryParams.append('utm_medium', params.utm_medium);
    if (params.utm_content) queryParams.append('utm_content', params.utm_content);
    if (params.status !== undefined) queryParams.append('status', params.status.toString());
    if (params.merchant) queryParams.append('merchant', params.merchant);

    const url = `/v1/order-list?${queryParams.toString()}`;

    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  async getOrderProducts(params: GetOrderProductsDto) {
    const queryParams = new URLSearchParams();

    queryParams.append('order_id', params.order_id);
    queryParams.append('merchant', params.merchant);
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    const url = `/v1/order-products?${queryParams.toString()}`;

    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  async getProductDetail(params: GetProductDetailDto) {
    const queryParams = new URLSearchParams();

    queryParams.append('merchant', params.merchant);
    queryParams.append('product_id', params.product_id);

    const url = `/v1/product_detail?${queryParams.toString()}`;

    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  async getCampaigns(params: GetCampaignsDto) {
    const queryParams = new URLSearchParams();

    queryParams.append('approval', params.approval);
    queryParams.append('campaign_id', params.campaign_id);
    if (params.merchant) queryParams.append('merchant', params.merchant);

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());

    const url = `/v1/campaigns?${queryParams.toString()}`;

    const response = await this.axiosInstance.get(url);
    return response.data;
  }
}
