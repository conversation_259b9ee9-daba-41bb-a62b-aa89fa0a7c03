import { Controller, Get, Query } from '@nestjs/common';
import { ApiExcludeController, ApiResponse } from '@nestjs/swagger';
import { AccessTradeApiService } from './accesstrade-api.service';
import { GetOrderListDto, GetOrderProductsDto, GetProductDetailDto, GetTransactionsDto } from './accesstrade-api.dto';

@Controller('/cron-data')
@ApiExcludeController()
export class AccesstradeApiController {
  constructor(private readonly accessTradeApiService: AccessTradeApiService) {
  }

  //http://localhost:3003/api/v1/accesstrade/cron-data/orders?since=2024-09-26T00:00:00Z&until=2024-09-27T00:00:00Z
  @Get('orders')
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  async getOrders(@Query() query: GetOrderListDto) {
    const data = await this.accessTradeApiService.getOrderList(query);

    return { data };
  }

  //http://localhost:3003/api/v1/accesstrade/cron-data/transactions?since=2024-09-26T00:00:00Z&until=2024-09-27T00:00:00Z
  @Get('transactions')
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  async getTransactions(@Query() query: GetTransactionsDto) {
    const data = await this.accessTradeApiService.getTransactions(query);

    return { data };
  }

  //http://localhost:3003/api/v1/accesstrade/cron-data/transactions?since=2024-09-26T00:00:00Z&until=2024-09-27T00:00:00Z
  @Get('orders-products')
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  async getOrderProducts(@Query() query: GetOrderProductsDto) {
    const data = await this.accessTradeApiService.getOrderProducts(query);

    return { data };
  }

  @Get('products')
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  async getProductDetail(@Query() query: GetProductDetailDto) {
    const data = await this.accessTradeApiService.getProductDetail(query);

    return { data };
  }
}
