import { Test, TestingModule } from '@nestjs/testing';
import { AccesstradeService } from './accesstrade.service';

describe('AccesstradeService', () => {
  let service: AccesstradeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AccesstradeService],
    }).compile();

    service = module.get<AccesstradeService>(AccesstradeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
