import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { OrderStatusEnum } from '../../common/core/constants';
import { ICoreTransactionStatus } from '../dto/update-accesstrade-order.dto';

export type AccesstradeOrderDocument = AccesstradeOrder & Document;

@Schema({
  timestamps: true,
})
export class AccesstradeOrder {
  @Prop()
  orderId: string;

  @Prop()
  userId: string;

  @Prop()
  mobile: string;

  @Prop()
  merchantId: string;

  @Prop()
  merchantName: string;

  @Prop()
  storeCode: string;

  @Prop()
  storeName: string;

  @Prop()
  transactionIds: string[];

  @Prop({
    required: false,
    default: 0,
  })
  totalCommission: number;

  @Prop({
    required: false,
    default: 0,
  })
  totalAmount: number;

  @Prop({
    required: false,
    default: 0,
  })
  orderAmount: number;

  @Prop({
    required: false,
    default: 0,
  })
  firstAmount: number;

  @Prop({
    required: false,
    default: 0,
  })
  secondAmount: number;

  @Prop({
    required: false,
    default: 0,
  })
  totalVUI: number;

  @Prop({
    required: false,
    default: 0,
  })
  firstTimeVUI: number;

  @Prop({
    enum: OrderStatusEnum,
  })
  status: OrderStatusEnum;

  @Prop({ required: true })
  earnRate: number;

  @Prop({ required: true })
  earnCommissionRate: number;

  @Prop({ required: true })
  firstTimeIssuedRate: number;

  @Prop({ required: true })
  transactionDate: Date;

  @Prop({ required: false, type: Array, default: [] })
  coreTransactionIds: string[];

  @Prop({ required: false, type: Array, default: [] })
  coreTriggerIds: string[];

  @Prop({ required: false, type: Array, default: [] })
  coreTransactionStatus: ICoreTransactionStatus[];
}

export const AccesstradeOrderSchema = SchemaFactory.createForClass(AccesstradeOrder);
