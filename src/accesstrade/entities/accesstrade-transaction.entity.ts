import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AccesstradeTransactionDocument = AccesstradeTransaction & Document;

@Schema({
  timestamps: true,
})
export class AccesstradeTransaction {
  @Prop()
  transactionId: string;

  @Prop()
  orderId: string;

  @Prop()
  campaignId: string;

  @Prop()
  productId: string;

  @Prop()
  quantity: number;

  @Prop()
  productCategory: string;

  @Prop()
  productPrice: number;

  @Prop()
  reward: number;

  @Prop()
  salesTime: string;

  @Prop()
  browser: string;

  @Prop()
  conversionPlatform: string;

  @Prop()
  ip: string;

  @Prop()
  referrer: string;

  @Prop()
  clickTime: string;

  @Prop()
  status: number;

  @Prop()
  isConfirmed: number;

  @Prop()
  utmSource: string;

  @Prop()
  utmCampaign: string;

  @Prop()
  utmContent: string;

  @Prop()
  utmMedium: string;

  @Prop()
  customerType: string;

  @Prop({ default: '' })
  estimatedApprovalDate?: string;
}

export const AccesstradeTransactionSchema = SchemaFactory.createForClass(AccesstradeTransaction);
