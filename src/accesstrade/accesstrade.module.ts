import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { AffiliateConfigurationModule } from '../affiliate-configuration/affiliate-configuration.module';
import { BrandConfigurationModule } from '../brand-configuration/brand-configuration.module';
import { ConnectionNameEnum, QUEUE_PROCESSOR } from "../common/core/constants";
import { UserModule } from '../user/user.module';
import { AccesstradeService } from './accesstrade.service';
import { AccesstradeOrder, AccesstradeOrderSchema } from './entities/accesstrade-order.entity';
import { AccesstradeTransaction, AccesstradeTransactionSchema } from './entities/accesstrade-transaction.entity';
import { AccessTradeApiService } from './accesstrade-api.service';
import { AccesstradeApiController } from './accesstrade-api.controller';
import { CrawlDataModule } from 'src/crawl-data/crawl-data.module';
import { AccesstradeController } from "./accesstrade.controller";
import { PostbackService } from "./postback.service";
import { BullModule } from "@nestjs/bull";
import { CommonModule } from "../common/common.module";
import { AccessTradeFakeModule } from "../accesstrade-fake/accesstrade-fake.module";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: AccesstradeOrder.name,
          schema: AccesstradeOrderSchema,
          collection: 'accesstrade_orders',
        },
        {
          name: AccesstradeTransaction.name,
          schema: AccesstradeTransactionSchema,
          collection: 'accesstrade_transactions',
        },
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API,
      limiter: {
        max: 8,        // Maximum 8 jobs
        duration: 60000 // Per 60,000 milliseconds (1 minute)
      },
    }),
    UserModule,
    AffiliateConfigurationModule,
    forwardRef(() => CrawlDataModule),
    forwardRef(() => BrandConfigurationModule),
    forwardRef(() => AccessTradeFakeModule),
    CommonModule
  ],
  controllers: [AccesstradeApiController, AccesstradeController],
  providers: [AccesstradeService, AccessTradeApiService, PostbackService],
  exports: [AccesstradeService, AccessTradeApiService, PostbackService],
})
export class AccesstradeModule {}
