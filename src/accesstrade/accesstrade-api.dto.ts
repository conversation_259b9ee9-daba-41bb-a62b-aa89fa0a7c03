// src/transactions/dto/get-transactions.dto.ts

import { IsString, IsISO8601, IsOptional, IsInt, IsIn, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class GetTransactionsDto {
  @IsISO8601()
  readonly since: string; // Bắt buộc - Thời gian bắt đầu

  @IsISO8601()
  readonly until: string; // Bắt buộc - Thời gian kết thúc

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly page?: number; // Tùy chọn - Số trang

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly offset?: number; // Tùy chọn - Offset

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly limit?: number; // Tùy chọn - Giới hạn số kết quả

  @IsOptional()
  @IsString()
  readonly merchant?: string; // Tùy chọn - Tên merchant

  @IsOptional()
  @IsString()
  readonly utm_source?: string; // Tùy chọn - utm_source

  @IsOptional()
  @IsString()
  readonly utm_campaign?: string; // Tùy chọn - utm_campaign

  @IsOptional()
  @IsString()
  readonly utm_medium?: string; // Tùy chọn - utm_medium

  @IsOptional()
  @IsString()
  readonly utm_content?: string; // Tùy chọn - utm_content

  @IsOptional()
  @IsIn([0, 1, 2])
  @Type(() => Number)
  readonly status?: number; // Tùy chọn - Trạng thái giao dịch

  @IsOptional()
  @IsIn([0, 1])
  @Type(() => Number)
  readonly is_confirmed?: number; // Tùy chọn - Trạng thái duyệt

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  readonly transaction_id?: string[]; // Tùy chọn - Mã giao dịch (có thể nhiều mã)

  @IsOptional()
  @IsISO8601()
  readonly update_time_start?: string; // Tùy chọn - Thời gian bắt đầu cập nhật

  @IsOptional()
  @IsISO8601()
  readonly update_time_end?: string; // Tùy chọn - Thời gian kết thúc cập nhật
}

export class GetOrderListDto {
  @IsISO8601()
  readonly since: string; // Bắt buộc - Thời gian bắt đầu

  @IsISO8601()
  readonly until: string; // Bắt buộc - Thời gian kết thúc

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly page?: number; // Tùy chọn - Thứ tự page (mặc định là 1)

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly limit?: number; // Tùy chọn - Số lượng đơn hàng trả về mỗi page (mặc định là 30)

  @IsOptional()
  @IsString()
  readonly utm_source?: string; // Tùy chọn - utm_source (ví dụ: facebook)

  @IsOptional()
  @IsString()
  readonly utm_campaign?: string; // Tùy chọn - utm_campaign

  @IsOptional()
  @IsString()
  readonly utm_medium?: string; // Tùy chọn - utm_medium (ví dụ: email)

  @IsOptional()
  @IsString()
  readonly utm_content?: string; // Tùy chọn - utm_content

  @IsOptional()
  @IsIn([0, 1, 2])
  @Type(() => Number)
  readonly status?: number; // Tùy chọn - Trạng thái của conversion (0: Pending, 1: Approved, 2: Rejected)

  @IsOptional()
  @IsString()
  readonly merchant?: string; // Tùy chọn - Tên merchant (ví dụ: lazada)
}

export class GetOrderProductsDto {
  @IsString()
  readonly order_id: string; // Bắt buộc - Order ID của đơn hàng

  @IsString()
  readonly merchant: string; // Bắt buộc - Tên merchant (ví dụ: shopee_kolnew)

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly page?: number; // Tùy chọn - Thứ tự page, mặc định là 1

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly limit?: number; // Tùy chọn - Số lượng đơn hàng trả về mỗi page
}

export class GetProductDetailDto {
  @IsString()
  readonly merchant: string; // Bắt buộc - Tên merchant (ví dụ: shopee)

  @IsString()
  readonly product_id: string; // Bắt buộc - ID của sản phẩm
}

export class GetCampaignsDto {
  @IsString()
  @IsOptional()
  approval?: string;

  @IsString()
  @IsOptional()
  campaign_id?: string;

  @IsString()
  @IsOptional()
  merchant?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly page?: number; // Tùy chọn - Số trang

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  readonly limit?: number; // Tùy chọn - Số trang
}