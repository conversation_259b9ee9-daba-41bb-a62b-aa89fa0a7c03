import { ConfigService } from '@nestjs/config';
import { configStub } from './../stubs/config.stub';
import { Test, TestingModule } from '@nestjs/testing';
import { AccesstradeController } from './accesstrade.controller';
import { AccesstradeService } from './accesstrade.service';

describe('AccesstradeController', () => {
  let controller: AccesstradeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccesstradeController],
      providers: [
        AccesstradeService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    controller = module.get<AccesstradeController>(AccesstradeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
