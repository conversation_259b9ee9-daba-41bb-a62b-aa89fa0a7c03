import { ConnectionNameEnum } from 'src/common/core/constants';
import { UpdateAccesstradeOrderDto } from './dto/update-accesstrade-order.dto';
import { UpdateAccesstradeTransactionDto } from './dto/update-accesstrade-transaction.dto';
import { AccesstradeTransaction, AccesstradeTransactionDocument } from './entities/accesstrade-transaction.entity';
import { AccesstradeOrder, AccesstradeOrderDocument } from './entities/accesstrade-order.entity';
import { Model } from 'mongoose';
import { CreateAccesstradeOrderDto } from './dto/create-accesstrade-order.dto';
import { CreateAccesstradeTransactionDto } from './dto/create-accesstrade-transaction.dto';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FindAccesstradeOrderDto } from './dto/find-accesstrade-order.dto';
import { get } from 'lodash';
import utils from '../common/core/utils';
import { FindAccesstradeTransactionDto } from './dto/find-accesstrade-transaction.dto';
import * as moment from 'moment';

@Injectable()
export class AccesstradeService {
  private readonly logger = new Logger(AccesstradeService.name);
  constructor(
    @InjectModel(AccesstradeOrder.name, ConnectionNameEnum.AFFILIATE_V2) private accesstradeOrderModel: Model<AccesstradeOrderDocument>,
    @InjectModel(AccesstradeTransaction.name, ConnectionNameEnum.AFFILIATE_V2) private accesstradeTransactionModel: Model<AccesstradeTransactionDocument>,
  ) {}

  async findAllOrders(query: FindAccesstradeOrderDto, showTransaction: boolean = false) {
    try {
      const _size = Number(get(query, 'size', 10));
      const _page = Number(get(query, 'page', 1));
      const { limit, skip } = utils.sanitizePageSize(_page, _size);
      const { size, page, startDate, endDate, status, ...remain } = query;
      const _query = JSON.parse(JSON.stringify(remain));
      if (startDate || endDate) {
        const queryDate = {};
        if (startDate) queryDate['$gte'] = moment(startDate).startOf('days').toDate();
        if (endDate) queryDate['$lte'] = moment(endDate).endOf('days').toDate();
        _query['transactionDate'] = queryDate;
      }
      if (status) {
        _query['status'] = {
          $in: Array.isArray(status) ? status : [status],
        };
      }
      const [total, data] = await Promise.all([
        this.accesstradeOrderModel.count(_query),
        this.accesstradeOrderModel.find(_query).limit(limit).skip(skip).sort({ createdAt: -1 }).lean(),
      ]);
      let items = data;
      if (showTransaction) {
        items = await Promise.all(
          data.map(async (item) => {
            const transactions = await this.accesstradeTransactionModel.find({ orderId: item.orderId, size: 200 }).lean();
            item['transactionDetails'] = transactions.map((transaction) => ({
              transactionId: transaction.transactionId,
              orderId: transaction.orderId,
              productId: transaction.productId,
              productCategory: transaction.productCategory,
              productPrice: transaction.productPrice,
              reward: transaction.reward,
              estimatedApprovalDate: transaction.estimatedApprovalDate,
              salesTime: transaction.salesTime,
            }));
            return item;
          }),
        );
      }
      return {
        data: items,
        meta: {
          currentPage: +_page,
          pageSize: +_size,
          totalPages: Math.ceil(total / _size),
          totalRows: total,
        },
      };
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findAllTransactions(query: FindAccesstradeTransactionDto) {
    try {
      const size = Number(get(query, 'size', 10));
      const page = Number(get(query, 'page', 1));
      const { limit, skip } = utils.sanitizePageSize(page, size);
      const { orderId, transactionId } = query;
      const _query = JSON.parse(JSON.stringify({ orderId, transactionId }));
      const [total, data] = await Promise.all([
        this.accesstradeTransactionModel.count(_query),
        this.accesstradeTransactionModel.find(_query).limit(limit).skip(skip).sort({ createdAt: -1 }).lean(),
      ]);
      return {
        data,
        meta: {
          currentPage: +page,
          pageSize: +size,
          totalPages: Math.ceil(total / size),
          totalRows: total,
        },
      };
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findOrderById(orderId: string) {
    try {
      const result = await this.accesstradeOrderModel.findOne({ orderId }).lean();
      if (!result) {
        this.logger.error(`AccesstradeOrder not found with orderId: ${orderId}`);
        throw new HttpException(`AccesstradeOrder with orderId ${orderId} not found`, 404);
      }
      return result;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findTransactionById(transactionId: string) {
    try {
      const result = await this.accesstradeTransactionModel.findOne({ transactionId }).lean();
      if (!result) {
        this.logger.error(`AccesstradeTransaction not found with transactionId: ${transactionId}`);
        throw new HttpException(`AccesstradeTransaction with transactionId ${transactionId} not found`, 404);
      }
      return result;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  createOrder(createAccesstradeOrderDto: CreateAccesstradeOrderDto) {
    try {
      const order = new this.accesstradeOrderModel(createAccesstradeOrderDto);
      return order.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  createTransaction(createAccesstradeTransactionDto: CreateAccesstradeTransactionDto) {
    try {
      const transaction = new this.accesstradeTransactionModel(createAccesstradeTransactionDto);
      return transaction.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async updateOrder(orderId: string, updateAccesstradeOrderDto: UpdateAccesstradeOrderDto) {
    try {
      const existed = await this.accesstradeOrderModel.findOne({ orderId });
      if (!existed) {
        this.logger.error(`AccesstradeOrder not found with orderId: ${orderId}`);
        throw new HttpException(`AccesstradeOrder with orderId ${orderId} not found`, 404);
      }
      return this.accesstradeOrderModel.findOneAndUpdate({ orderId }, updateAccesstradeOrderDto, { new: true }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async updateTransaction(transactionId: string, updateAccesstradeTransactionDto: UpdateAccesstradeTransactionDto) {
    try {
      const existed = await this.accesstradeTransactionModel.findOne({ transactionId });
      if (!existed) {
        this.logger.error(`AccesstradeTransaction not found with transactionId: ${transactionId}`);
        throw new HttpException(`AccesstradeTransaction with transactionId ${transactionId} not found`, 404);
      }
      return this.accesstradeTransactionModel.findOneAndUpdate({ transactionId }, updateAccesstradeTransactionDto, { new: true }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }
}
