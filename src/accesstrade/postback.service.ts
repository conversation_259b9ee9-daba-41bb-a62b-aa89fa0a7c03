import { HttpException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateAccesstradeTransactionDto } from "./dto/create-accesstrade-transaction.dto";
import { plainToInstance } from "class-transformer";
import { InjectQueue } from "@nestjs/bull";
import { Queue } from "bull";
import { QUEUE_CONSUMER, QUEUE_PROCESSOR } from "../common/core/constants";

@Injectable()
export class PostbackService {
  private readonly logger = new Logger(PostbackService.name);
  constructor(
    @InjectQueue(QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API)
    private accessTradeCrawlAPIQueue: Queue,
    private readonly configService: ConfigService,
  ) { }

  async runPostBack(publicKey: string, query: CreateAccesstradeTransactionDto, method: string) {
    const atPublicKey: string = this.configService.get<string>('accesstrade.public_key');

    if (atPublicKey !== publicKey) {
      this.logger.error(`Invalid accessTrade public key: ${publicKey}`);
      throw new HttpException('Invalid accessTrade public key', 400);
    }

    const _query: CreateAccesstradeTransactionDto = plainToInstance(CreateAccesstradeTransactionDto, query);
    await this.accessTradeCrawlAPIQueue.add(QUEUE_CONSUMER.ACCESSTRADE_POSTBACK, {
      data: { publicKey: publicKey, query: _query },
    }, {
      priority: 1,
      removeOnComplete: true,
      removeOnFail: true,
    });

    this.logger.log(`Transaction enqueued for postback with method ${method}`);

    return {
      status: {
        success: true,
        code: 200,
        message: 'Postback enqueued successfully',
      },
    };
  }
}