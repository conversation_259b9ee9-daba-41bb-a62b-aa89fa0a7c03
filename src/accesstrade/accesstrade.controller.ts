import { Body, Controller, Get, HttpCode, Param, Post, Query } from '@nestjs/common';
import { ApiExcludeController, ApiResponse } from '@nestjs/swagger';
import { PostbackService } from "./postback.service";
import { CreateAccesstradeTransactionDto } from "./dto/create-accesstrade-transaction.dto";

@Controller()
@ApiExcludeController()
export class AccesstradeController {
  constructor(private readonly postbackService: PostbackService) {}

  @Get('public/postback/:publicKey')
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  getPostBack(@Param('publicKey') publicKey: string, @Query() query: CreateAccesstradeTransactionDto) {
    return this.postbackService.runPostBack(publicKey, query, 'get');
  }

  @Post('public/postback/:publicKey')
  @HttpCode(200)
  @ApiResponse({ status: 200, description: 'The record has been successfully save.' })
  postPostBack(@Param('publicKey') publicKey: string, @Body() data: CreateAccesstradeTransactionDto) {
    return this.postbackService.runPostBack(publicKey, data, 'post');
  }
}