import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ate, <PERSON><PERSON>num, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { OrderStatusEnum } from '../../common/core/constants';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAccesstradeOrderDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  storeCode: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  storeName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchantId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchantName: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  mobile: string;

  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  transactionIds: string[];

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  totalCommission?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  totalAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  orderAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  firstAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  secondAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  totalVUI?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsNotEmpty()
  firstTimeVUI?: number;

  @ApiProperty({ enum: OrderStatusEnum })
  @IsString()
  @IsNotEmpty()
  @IsEnum(OrderStatusEnum)
  status: OrderStatusEnum;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  earnRate: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  earnCommissionRate: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  firstTimeIssuedRate: number;

  @ApiPropertyOptional()
  @IsNotEmpty()
  @IsDate()
  transactionDate?: Date;

  @ApiPropertyOptional()
  @IsArray()
  coreTransactionIds?: string[];

  @ApiPropertyOptional()
  @IsArray()
  coreTriggerIds?: string[];
}
