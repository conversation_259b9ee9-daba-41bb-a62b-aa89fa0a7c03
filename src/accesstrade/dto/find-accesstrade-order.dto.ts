import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { OrderStatusEnum } from '../../common/core/constants';
import QueryCommonDto from '../../common/core/query';

export class FindAccesstradeOrderDto extends QueryCommonDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  orderId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({ enum: OrderStatusEnum })
  @IsOptional()
  @IsEnum(OrderStatusEnum)
  status?: OrderStatusEnum | OrderStatusEnum[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  mobile?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantId?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  startDate?: Date;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  endDate?: Date;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coreTransactionIds?: string[];

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coreTriggerIds?: string[];
}
