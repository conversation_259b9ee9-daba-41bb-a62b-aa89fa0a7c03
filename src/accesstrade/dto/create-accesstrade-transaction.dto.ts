import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAccesstradeTransactionDto {
  constructor(partial: Partial<CreateAccesstradeTransactionDto> = {}) {
    Object.assign(this, partial);
  }

  //    {transaction_id}	Mã unique trên hệ thống AccessTrade
  @ApiProperty()
  @Expose({ name: 'transaction_id' })
  @IsNotEmpty()
  @IsString()
  transactionId: string;

  //    {order_id}	Mã đơn hàng hiển thị trên trang pub
  @ApiProperty()
  @Expose({ name: 'order_id' })
  @IsNotEmpty()
  @IsString()
  orderId: string;

  //    {campaign_id}	ID của campaign trên hệ thống
  @ApiProperty()
  @Expose({ name: 'campaign_id' })
  @IsNotEmpty()
  @IsString()
  campaignId: string;

  //    {product_id}	Mã sản phẩm
  @ApiProperty()
  @Expose({ name: 'product_id' })
  @IsNotEmpty()
  @IsString()
  productId: string;

  //    {quantity}	Số lượng sản phẩm
  @ApiProperty()
  @Expose({ name: 'quantity' })
  @IsNumber()
  @Type(() => Number)
  quantity: number;

  //    {product_category}	Group commission của sản phẩm
  @ApiProperty()
  @Expose({ name: 'product_category' })
  @IsNotEmpty()
  @IsString()
  productCategory: string;

  //    {product_price}	Giá của một sản phẩm
  @ApiProperty()
  @Expose({ name: 'product_price' })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  productPrice: number;

  //    {reward}	Hoa hồng nhận được
  @ApiProperty()
  @Expose({ name: 'reward' })
  @IsNumber()
  @Type(() => Number)
  reward: number;

  //    {sales_time}	Thời gian phát sinh của đơn hàng
  @ApiProperty()
  @Expose({ name: 'sales_time' })
  @IsNotEmpty()
  @IsString()
  salesTime: string;

  //    {browser}	Trình duyệt sử dụng
  @ApiProperty()
  @Expose({ name: 'browser' })
  @IsNotEmpty()
  @IsString()
  browser: string;

  //    {conversion_platform}	Platform sử dụng
  @ApiProperty()
  @Expose({ name: 'conversion_platform' })
  @IsString()
  conversionPlatform: string;

  //    {ip}	IP phát sinh đơn hàng
  @ApiProperty()
  @Expose({ name: 'ip' })
  @IsNotEmpty()
  @IsString()
  ip: string;

  //    {referrer}	click_referrer
  @ApiProperty()
  @Expose({ name: 'referrer' })
  @IsString()
  referrer: string;

  //    {click_time}	Thời gian phát sinh click
  @ApiProperty()
  @Expose({ name: 'click_time' })
  @IsNotEmpty()
  @IsString()
  clickTime: string;

  //    {status}	Status của đơn hàng gồm 3 giá trị: 0: new, 1: approved, 2: rejected
  @ApiProperty()
  @Expose({ name: 'status' })
  @IsNumber()
  @Type(() => Number)
  status: number;

  //    {is_confirmed}	Đơn hàng khóa data và được thanh toán: 0: chưa đối soát, 1: đã đối soát
  @ApiProperty()
  @Expose({ name: 'is_confirmed' })
  @IsNumber()
  @Type(() => Number)
  isConfirmed: number;

  //    {utm_source}	Thông tin tùy biến pub truyền vào url trong param utm_source
  @ApiProperty()
  @Expose({ name: 'utm_source' })
  @IsString()
  utmSource: string;

  //    {utm_campaign}	Thông tin tùy biến pub truyền vào url trong param utm_campaign
  @ApiProperty()
  @Expose({ name: 'utm_campaign' })
  @IsString()
  utmCampaign: string;

  //    {utm_content}	Thông tin tùy biến pub truyền vào url trong param utm_content
  @ApiProperty()
  @Expose({ name: 'utm_content' })
  @IsString()
  utmContent: string;

  //    {utm_medium}	Thông tin tùy biến pub truyền vào url trong param utm_medium
  @ApiProperty()
  @Expose({ name: 'utm_medium' })
  @IsString()
  utmMedium: string;

  //    {customer_type}	Thuộc tính của khách hàng phụ thuộc theo campaigns
  @ApiProperty()
  @Expose({ name: 'customer_type' })
  @IsString()
  customerType: string;

  @ApiProperty()
  @Expose({ name: 'publisher_login_name' })
  @IsString()
  publisherLoginName: string;
}
