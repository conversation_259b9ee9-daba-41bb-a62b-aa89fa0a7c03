import { PartialType } from '@nestjs/swagger';
import { CreateAccesstradeOrderDto } from './create-accesstrade-order.dto';

export interface ICoreTransactionStatus {
  id?: string;
  success?: boolean;
  message?: string;
  code?: number;
  issueDate?: string;
  mobile?: string;
  billNumber?: string;
  number?: string;
  storeCode?: string;
  brandCode?: string;
  billAmount?: number;
}
export class UpdateAccesstradeOrderDto extends PartialType(CreateAccesstradeOrderDto) {
  coreTransactionStatus?: ICoreTransactionStatus[];
}
