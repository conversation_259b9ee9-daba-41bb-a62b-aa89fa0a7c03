import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, Matches } from 'class-validator';

export class CreateAffiliateConfigurationDto {
  @ApiProperty()
  @IsNotEmpty()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  activeConfiguration: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Matches(/(http)?s?:?(\/\/[^"']*\.(?:png|jpg|jpeg|gif|png|svg))/, {
    message: 'Affiliate banner must be a valid image URL',
  })
  banner?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  total1stTimeIssuedPerUser: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  termAndCondition: string;
}
