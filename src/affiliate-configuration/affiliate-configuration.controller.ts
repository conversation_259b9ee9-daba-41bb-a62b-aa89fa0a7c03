import { AccessTokenGuard } from './../common/guards/access-token.guard';
import { Controller, Get, Post, Body, Put, Param, Delete, UseGuards } from '@nestjs/common';
import { AffiliateConfigurationService } from './affiliate-configuration.service';
import { CreateAffiliateConfigurationDto } from './dto/create-affiliate-configuration.dto';
import { UpdateAffiliateConfigurationDto } from './dto/update-affiliate-configuration.dto';
import { ApiBadRequestResponse, ApiBearerAuth, ApiCreatedResponse, ApiForbiddenResponse, ApiNotFoundResponse, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Afiiliate Configuration')
@ApiBearerAuth()
@Controller()
@UseGuards(AccessTokenGuard)
export class AffiliateConfigurationController {
  constructor(private readonly affiliateConfigurationService: AffiliateConfigurationService) {}

  @Post()
  @ApiCreatedResponse({ description: 'The record has been successfully created.' })
  @ApiBadRequestResponse({ description: 'Affiliate Configuration already exists' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async create(@Body() createAffiliateConfigurationDto: CreateAffiliateConfigurationDto) {
    const affiliateConfiguration = await this.affiliateConfigurationService.create(createAffiliateConfigurationDto);
    return {
      data: affiliateConfiguration,
    };
  }

  @Get()
  @ApiResponse({ status: 200, description: 'The record has been successfully found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async findOne() {
    const affiliateConfiguration = await this.affiliateConfigurationService.findOne();
    return {
      data: affiliateConfiguration,
    };
  }

  @Put()
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async update(@Body() updateAffiliateConfigurationDto: UpdateAffiliateConfigurationDto) {
    const affiliateConfiguration = await this.affiliateConfigurationService.update(updateAffiliateConfigurationDto);
    return {
      data: affiliateConfiguration,
    };
  }
}
