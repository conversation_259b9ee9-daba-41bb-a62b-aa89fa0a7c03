import { configStub } from './../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateConfigurationService } from './affiliate-configuration.service';

jest.mock('./affiliate-configuration.service');

describe('AffiliateConfigurationService', () => {
  let service: AffiliateConfigurationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AffiliateConfigurationService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<AffiliateConfigurationService>(AffiliateConfigurationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
