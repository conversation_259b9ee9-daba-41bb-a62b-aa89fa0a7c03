import { AffiliateConfiguration, AffiliateConfigurationDocument } from './entities/affiliate-configuration.entity';
import { Injectable, Logger, HttpException } from '@nestjs/common';
import { CreateAffiliateConfigurationDto } from './dto/create-affiliate-configuration.dto';
import { UpdateAffiliateConfigurationDto } from './dto/update-affiliate-configuration.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { get } from 'lodash';
import { RedisService } from "../common/services/redis.service";
import { ConnectionNameEnum } from "../common/core/constants";

@Injectable()
export class AffiliateConfigurationService {
  private readonly logger = new Logger(AffiliateConfigurationService.name);

  constructor(
    @InjectModel(AffiliateConfiguration.name, ConnectionNameEnum.AFFILIATE_V2)
    private affiliateConfigurationModel: Model<AffiliateConfigurationDocument>,
    private readonly redisService: RedisService,
  ) {}

  async init() {
    try {
      const affiliateConfiguration = await this.affiliateConfigurationModel.findOne().lean();
      if (!affiliateConfiguration) {
        const initAffiliateConfiguration: CreateAffiliateConfigurationDto = {
          activeConfiguration: false,
          total1stTimeIssuedPerUser: 25,
          termAndCondition: 'This is the default term and condition',
        };
        const createAffiliateConfiguration = new this.affiliateConfigurationModel(initAffiliateConfiguration);
        return createAffiliateConfiguration.save();
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async create(createAffiliateConfigurationDto: CreateAffiliateConfigurationDto) {
    try {
      const affiliateConfiguration = await this.affiliateConfigurationModel.find().lean();
      if (affiliateConfiguration.length > 0) {
        this.logger.error('AffiliateConfiguration already exists. Only one affiliateConfiguration is allowed');
        return affiliateConfiguration[0];
      }
      const createAffiliateConfiguration = new this.affiliateConfigurationModel(createAffiliateConfigurationDto);
      await this.redisService.clearCacheByTags(...[AffiliateConfiguration.name]);
      return createAffiliateConfiguration.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  findAll() {
    try {
      return this.affiliateConfigurationModel.find().lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findOne() {
    try {
      const affiliateConfiguration = await this.affiliateConfigurationModel.findOne().lean();
      if (!affiliateConfiguration) {
        this.logger.error(`AffiliateConfiguration not found`);
        throw new HttpException(`AffiliateConfiguration not found`, 404);
      }
      return affiliateConfiguration;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async update(updateAffiliateConfigurationDto: UpdateAffiliateConfigurationDto) {
    try {
      const existed = await this.affiliateConfigurationModel.findOne();
      if (!existed) {
        this.logger.error(`AffiliateConfiguration not found`);
        throw new HttpException(`AffiliateConfiguration not found`, 404);
      }
      const updated = await this.affiliateConfigurationModel.findOneAndUpdate({}, updateAffiliateConfigurationDto, { new: true }).lean();
      await this.redisService.clearCacheByTags(...[AffiliateConfiguration.name]);
      return updated;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }
}
