import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AffiliateConfigurationDocument = AffiliateConfiguration & Document;

@Schema()
export class AffiliateConfiguration {
  @Prop({ required: true, default: true })
  activeConfiguration: boolean;

  @Prop({ required: false, default: '' })
  banner: string;

  @Prop({ required: true, default: 0 })
  total1stTimeIssuedPerUser: number;

  @Prop({ required: true, default: '' })
  termAndCondition: string;
}

export const AffiliateConfigurationSchema = SchemaFactory.createForClass(AffiliateConfiguration);
