import { configStub } from './../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { CreateAffiliateConfigurationDto } from './dto/create-affiliate-configuration.dto';
import { affiliateConfigurationStub } from './../stubs/affiliate-configuration.stub';
import { AffiliateConfiguration } from './entities/affiliate-configuration.entity';
import { Test, TestingModule } from '@nestjs/testing';
import { AffiliateConfigurationController } from './affiliate-configuration.controller';
import { AffiliateConfigurationService } from './affiliate-configuration.service';

jest.mock('./affiliate-configuration.service');

describe('AffiliateConfigurationController', () => {
  let controller: AffiliateConfigurationController;
  let service: AffiliateConfigurationService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [AffiliateConfigurationController],
      providers: [
        AffiliateConfigurationService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    controller = moduleRef.get<AffiliateConfigurationController>(AffiliateConfigurationController);
    service = moduleRef.get<AffiliateConfigurationService>(AffiliateConfigurationService);
    jest.clearAllMocks();
  });

  describe('POST create affiliate configuration', () => {
    describe('when create affiliate configuration is successful', () => {
      let affiliateConfiguration: AffiliateConfiguration;
      let affiliateConfigurationDto: CreateAffiliateConfigurationDto;

      beforeEach(async () => {
        affiliateConfigurationDto = {
          activeConfiguration: affiliateConfigurationStub().activeConfiguration,
          banner: affiliateConfigurationStub().banner,
          total1stTimeIssuedPerUser: affiliateConfigurationStub().total1stTimeIssuedPerUser,
        };
        affiliateConfiguration = await controller.create(affiliateConfigurationDto);
      });
      it('should call the affiliateConfigurationService', async () => {
        expect(service.create).toHaveBeenCalledWith(affiliateConfigurationDto);
      });
      it('should return the affiliateConfiguration', async () => {
        expect(affiliateConfiguration).toEqual(affiliateConfigurationStub());
      });
    });
  });

  describe('GET all affiliate configuration', () => {
    describe('when get all affiliate configuration is successful', () => {
      let affiliateConfigurations: AffiliateConfiguration[];
      4;
      beforeEach(async () => {
        affiliateConfigurations = await controller.findAll();
      });
      it('should call the affiliateConfigurationService', async () => {
        expect(service.findAll).toHaveBeenCalledWith();
      });
      it('should return the list of affiliateConfiguration', async () => {
        expect(affiliateConfigurations).toEqual([affiliateConfigurationStub()]);
      });
    });
  });
});
