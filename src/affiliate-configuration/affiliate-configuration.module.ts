import { AffiliateConfiguration, AffiliateConfigurationSchema } from './entities/affiliate-configuration.entity';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import { AffiliateConfigurationService } from './affiliate-configuration.service';
import { AffiliateConfigurationController } from './affiliate-configuration.controller';
import { ConnectionNameEnum } from 'src/common/core/constants';
import { CommonModule } from "../common/common.module";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: AffiliateConfiguration.name,
          schema: AffiliateConfigurationSchema,
          collection: 'affiliate_configuration',
        },
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    CommonModule
  ],
  controllers: [AffiliateConfigurationController],
  providers: [AffiliateConfigurationService],
  exports: [AffiliateConfigurationService],
})
export class AffiliateConfigurationModule {}
