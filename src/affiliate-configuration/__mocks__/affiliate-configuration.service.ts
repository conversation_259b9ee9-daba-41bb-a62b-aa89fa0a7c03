import { affiliateConfigurationStub } from './../../stubs/affiliate-configuration.stub';
export const AffiliateConfigurationService = jest.fn().mockReturnValue({
  create: jest.fn().mockResolvedValue(affiliateConfigurationStub()),
  findAll: jest.fn().mockResolvedValue([affiliateConfigurationStub()]),
  findOne: jest.fn().mockResolvedValue(affiliateConfigurationStub()),
  update: jest.fn().mockResolvedValue(affiliateConfigurationStub()),
});
