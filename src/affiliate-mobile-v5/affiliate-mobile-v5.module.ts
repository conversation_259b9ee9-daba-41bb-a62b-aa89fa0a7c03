import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserModule } from 'src/user/user.module';

import { AffiliateConfigurationModule } from '../affiliate-configuration/affiliate-configuration.module';
import { BrandConfigurationModule } from '../brand-configuration/brand-configuration.module';
import { AffiliateMobileV2Controller } from './affiliate-mobile-v5.controller';
import { AffiliateMobileV5Service } from './affiliate-mobile-v5.service';
import { CommonModule } from '../common/common.module';
import { TransactionV2Module } from '../transaction-v2/transaction.module';
import { AffiliateOrder, AffiliateOrderSchema } from './entities/affiliate-order.entity';
import { ConnectionNameEnum } from '../common/core/constants';
import { CommissionPolicyModule } from '../commission-policy/commission-policy.module';
import { TransactionLog, TransactionLogSchema } from '../transaction-v2/entities/transaction-log.entity';

@Module({
  controllers: [AffiliateMobileV2Controller],
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: AffiliateOrder.name,
          schema: AffiliateOrderSchema,
          collection: 'affiliate_orders',
        },
        {
          name: TransactionLog.name,
          schema: TransactionLogSchema,
          collection: 'transaction_logs',
        },
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    BrandConfigurationModule,
    AffiliateConfigurationModule,
    UserModule,
    CommonModule,
    TransactionV2Module,
    CommissionPolicyModule,
  ],
  providers: [AffiliateMobileV5Service],
  exports: [AffiliateMobileV5Service],
})
export class AffiliateMobileV5Module {}
