import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema({
  timestamps: true,
})
export class AffiliateTransaction {
  @Prop({ type: String, required: true, unique: true })
  transactionId: string;

  @Prop({ type: Number, required: true })
  commission: number;

  @Prop({ type: Number, required: true })
  orderAmount: number;

  @Prop({ type: Number, required: true, default: 0 })
  totalVUI: number;

  @Prop({ type: Number, required: false, default: 0 })
  firstAmount: number;

  @Prop({ type: Number, required: false, default: 0 })
  secondAmount: number;

  @Prop({ type: String, required: false, default: null })
  firstAmountStatus: string;

  @Prop({ type: String, required: false, default: null })
  secondAmountStatus: string;

  @Prop({ type: String, enum: ['PENDING', 'TEMP_APPROVED', 'APPROVED', 'CANCELLED', 'REFUNDED'], required: true })
  status: string;

  @Prop({ type: Date, required: true })
  transactionDate: Date;
}

export const AffiliateTransactionSchema = SchemaFactory.createForClass(AffiliateTransaction);
