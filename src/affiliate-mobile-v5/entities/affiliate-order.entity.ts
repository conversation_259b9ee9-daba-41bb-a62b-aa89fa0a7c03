import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types, Document } from 'mongoose';
import { AffiliateTransaction, AffiliateTransactionSchema } from './affiliate-transaction.entity';

export type AffiliateOrderDocument = AffiliateOrder & Document;

@Schema({
  timestamps: true,
})
export class AffiliateOrder {
  @Prop({ type: String, required: true, index: true })
  orderId: string;

  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, required: true })
  merchantId: string;

  @Prop({ type: String, required: true })
  merchantName: string;

  @Prop({ type: String, required: true })
  merchantLogo: string;

  @Prop({ type: Number, required: true, default: 0 })
  totalCommission: number;

  @Prop({ type: Number, required: true, default: 0 })
  totalOrderAmount: number;

  @Prop({ type: Number, required: true, default: 0 })
  totalVUI: number;

  @Prop({ type: String, enum: ['TEMP_APPROVED', 'APPROVED'], required: false, default: 'TEMP_APPROVED' })
  status: string;

  @Prop({ type: [AffiliateTransactionSchema], default: [] })
  transactions: Types.DocumentArray<AffiliateTransaction>;

  @Prop({ type: Date, required: true })
  orderDate: Date;
}

export const AffiliateOrderSchema = SchemaFactory.createForClass(AffiliateOrder);

AffiliateOrderSchema.index({ orderId: 1 });
