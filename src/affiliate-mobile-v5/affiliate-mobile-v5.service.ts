import { get } from 'lodash';

import { HttpException, Injectable, Logger, NotFoundException } from '@nestjs/common';

import { AffiliateConfigurationService } from '../affiliate-configuration/affiliate-configuration.service';
import { BrandConfigurationService } from '../brand-configuration/brand-configuration.service';
import { GetAffiliateConfigurationMobileDto } from './dto/get-affiliate-configuration-mobile.dto';
import { GetBrandConfigurationMobileDto } from './dto/get-brand-configuration-mobile.dto';
import { RedisService } from '../common/services/redis.service';
import { CACHE_TIME, ConnectionNameEnum } from '../common/core/constants';
import { BrandConfiguration } from '../brand-configuration/entities/brand-configuration.entity';
import { AffiliateConfiguration } from '../affiliate-configuration/entities/affiliate-configuration.entity';
import { FindBrands } from './dto/find-brand-configuration-mobile.dto';
import { TransactionMobileService } from '../transaction-v2/transaction-mobile.service';
import { GetTransactionsDto } from './dto/get-transactions.dto';
import { Transaction } from '../transaction-v2/entities/transaction.entity';
import { CreateAffiliateOrderDto } from './dto/create-affiliate-order-mobile.dto';
import { InjectModel } from '@nestjs/mongoose';
import { AffiliateOrder, AffiliateOrderDocument } from './entities/affiliate-order.entity';
import { Model, SortOrder, Types } from 'mongoose';
import { UpdateAffiliateOrderDto } from './dto/update-affiliate-order-mobile.dto';
import { GetAffiliateOrderDto } from './dto/find-affiliate-order-mobile.dto';
import * as moment from 'moment/moment';
import { CommissionPolicyService } from '../commission-policy/commission-policy.service';
// Removed import: AffiliateTransactionStatusHelper - no longer needed
import { ConsoleLogger } from '../common/helpers/console-logger.helper';
import { TransactionLog } from '../transaction-v2/entities/transaction-log.entity';

@Injectable()
export class AffiliateMobileV5Service {
  private readonly logger = new Logger(AffiliateMobileV5Service.name);

  constructor(
    @InjectModel(AffiliateOrder.name, ConnectionNameEnum.AFFILIATE_V2)
    private affiliateOrderModel: Model<AffiliateOrderDocument>,
    @InjectModel(TransactionLog.name, ConnectionNameEnum.AFFILIATE_V2)
    private transactionLogModel: Model<TransactionLog>,
    private readonly brandConfigurationService: BrandConfigurationService,
    private readonly affiliateConfigurationService: AffiliateConfigurationService,
    private readonly redisService: RedisService,
    private readonly transactionMobileService: TransactionMobileService,
    private readonly commissionPolicyService: CommissionPolicyService,
  ) {}

  async getBrands(query: FindBrands) {
    const affiliateConfiguration = await this.getGlobalConfig();

    if (!affiliateConfiguration.activeConfiguration) {
      this.logger.error(`Service is turned off`);
      throw new HttpException(`Service is turned off`, 503);
    }

    const brands = await this.brandConfigurationService.getListActive(query);

    const data: GetBrandConfigurationMobileDto[] = await Promise.all(
      brands.map(async (brand) => {
        const url = await this.encodeMobileToLink(query.userId, brand.affiliateLink);
        return {
          _id: brand._id,
          index: brand.index,
          brandCode: brand.merchantId,
          icon: brand.iconImageLink,
          name: brand.merchantName,
          // desc: '',
          earnRate: brand.earnRate,
          earnRateType: 0, // 1 = upto, 1 = fixed
          earnType: brand.earnType,
          earnValue: brand.earnAmount,
          url,
          // termOfUse: '',
          // guide: '',
          // isEnabled: brand.isEnabled,
        };
      }),
    );
    return { data };
  }

  async getConfig() {
    try {
      const affiliateConfiguration = await this.getGlobalConfig();

      const configForMobile: GetAffiliateConfigurationMobileDto = {
        banner: affiliateConfiguration.banner,
        maxUserCredit: affiliateConfiguration.total1stTimeIssuedPerUser,
        status: affiliateConfiguration.activeConfiguration ? 1 : 0,
        updatedAt: null,
      };
      const response = {
        data: configForMobile,
      };

      return response;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async encodeMobileToLink(userId: string, affiliateLink: string) {
    const updatedUrl = affiliateLink.replace('encode_mobile', userId);
    return updatedUrl;
  }

  async getGlobalConfig() {
    return await this.redisService.cacheWithTags([AffiliateConfiguration.name], `${AffiliateConfiguration.name}`, CACHE_TIME.THIRTY_MINUTES, async () => {
      return await this.affiliateConfigurationService.findOne();
    });
  }

  async getTransactions(query: GetTransactionsDto) {
    return await this.redisService.cacheWithTags([Transaction.name], `${AffiliateConfiguration.name}`, CACHE_TIME.THIRTY_MINUTES, async () => {
      return await this.transactionMobileService.getTransactions(query);
    });
  }

  async getAffiliateOrderById(id: string): Promise<any> {
    return await this.redisService.cacheWithTags([Transaction.name], `${AffiliateConfiguration.name}:${id}`, CACHE_TIME.THIRTY_MINUTES, async () => {
      return await this.affiliateOrderModel.findById(id).lean();
    });
  }

  async getOrderHistories(params: GetAffiliateOrderDto) {
    let sort: Record<string, SortOrder> = { orderDate: -1, _id: -1 };
    let limit = params.limit ?? 10;

    const { after, before, startDate, endDate, ...restData } = params;

    const _query = JSON.parse(JSON.stringify(restData));

    if (after) {
      const afterRecord = await this.getAffiliateOrderById(after);
      if (afterRecord) {
        _query.$or = [
          { orderDate: { $lt: afterRecord.orderDate } },
          {
            orderDate: afterRecord.orderDate,
            _id: { $gt: after },
          },
        ];
      }
    } else if (before) {
      const beforeRecord = await this.getAffiliateOrderById(before);
      if (beforeRecord) {
        _query.$or = [
          { orderDate: { $gt: beforeRecord.orderDate } },
          {
            orderDate: beforeRecord.orderDate,
            _id: { $lt: before },
          },
        ];
      }
      sort = { orderDate: 1, _id: 1 };
    }

    if (startDate || endDate) {
      const queryDate = {};
      if (startDate) queryDate['$gte'] = moment(startDate).startOf('days').toDate();
      if (endDate) queryDate['$lte'] = moment(endDate).endOf('days').toDate();
      _query['orderDate'] = queryDate;
    }

    let [data, total] = await Promise.all([
      await this.affiliateOrderModel.find(_query).sort(sort).limit(limit).exec(),
      this.affiliateOrderModel.countDocuments(_query),
    ]);

    if (params?.before) {
      data = data.reverse();
    }

    const previousCursor = data.length ? data[0]._id : null;
    const nextCursor = data.length >= limit ? data[data.length - 1]._id : null;

    return {
      data: data,
      meta: {
        cursors: {
          before: previousCursor && _query?._id ? previousCursor.toString() : null,
          after: nextCursor ? nextCursor.toString() : null,
        },
        total,
      },
    };
  }

  async getOrderHistory(orderId: string, query: GetAffiliateOrderDto) {
    const orderDetail = await this.affiliateOrderModel.findOne({ orderId, userId: query.userId }).populate('transactions').lean();

    if (!orderDetail) {
      throw new NotFoundException(`AffiliateOrder with orderId ${orderId} not found`);
    }

    if (orderDetail.totalOrderAmount) {
      orderDetail.totalOrderAmount = Math.round(orderDetail.totalOrderAmount);
    }

    return orderDetail;
  }

  async getBrandConfigDetail(merchantId: string, query: FindBrands) {
    const [brandConfig, affiliateConfiguration, commissionPolicies] = await Promise.all([
      this.brandConfigurationService.findById(merchantId),
      this.getGlobalConfig(),
      this.commissionPolicyService.getCommissionPolicies(merchantId),
    ]);

    const url = await this.encodeMobileToLink(query.userId, brandConfig.affiliateLink);

    const formattedPolicies = commissionPolicies.map((policy) => ({
      _id: policy._id,
      name: policy.name,
      commission: policy.commission,
    }));

    return {
      ...brandConfig,
      url: url,
      earnValue: brandConfig.earnAmount,
      tnc: affiliateConfiguration.termAndCondition,
      commissionPolicies: formattedPolicies,
    };
  }

  async createAffiliateOrder(createAffiliateOrderDto: CreateAffiliateOrderDto): Promise<AffiliateOrder> {
    const { orderId, userId, transactions, ...orderData } = createAffiliateOrderDto;

    const existingOrder = await this.affiliateOrderModel.findOne({ userId, orderId });

    const calculateOrderStatus = (transactions: any[]): string => {
      return transactions.some((t) => t.status !== 'APPROVED') ? 'TEMP_APPROVED' : 'APPROVED';
    };

    if (existingOrder) {
      const newTransactions = transactions.filter((transaction) => !existingOrder.transactions.some((t) => t.transactionId === transaction.transactionId));

      if (newTransactions.length > 0) {
        existingOrder.transactions.push(...newTransactions);

        newTransactions.forEach((newTransaction) => {
          existingOrder.totalCommission += newTransaction.commission;
          existingOrder.totalOrderAmount += newTransaction.orderAmount;
          existingOrder.totalVUI += newTransaction.totalVUI;
        });

        existingOrder.status = calculateOrderStatus(existingOrder.transactions);

        await existingOrder.save();
      }

      return existingOrder;
    }

    const status = calculateOrderStatus(transactions);

    const createdOrder = new this.affiliateOrderModel({
      ...orderData,
      userId,
      orderId,
      transactions,
      status: status,
      totalCommission: transactions.reduce((sum, t) => sum + (t.commission || 0), 0),
      totalOrderAmount: transactions.reduce((sum, t) => sum + (t.orderAmount || 0), 0),
      totalVUI: transactions.reduce((sum, t) => sum + (t.totalVUI || 0), 0),
    });

    return createdOrder.save();
  }

  async updateAffiliateOrder(userId: string, orderId: string, updateData: UpdateAffiliateOrderDto): Promise<AffiliateOrder> {
    const existingOrder = await this.affiliateOrderModel.findOne({ userId, orderId });

    if (existingOrder) {
      if (updateData.transactions && updateData.transactions.length > 0) {
        updateData.transactions.forEach((newTransaction) => {
          const existingTransaction = existingOrder.transactions.find((t) => t.transactionId === newTransaction.transactionId);

          if (existingTransaction) {
            if (newTransaction.commission !== undefined) {
              existingOrder.totalCommission += newTransaction.commission - existingTransaction.commission;
              existingTransaction.commission = newTransaction.commission;
            }

            if (newTransaction.orderAmount !== undefined) {
              existingOrder.totalOrderAmount += newTransaction.orderAmount - existingTransaction.orderAmount;
              existingTransaction.orderAmount = newTransaction.orderAmount;
            }

            if (newTransaction.totalVUI !== undefined) {
              existingOrder.totalVUI += newTransaction.totalVUI - existingTransaction.totalVUI;
              existingTransaction.totalVUI = newTransaction.totalVUI;
            }

            Object.assign(existingTransaction, newTransaction);
          } else {
            existingOrder.totalCommission += newTransaction.commission ?? 0;
            existingOrder.totalOrderAmount += newTransaction.orderAmount ?? 0;
            existingOrder.totalVUI += newTransaction.totalVUI ?? 0;

            existingOrder.transactions.push(newTransaction);
          }
        });
      }

      existingOrder.totalVUI = existingOrder.transactions.reduce((total, transaction) => {
        return total + (transaction.totalVUI || 0);
      }, 0);

      Object.entries(updateData).forEach(([key, value]) => {
        if (value !== undefined && key !== 'transactions') {
          (existingOrder as any)[key] = value;
        }
      });

      existingOrder.status = existingOrder.transactions.some((t) => t.status !== 'APPROVED') ? 'TEMP_APPROVED' : 'APPROVED';

      return existingOrder.save();
    }
  }

  async deleteAffiliateOrder(payload: { transactionId: string; userId: string }) {
    const { transactionId, userId } = payload;

    const existingOrder: any = await this.affiliateOrderModel.findOne({ userId, 'transactions.transactionId': transactionId });

    if (!existingOrder) {
      this.logger.warn(`Affiliate Order not found for transactionId: ${transactionId} and userId: ${userId}`);
      return;
    }

    const updatedTransactions = existingOrder.transactions.filter((t) => t.transactionId !== transactionId);

    existingOrder.transactions = updatedTransactions;
    existingOrder.totalCommission = updatedTransactions.reduce((sum, t) => sum + t.commission, 0);
    existingOrder.totalOrderAmount = updatedTransactions.reduce((sum, t) => sum + t.orderAmount, 0);
    existingOrder.totalVUI = updatedTransactions.reduce((sum, t) => sum + t.totalVUI, 0);
    existingOrder.status = updatedTransactions.some((t) => t.status !== 'APPROVED') ? 'TEMP_APPROVED' : 'APPROVED';

    if (updatedTransactions.length === 0) {
      await this.affiliateOrderModel.deleteOne({ userId, orderId: existingOrder.orderId });
      this.logger.log(`Affiliate Order ${existingOrder.orderId} deleted as no transactions remain.`);
    } else {
      await existingOrder.save();
      this.logger.log(`Affiliate Order ${existingOrder.orderId} updated after transaction ${transactionId} removal.`);
    }
  }

  /**
   * Helper methods for calculating amount statuses have been removed
   */

  /**
   * Sync existing affiliate orders with new fields
   */
  async syncOrdersWithNewFields(): Promise<{ processed: number; errors: number }> {
    const consoleLogger = new ConsoleLogger(AffiliateMobileV5Service.name);

    consoleLogger.log('='.repeat(60));
    consoleLogger.log('🔄 STARTING BULK SYNC OF AFFILIATE ORDERS');
    consoleLogger.log('='.repeat(60));

    this.logger.log('='.repeat(60));
    this.logger.log('🔄 STARTING BULK SYNC OF AFFILIATE ORDERS');
    this.logger.log('='.repeat(60));

    let processedCount = 0;
    let errorCount = 0;
    const batchSize = 500; // Process in batches for better performance

    try {
      // Get total count first
      const totalCount = await this.affiliateOrderModel.countDocuments({});
      consoleLogger.log(`📊 Total affiliate orders in database: ${totalCount}`);
      this.logger.log(`📊 Total affiliate orders in database: ${totalCount}`);

      if (totalCount === 0) {
        consoleLogger.log('ℹ️  No affiliate orders found in database. Sync completed.');
        this.logger.log('ℹ️  No affiliate orders found in database. Sync completed.');
        return { processed: 0, errors: 0 };
      }

      consoleLogger.log(`⚙️  Processing in batches of ${batchSize} orders`);
      consoleLogger.log('🚀 Starting sync process...');
      this.logger.log(`⚙️  Processing in batches of ${batchSize} orders`);
      this.logger.log('🚀 Starting sync process...');

      // Process in batches
      let skip = 0;
      let hasMore = true;

      while (hasMore) {
        const orders = await this.affiliateOrderModel.find({}).skip(skip).limit(batchSize);

        if (orders.length === 0) {
          hasMore = false;
          break;
        }

        consoleLogger.log(`Processing batch: ${skip + 1} to ${skip + orders.length} of ${totalCount}`);
        this.logger.log(`Processing batch: ${skip + 1} to ${skip + orders.length} of ${totalCount}`);

        // Xử lý từng order riêng biệt
        for (const order of orders) {
          try {
            // Lấy danh sách transactionIds cho order hiện tại
            const orderTransactionIds = order.transactions.map((t) => t.transactionId);

            // Truy vấn transaction logs cho chỉ order hiện tại
            const orderTransactionLogs = await this.transactionLogModel
              .find({
                'transactionLogDetails.transactionId': { $in: orderTransactionIds },
              })
              .lean();

            console.log(`[Order ${order._id}] Found ${orderTransactionLogs.length} transaction log documents for ${orderTransactionIds.length} transactions`);

            // Tạo map cho order hiện tại
            const orderLogMap = new Map();
            orderTransactionLogs.forEach((logDoc) => {
              if (logDoc.transactionLogDetails && Array.isArray(logDoc.transactionLogDetails)) {
                logDoc.transactionLogDetails.forEach((logDetail) => {
                  if (orderTransactionIds.includes(logDetail.transactionId)) {
                    orderLogMap.set(logDetail.transactionId, logDetail);
                  }
                });
              }
            });

            console.log(`[Order ${order._id}] Extracted ${orderLogMap.size} transaction details from logs`);

            // Update only transactions that have log details - add the 4 new fields
            const fullOrder = await this.affiliateOrderModel.findById(order._id);

            if (fullOrder && fullOrder.transactions) {
              let updatedCount = 0;

              // Loop through each transaction in the database and update with log details
              for (const dbTransaction of fullOrder.transactions) {
                const logDetail = orderLogMap.get(dbTransaction.transactionId);

                if (logDetail) {
                  // Calculate new fields from log detail
                  const transactionFirstAmount = logDetail.firstTimeVUI || 0;
                  const transactionSecondAmount = logDetail.secondTimeVUI || 0;

                  let firstAmountStatus: string | null = null;
                  let secondAmountStatus: string | null = null;

                  // Determine status based on VUI amounts and issue details
                  if (logDetail.firstIssueDetails) {
                    firstAmountStatus = logDetail.firstIssueDetails.status === 'SUCCESS' ? 'SUCCESS' : 'FAILED';
                  } else if (transactionFirstAmount > 0) {
                    firstAmountStatus = 'SUCCESS';
                  }

                  if (logDetail.secondIssueDetails) {
                    secondAmountStatus = logDetail.secondIssueDetails.status === 'SUCCESS' ? 'SUCCESS' : 'FAILED';
                  } else if (transactionSecondAmount > 0) {
                    secondAmountStatus = 'SUCCESS';
                  }

                  // Add the 4 new fields to the transaction
                  (dbTransaction as any).firstAmount = transactionFirstAmount;
                  (dbTransaction as any).secondAmount = transactionSecondAmount;
                  (dbTransaction as any).firstAmountStatus = firstAmountStatus;
                  (dbTransaction as any).secondAmountStatus = secondAmountStatus;

                  updatedCount++;
                }
              }

              // Save the entire document after updating all transactions
              await fullOrder.save();
              console.log(`[Order ${order._id}] Updated ${updatedCount} transactions with new fields from log details`);
            } else {
              console.log(`[Order ${order._id}] Could not find order or transactions array is empty`);
            }

            processedCount++;

            if (processedCount % 100 === 0) {
              this.logger.log(`Processed ${processedCount} orders...`);
            }
          } catch (error) {
            this.logger.error(`Error processing order ${order._id}:`, error);
            errorCount++;
          }
        }

        skip += batchSize;

        // Small delay between batches to avoid overwhelming the database
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      consoleLogger.log('='.repeat(60));
      consoleLogger.log('✅ SYNC PROCESS COMPLETED SUCCESSFULLY!');
      consoleLogger.log(`📊 Total orders processed: ${processedCount}`);
      consoleLogger.log(`❌ Total errors encountered: ${errorCount}`);
      consoleLogger.log(`📈 Success rate: ${totalCount > 0 ? Math.round((processedCount / totalCount) * 100) : 0}%`);
      consoleLogger.log('='.repeat(60));

      this.logger.log('='.repeat(60));
      this.logger.log('✅ SYNC PROCESS COMPLETED SUCCESSFULLY!');
      this.logger.log(`📊 Total orders processed: ${processedCount}`);
      this.logger.log(`❌ Total errors encountered: ${errorCount}`);
      this.logger.log(`📈 Success rate: ${totalCount > 0 ? Math.round((processedCount / totalCount) * 100) : 0}%`);
      this.logger.log('='.repeat(60));

      return { processed: processedCount, errors: errorCount };
    } catch (error) {
      this.logger.error('Sync process failed:', error);
      throw error;
    }
  }
}
