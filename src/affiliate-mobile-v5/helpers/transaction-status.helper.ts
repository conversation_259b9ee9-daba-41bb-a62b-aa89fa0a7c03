import { TransactionIssueStatus } from '../../common/core/constants';

export class AffiliateTransactionStatusHelper {
  /**
   * Calculate first amount status based on transaction issue status
   */
  static calculateFirstAmountStatus(issueStatus: string): string | null {
    switch (issueStatus) {
      case TransactionIssueStatus.ISSUED_1ST:
      case TransactionIssueStatus.ISSUING_2ND:
      case TransactionIssueStatus.DONE:
      case TransactionIssueStatus.REFUNDING:
      case TransactionIssueStatus.REFUND_FAILED:
      case TransactionIssueStatus.ISSUE_FAILED_2ND:
        return 'SUCCESS';
      case TransactionIssueStatus.ISSUE_FAILED_1ST:
        return 'FAILED';
      default:
        return null;
    }
  }

  /**
   * Calculate second amount status based on transaction issue status
   */
  static calculateSecondAmountStatus(issueStatus: string): string | null {
    switch (issueStatus) {
      case TransactionIssueStatus.DONE:
        return 'SUCCESS';
      case TransactionIssueStatus.ISSUE_FAILED_2ND:
        return 'FAILED';
      default:
        return null;
    }
  }

  /**
   * Calculate both first and second amount status
   */
  static calculateAmountStatuses(issueStatus: string): { firstAmountStatus: string | null; secondAmountStatus: string | null } {
    return {
      firstAmountStatus: this.calculateFirstAmountStatus(issueStatus),
      secondAmountStatus: this.calculateSecondAmountStatus(issueStatus)
    };
  }
}
