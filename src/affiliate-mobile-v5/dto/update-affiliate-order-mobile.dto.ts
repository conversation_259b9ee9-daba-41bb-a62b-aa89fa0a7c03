import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON>ptional, IsArray, ValidateNested, IsEnum, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from "@nestjs/swagger";

export class UpdateAffiliateTransactionDto {
  @IsString()
  transactionId?: string;

  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  commission?: number;

  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  orderAmount?: number;

  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  totalVUI?: number;

  @IsString()
  @IsEnum(['PENDING', 'TEMP_APPROVED', 'APPROVED', 'CANCELLED', 'REFUNDED'])
  @IsOptional()
  status?: 'PENDING' | 'TEMP_APPROVED' | 'APPROVED' | 'CANCELLED' | 'REFUNDED';

  @IsOptional()
  transactionDate?: Date;
}

export class UpdateAffiliateOrderDto {
  @IsString()
  @IsOptional()
  merchantName?: string;

  @IsString()
  @IsOptional()
  merchantLogo?: string;

  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalCommission?: number;

  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalOrderAmount?: number;

  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalVUI?: number;

  @IsString()
  @IsEnum(['TEMP_APPROVED', 'APPROVED'])
  @IsOptional()
  status?: 'TEMP_APPROVED' | 'APPROVED';

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateAffiliateTransactionDto)
  @IsOptional()
  transactions?: UpdateAffiliateTransactionDto[];

  @IsOptional()
  orderDate?: Date;
}
