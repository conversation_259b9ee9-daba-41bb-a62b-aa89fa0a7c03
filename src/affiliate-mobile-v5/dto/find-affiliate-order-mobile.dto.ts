import { ApiProperty, ApiPropertyOptional, PartialType } from "@nestjs/swagger";
import { PaginationCursorQueryDto } from "../../common/core/pagination.dto";
import { IsNotEmpty, IsOptional, IsString, Matches, MaxLength } from "class-validator";

export class GetAffiliateOrderDto extends PartialType(PaginationCursorQueryDto) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  startDate?: Date | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  endDate?: Date | null;
}