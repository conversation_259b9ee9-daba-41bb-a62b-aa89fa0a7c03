import { IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { PaginationCursorQueryDto } from '../../common/core/pagination.dto';

export class GetTransactionsDto extends PartialType(PaginationCursorQueryDto) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  orderId?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  mobile?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  userId?: string | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  startDate?: Date | null;
}