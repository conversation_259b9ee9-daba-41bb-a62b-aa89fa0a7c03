import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, Matches } from 'class-validator';

export class GetBrandConfigurationMobileDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  brandCode: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Matches(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&=]*)/, {
    message: 'Affiliate link must be a valid URL',
  })
  url: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Matches(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&=]*)/, {
    message: 'Affiliate link must be a valid URL',
  })
  icon: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  index: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  earnRate: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  earnRateType?: number;

  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsString()
  // desc?: string;

  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsString()
  // termOfUse?: string;

  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsString()
  // guide?: string;

  // @ApiPropertyOptional()
  // @IsBoolean()
  // @IsOptional()
  // @Transform(({ value }) => {
  //   if (value === 'true') return true;
  //   if (value === 'false') return false;
  //   return value;
  // })
  // isEnabled: boolean;
}
