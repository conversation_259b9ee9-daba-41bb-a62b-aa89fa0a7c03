import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON>te<PERSON>ested, IsOptional, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class CreateAffiliateTransactionDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  transactionId?: string;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  commission?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  orderAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  totalVUI?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  firstAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(1000000000)
  @IsOptional()
  secondAmount?: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  firstAmountStatus?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  secondAmountStatus?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  status?: 'PENDING' | 'TEMP_APPROVED' | 'APPROVED' | 'CANCELLED' | 'REFUNDED';

  @ApiPropertyOptional()
  @IsOptional()
  transactionDate?: Date;
}

export class CreateAffiliateOrderDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  orderId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantName?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantLogo?: string;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalCommission?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalOrderAmount?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @Min(0)
  @Max(10000000000)
  @IsOptional()
  totalVUI?: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  status?: 'TEMP_APPROVED' | 'APPROVED';

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAffiliateTransactionDto)
  transactions: CreateAffiliateTransactionDto[];

  @ApiPropertyOptional()
  @IsOptional()
  orderDate?: Date;
}
