import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { ApiBearerAuth } from '@nestjs/swagger';
import { AffiliateMobileV5Service } from './affiliate-mobile-v5.service';
import { FindBrands } from './dto/find-brand-configuration-mobile.dto';
import { GetTransactionsDto } from './dto/get-transactions.dto';
import { GetAffiliateOrderDto } from "./dto/find-affiliate-order-mobile.dto";
import { IdTokenAuthenGuard } from "../common/guards/internalAuthen.guard";

@ApiBearerAuth()
@Controller()
@UseGuards(IdTokenAuthenGuard)
export class AffiliateMobileV2Controller {
  constructor(private readonly affiliateMobileService: AffiliateMobileV5Service) {
  }

  @Get('brand-config')
  getBrandConfig(@Query() query: FindBrands) {
    return this.affiliateMobileService.getBrands(query);
  }

  @Get('brand-config/:id')
  async getBrandConfigDetail(@Param('id') id: string, @Query() query: FindBrands) {
    const data = await this.affiliateMobileService.getBrandConfigDetail(id, query);

    return { data };
  }

  @Get('config')
  getConfig() {
    return this.affiliateMobileService.getConfig();
  }

  // @Get('content/withField')
  // getWithField(@Query() query: GetFieldByKeyDto) {
  //   return this.affiliateMobileService.getWithField(query);
  // }

  @Get('transactions')
  getTransactions(@Query() query: GetTransactionsDto) {
    return this.affiliateMobileService.getTransactions(query);
  }

  @Get('orders')
  async getOrders(@Query() query: GetAffiliateOrderDto) {
    return await this.affiliateMobileService.getOrderHistories(query);
  }

  @Get('orders/:id')
  async getOrderDetail(@Param('id') id: string, @Query() query: GetAffiliateOrderDto) {
    const data = await this.affiliateMobileService.getOrderHistory(id, query);

    return { data }
  }
}
