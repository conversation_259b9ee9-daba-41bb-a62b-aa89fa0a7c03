import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, Matches, Min } from "class-validator";

export class CreateBrandConfigurationDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toString())
  merchantId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.toString())
  storeCode: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toString())
  storeName?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toString())
  merchantName?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toString())
  campaignId?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Matches(/(http)?s?:?(\/\/[^"']*\.(?:png|jpg|jpeg|gif|png|svg))/, {
    message: 'Icon link must be a valid image URL',
  })
  @Transform(({ value }) => value?.toString())
  iconImageLink: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @Matches(/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&=]*)/, {
    message: 'Affiliate link must be a valid URL',
  })
  @Transform(({ value }) => value?.toString())
  affiliateLink: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  index: number;

  @ApiPropertyOptional()
  @IsOptional()
  @Transform(({ value }) => value?.toString())
  earnType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Min(0, { message: 'Earn amount must be a non-negative number' })
  @Transform(({ value }) => value ? parseFloat(value) : value)
  maxCommission?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Earn amount must be a non-negative number' })
  @Transform(({ value }) => parseFloat(value))
  earnAmount?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Earn commission rate must be a non-negative number' })
  @Transform(({ value }) => parseFloat(value))
  earnCommissionRate: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Earn first time issued rate must be a non-negative number' })
  @Transform(({ value }) => parseFloat(value))
  firstTimeIssuedRate: number;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isEnabled?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value
  })
  isSyncCampaign?: boolean;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value
  })
  isSyncCommission?: boolean;
}
