import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsNumber, IsString, Min } from "class-validator";

export class CalculateEarnDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  earnType: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Earn commission rate must be a non-negative number' })
  earnCommissionRate: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: 'Max commission must be a non-negative number' })
  maxCommission: number;
}