import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import QueryCommonDto from '../../common/core/query';

export class FindBrandConfigurationDto extends QueryCommonDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantId?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isEnabled?: boolean;
}
