import { CreateBrandConfigurationDto } from './create-brand-configuration.dto';
import { OmitType, PickType } from '@nestjs/mapped-types';
import { PartialType } from "@nestjs/swagger";

export class UpdateBrandConfigurationDto extends OmitType(PartialType(CreateBrandConfigurationDto), ['isEnabled'] as const) {}

export class UpdateStatusBrandConfigurationDto extends PickType(CreateBrandConfigurationDto, ['isEnabled'] as const) {}
