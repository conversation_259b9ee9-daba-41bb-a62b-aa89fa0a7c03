import { configStub } from './../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { BrandConfigurationService } from './brand-configuration.service';
import { Test, TestingModule } from '@nestjs/testing';
import { BrandConfigurationController } from './brand-configuration.controller';

jest.mock('./brand-configuration.service');

describe('BrandConfigurationController', () => {
  let controller: BrandConfigurationController;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [BrandConfigurationController],
      providers: [
        BrandConfigurationService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    controller = moduleRef.get<BrandConfigurationController>(BrandConfigurationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
