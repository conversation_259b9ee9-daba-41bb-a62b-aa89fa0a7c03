import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type BrandConfigurationDocument = BrandConfiguration & Document;

@Schema({
  timestamps: true,
})
export class BrandConfiguration {
  @Prop({ required: true, unique: true, index: true })
  merchantId: string;

  @Prop({ required: true, default: '' })
  merchantName: string;

  @Prop({ required: true, default: '' })
  storeCode: string;

  @Prop({ required: true, default: '' })
  storeName: string;

  @Prop({ required: false, default: '' })
  campaignId?: string;

  @Prop({ required: true })
  iconImageLink: string;

  @Prop({ required: true })
  affiliateLink: string;

  @Prop({ required: true })
  index: number;

  @Prop({ default: '' })
  earnType: string;

  @Prop({ required: false, default: 0 })
  earnRate: number;

  @Prop({ required: false })
  maxCommission: number;

  @Prop({ required: true })
  earnAmount: number;

  @Prop({ required: true })
  earnCommissionRate: number;

  @Prop({ required: true })
  firstTimeIssuedRate: number;

  @Prop({ required: false, default: '' })
  instruction: string;

  @Prop({ required: true, default: true })
  isEnabled: boolean;

  @Prop({ required: true, default: true })
  isSyncCampaign: boolean;

  @Prop({ required: true, default: true })
  isSyncCommission: boolean;
}

export const BrandConfigurationSchema = SchemaFactory.createForClass(BrandConfiguration);

BrandConfigurationSchema.index({ isEnabled: 1 })
BrandConfigurationSchema.index({ merchantId: 1 })
BrandConfigurationSchema.index({ createdAt: -1 })