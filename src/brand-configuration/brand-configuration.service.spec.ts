import { configStub } from './../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { BrandConfigurationService } from './brand-configuration.service';

jest.mock('./brand-configuration.service');

describe('BrandConfigurationService', () => {
  let service: BrandConfigurationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrandConfigurationService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<BrandConfigurationService>(BrandConfigurationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
