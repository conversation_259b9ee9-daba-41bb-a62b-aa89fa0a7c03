import { ConnectionNameEnum } from './../common/core/constants';
import { MerchantModule } from './../merchant/merchant.module';
import { BrandConfiguration, BrandConfigurationSchema } from './entities/brand-configuration.entity';
import { forwardRef, Module } from "@nestjs/common";
import { MongooseModule } from '@nestjs/mongoose';
import { BrandConfigurationController } from './brand-configuration.controller';
import { BrandConfigurationService } from './brand-configuration.service';
import { CommonModule } from "../common/common.module";
import { AccesstradeModule } from "../accesstrade/accesstrade.module";
import { CommissionPolicyModule } from "../commission-policy/commission-policy.module";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: BrandConfiguration.name,
          schema: BrandConfigurationSchema,
          collection: 'brand_configurations',
        },
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    MerchantModule,
    CommonModule,
    CommissionPolicyModule,
    forwardRef(() => AccesstradeModule)
  ],
  controllers: [BrandConfigurationController],
  providers: [BrandConfigurationService],
  exports: [BrandConfigurationService],
})
export class BrandConfigurationModule {}
