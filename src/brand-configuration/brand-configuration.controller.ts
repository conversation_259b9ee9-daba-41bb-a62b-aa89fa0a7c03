import { UpdateBrandConfigurationDto, UpdateStatusBrandConfigurationDto } from './dto/update-brand-configuration.dto';
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Param,
  Post,
  Put,
  Query, UploadedFile,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";

import { BrandConfigurationService } from './brand-configuration.service';
import { CreateBrandConfigurationDto } from './dto/create-brand-configuration.dto';
import { FindBrandConfigurationDto } from './dto/find-brand-configuration.dto';
import { AccessTokenGuard } from './../common/guards/access-token.guard';
import { ApiBadRequestResponse, ApiBearerAuth, ApiCreatedResponse, ApiForbiddenResponse, ApiNotFoundResponse, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CalculateEarnDto } from "./dto/calculate-earn.dto";
import { RedisService } from "../common/services/redis.service";
import { FileInterceptor } from "@nestjs/platform-express";
import { CommissionPolicyService } from "../commission-policy/commission-policy.service";

@ApiTags('Brand Configuration')
// @ApiBearerAuth()
@Controller()
// @UseGuards(AccessTokenGuard)
export class BrandConfigurationController {
  constructor(private readonly brandConfigurationService: BrandConfigurationService, private redisService: RedisService, private readonly commissionPolicyService: CommissionPolicyService) {}
  @Get()
  @ApiResponse({ status: 200, description: 'The record has been successfully found.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  findAll(@Query() query: FindBrandConfigurationDto) {
    return this.brandConfigurationService.findAll(query);
  }

  @Get(':id')
  @ApiResponse({ status: 200, description: 'The record has been successfully found.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async findById(@Param('id') id: string) {
    const brandConfiguration = await this.brandConfigurationService.findById(id);
    return {
      data: brandConfiguration,
    };
  }

  @Get(':campaign/max-commission')
  @ApiResponse({ status: 200, description: 'The record has been successfully found.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async getMaxCommission(@Param('campaign') campaign: string) {
    const result = await this.brandConfigurationService.getMaxCommissionByCampaign(campaign);
    return {
      data: result
    }
  }

  @Post('calculate-earn')
  @ApiResponse({ status: 200, description: 'The record has been successfully found.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async calculateEarn(@Body() data: CalculateEarnDto) {
    const calculateEarn = await this.brandConfigurationService.calculateEarn(data);
    return {
      data: calculateEarn
    }
  }

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiCreatedResponse({ description: 'The record has been successfully created.' })
  @ApiBadRequestResponse({ description: 'Any fields are not correct' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  async create(@Body() data: CreateBrandConfigurationDto, @UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File upload is required');
    }

    if (file.mimetype !== 'text/csv' && file.originalname.split('.').pop() !== 'csv') {
      throw new BadRequestException('Please upload a valid CSV file');
    }

    const brandConfiguration = await this.brandConfigurationService.create(data);

    await this.commissionPolicyService.uploadCSV(brandConfiguration.merchantId, file.buffer);
    return {
      data: brandConfiguration,
    };
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('file'))
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  @ApiBadRequestResponse({ description: 'Any fields are not correct' })
  async update(@Param('id') id: string, @Body() data: UpdateBrandConfigurationDto, @UploadedFile() file: Express.Multer.File) {
    try {
      const updated = await this.brandConfigurationService.update(id, data);

      if (file) {
        if (file.mimetype !== 'text/csv' && file.originalname.split('.').pop() !== 'csv') {
          throw new BadRequestException('Please upload a valid CSV file');
        }

        await this.commissionPolicyService.uploadCSV(updated.merchantId, file.buffer);
      }

      return {
        data: updated,
      };
    } catch (error) {
      throw new HttpException(error.message || error, error.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put(':id/status')
  @ApiResponse({ status: 200, description: 'The record has been successfully updated.' })
  @ApiNotFoundResponse({ description: 'Not found.' })
  @ApiForbiddenResponse({ description: 'Forbidden.' })
  @ApiBadRequestResponse({ description: 'Any fields are not correct' })
  async updateStatus(@Param('id') id: string, @Body() data: UpdateStatusBrandConfigurationDto) {
    try {
      const updated = await this.brandConfigurationService.updateStatus(id, data);

      return {
        data: updated,
      };
    } catch (error) {
      throw new HttpException(error.message || error, error.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
