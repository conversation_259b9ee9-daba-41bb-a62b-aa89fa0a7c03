import { CACHE_TIME, ConnectionNameEnum } from 'src/common/core/constants';
import { MerchantService } from './../merchant/merchant.service';
import { FindBrandConfigurationDto } from './dto/find-brand-configuration.dto';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, SortOrder } from 'mongoose';
import { get } from 'lodash';
import utils from '../common/core/utils';
import { BrandConfiguration, BrandConfigurationDocument } from './entities/brand-configuration.entity';
import { CreateBrandConfigurationDto } from './dto/create-brand-configuration.dto';
import { UpdateBrandConfigurationDto, UpdateStatusBrandConfigurationDto } from './dto/update-brand-configuration.dto';
import { FindBrandConfigurationMobileDto } from 'src/affiliate-mobile/dto/find-brand-configuration-mobile.dto';
import { RedisService } from '../common/services/redis.service';
import { AccessTradeApiService } from '../accesstrade/accesstrade-api.service';
import { FindBrands } from '../affiliate-mobile-v5/dto/find-brand-configuration-mobile.dto';
import { CalculateEarnDto } from "./dto/calculate-earn.dto";

@Injectable()
export class BrandConfigurationService {
  private readonly logger = new Logger(BrandConfigurationService.name);

  constructor(
    @InjectModel(BrandConfiguration.name, ConnectionNameEnum.AFFILIATE_V2)
    private brandConfigurationModel: Model<BrandConfigurationDocument>,
    private readonly merChantService: MerchantService,
    private readonly redisService: RedisService,
    private readonly accessTradeApiService: AccessTradeApiService,
  ) {
  }

  async create(createBrandConfigurationDto: CreateBrandConfigurationDto) {
    try {
      const brandConfiguration = await this.brandConfigurationModel.findOne({ merchantId: createBrandConfigurationDto.merchantId }).lean();
      if (brandConfiguration) {
        this.logger.error('Create failed - Brand configuration already exists');
        throw new HttpException('Brand configuration already exists', 400);
      }
      const merchantData = await this.merChantService.validateMerchantCode(createBrandConfigurationDto.merchantId);
      const storeData = await this.merChantService.validateMerchantStoreCode(createBrandConfigurationDto.merchantId, createBrandConfigurationDto.storeCode);
      createBrandConfigurationDto.merchantName = merchantData.name;
      createBrandConfigurationDto.storeName = storeData.name;
      const createBrandConfiguration = new this.brandConfigurationModel(createBrandConfigurationDto);
      const savedConfig = await createBrandConfiguration.save();

      await this.redisService.clearCacheByTags(...[BrandConfiguration.name]);

      return savedConfig;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findAll(query: FindBrandConfigurationDto) {
    try {
      const cacheKey = `brand_configuration:all:${JSON.stringify(query)}`;
      const cacheTags = [BrandConfiguration.name];

      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const _size = Number(get(query, 'size', 10));
        const _page = Number(get(query, 'page', 1));
        const { limit, skip } = utils.sanitizePageSize(_page, _size);
        const { size, page, sort, ...restData } = query;

        const sortField = sort || 'createdAt';
        const sortOrder = sortField.startsWith('-') ? -1 : 1;

        const sortQuery: { [key: string]: 1 | -1 } = {
          [sortField.replace('-', '')]: sortOrder,
        }

        const _query = JSON.parse(JSON.stringify(restData));

        const [total, data] = await Promise.all([
          this.brandConfigurationModel.count(_query),
          this.brandConfigurationModel.find(_query).limit(limit).skip(skip).sort(sortQuery).lean(),
        ]);
        return {
          data,
          meta: {
            currentPage: +_page,
            pageSize: +_size,
            totalPages: Math.ceil(total / _size),
            totalRows: total,
          },
        };
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findAllForMobile(query: FindBrandConfigurationMobileDto) {
    try {
      const cacheKey = `brand_configuration_all:mobile:${JSON.stringify(query)}`;
      const cacheTags = [BrandConfiguration.name];

      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const size = Number(get(query, 'size', 10));
        const page = Number(get(query, 'page', 1));
        const { limit, skip } = utils.sanitizePageSize(page, size);
        const { isEnabled } = query;
        const _query = JSON.parse(JSON.stringify({ isEnabled }));
        return await this.brandConfigurationModel.find(_query).limit(limit).skip(skip).sort({ index: 1 }).lean();
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async getListActive(params: FindBrands) {
    try {
      const cacheKey = `${BrandConfiguration.name}:getListActive:${params.limit}:${params.after}:${params.before}`;
      const cacheTags = [BrandConfiguration.name];

      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const limit = params.limit ?? 20;
        const query: any = {};
        if (query?.after) {
          query._id = { $gt: params.after };
        } else if (params?.before) {
          query._id = { $lt: params.before };
        }
        return await this.brandConfigurationModel.find({ isEnabled: true }).limit(limit).sort({ index: 1 }).lean();
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findOne(id: string) {
    try {
      const brandConfiguration = await this.brandConfigurationModel.findOne({ merchantId: id }).lean();
      if (!brandConfiguration) {
        this.logger.error(`BrandConfiguration not found with merchant id: ${id}`);
        throw new HttpException('brand Configuration not found', 404);
      }
      return brandConfiguration;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async findById(id: string) {
    const cacheKey = `brand_configuration:${id}`;
    const cacheTags = [BrandConfiguration.name];
    try {
      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const brandConfiguration = await this.brandConfigurationModel.findOne({ merchantId: id }).lean();
        if (!brandConfiguration) {
          this.logger.error(`BrandConfiguration not found with merchant id: ${id}`);
          throw new HttpException('brand Configuration not found', 404);
        }
        return brandConfiguration;
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async update(id: string, data: UpdateBrandConfigurationDto) {
    try {
      const existed = await this.brandConfigurationModel.findOne({
        merchantId: id,
      });
      if (!existed) {
        this.logger.error(`BrandConfiguration not found with merchant id: ${id}`);
        throw new HttpException(`BrandConfiguration with merchant id ${id} not found`, 404);
      }
      const merchantData = await this.merChantService.validateMerchantCode(data.merchantId);
      data.merchantName = merchantData.name;
      if (data.storeCode) {
        const storeData = await this.merChantService.validateMerchantStoreCode(data.merchantId, data.storeCode);
        data.storeName = storeData.name;
      }
      const updatedConfig = await this.brandConfigurationModel.findOneAndUpdate({ merchantId: id }, data, { new: true }).lean();

      await this.redisService.clearCacheByTags(...[BrandConfiguration.name]);

      return updatedConfig;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async updateStatus(id: string, data: UpdateStatusBrandConfigurationDto) {
    try {
      const existed = await this.brandConfigurationModel.findOne({
        merchantId: id,
      });
      if (!existed) {
        this.logger.error(`BrandConfiguration not found with merchant id: ${id}`);
        throw new HttpException(`BrandConfiguration with merchant id ${id} not found`, 404);
      }
      const updatedStatus = await this.brandConfigurationModel.findOneAndUpdate({ merchantId: id }, data, { new: true }).lean();

      await this.redisService.clearCacheByTags(...[BrandConfiguration.name]);
      return updatedStatus;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  parseToNumber(value: string): number {
    if (!value) {
      throw new Error('Invalid input');
    }

    const cleanedValue = value.replace(/[.,]/g, '');

    return parseFloat(cleanedValue);
  }

  async getMaxCommissionByCampaign(campaign: string) {
    try {
      const res = await this.accessTradeApiService.getCampaigns({ approval: 'successful', campaign_id: campaign });

      if (!res.data || res.data.length === 0) {
        return { value: 0, type: '' };
      }

      const firstItem = res.data[0];
      const maxCom = firstItem.max_com;

      if (!maxCom || maxCom.trim() === '') {
        return { value: 0, type: '' };
      }

      if (maxCom.includes('%')) {
        const value = parseFloat(maxCom.replace('%', '').trim());
        return { value, type: 'percent' }
      } else {
        const value = this.parseToNumber(maxCom);
        return { value, type: 'fixed' }
      }
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async calculateEarn(data: CalculateEarnDto): Promise<number> {
    try {
      const { earnType, earnCommissionRate, maxCommission } = data;
      if (earnType !== 'percent' && earnType !== 'fixed') {
        return 0;
      }

      if (earnType === 'percent') {
        return parseFloat(((maxCommission * earnCommissionRate) / 100).toFixed(2));
      } else {
        return Math.floor((maxCommission * earnCommissionRate) / 100)
      }
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }
}
