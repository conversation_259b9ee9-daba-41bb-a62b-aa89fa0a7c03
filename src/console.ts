import 'dotenv/config';
import { CommandFactory } from 'nest-commander';

import { AppModule } from './app.module';

async function bootstrap() {
  await CommandFactory.run(AppModule, ['log', 'error', 'warn', 'debug']);
}

bootstrap()
  .then(async () => {
    console.info('command bootstrapped ...!');
    process.exit(0);
  })
  .catch((err) => {
    console.error(`server failed to start command`, err);
    process.exit(1);
  })
  .finally(() => {
    return onExit();
  });

function onExit(): void {
  process.exit(1);
}
