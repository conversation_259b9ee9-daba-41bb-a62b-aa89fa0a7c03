import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { getLoggerModule } from './pino.config';
import { WorkerModule } from './worker/worker.module';
import { ScheduleModule } from '@nestjs/schedule';
import { MongooseModule } from '@nestjs/mongoose';
import { ConnectionNameEnum } from './common/core/constants';
import { AccesstradeModule } from './accesstrade/accesstrade.module';
import { CrawlDataModule } from './crawl-data/crawl-data.module';
import { TransactionV2Module } from './transaction-v2/transaction.module';
import { AffiliateConfigurationModule } from './affiliate-configuration/affiliate-configuration.module';
import { CoreTransactionModule } from './core-transaction/core-transaction.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { EventListenersModule } from "./event-listeners/event-listeners.module";

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('mongodb'),
      }),
      inject: [ConfigService],
      connectionName: ConnectionNameEnum.AFFILIATE_V2,
    }),
    BullModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        redis: configService.get('redis'),
        prefix: configService.get('redis.prefix'),
      }),
      inject: [ConfigService],
    }),
    AccesstradeModule,
    CrawlDataModule,
    TransactionV2Module,
    AffiliateConfigurationModule,
    CoreTransactionModule,
    WorkerModule,
    EventListenersModule,
    ScheduleModule.forRoot(),
    ...getLoggerModule(),
  ],
})
export class AppWorker {
}