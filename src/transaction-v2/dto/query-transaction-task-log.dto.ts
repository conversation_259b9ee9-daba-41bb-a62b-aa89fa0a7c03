import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { StatusCommon, TransactionInitLogKey } from 'src/common/core/constants';
import QueryCommonDto from 'src/common/core/query';

export class QueryTransactionTaskLogDto extends QueryCommonDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  orderId?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  transactionId?: string | null;

  @ApiPropertyOptional({ enum: StatusCommon })
  @IsOptional()
  @IsEnum(StatusCommon)
  status?: StatusCommon | StatusCommon[] | null;

  @ApiPropertyOptional({ enum: TransactionInitLogKey })
  @IsOptional()
  @IsEnum(TransactionInitLogKey)
  taskKey?: TransactionInitLogKey | TransactionInitLogKey[] | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  startDate?: Date | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  endDate?: Date | null;
}