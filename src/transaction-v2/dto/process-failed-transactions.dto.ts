import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { TransactionOperationAction } from "../../common/core/constants";

export class ProcessFailedTransactionsPayloadDto  {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  transactionIds?: string[];
}

export class ProcessFailedTransactionsDto  {
  @ApiProperty()
  @IsEnum(TransactionOperationAction)
  @IsNotEmpty()
  action: TransactionOperationAction;

  @ApiPropertyOptional()
  @IsOptional()
  payload: ProcessFailedTransactionsPayloadDto;
}