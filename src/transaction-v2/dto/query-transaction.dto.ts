import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { TransactionIssueStatus } from 'src/common/core/constants';
import QueryCommonDto from 'src/common/core/query';

export class QueryTransactionDto extends QueryCommonDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  orderId?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  transactionId?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  mobile?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  userId?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchantId?: string | null;

  @ApiPropertyOptional({ enum: TransactionIssueStatus })
  @IsOptional()
  @IsEnum(TransactionIssueStatus)
  issueStatus?: TransactionIssueStatus | TransactionIssueStatus[] | null;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coreTransactionIds?: string[] | null;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coreTriggerIds?: string[] | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  startDate?: Date | null;

  @IsString()
  @IsOptional()
  @ApiProperty({
    required: false,
    description: 'YYYY-MM-DD',
  })
  @Matches(/([12]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01]))/, { message: 'format YYYY-MM-DD' })
  @MaxLength(10)
  endDate?: Date | null;
}