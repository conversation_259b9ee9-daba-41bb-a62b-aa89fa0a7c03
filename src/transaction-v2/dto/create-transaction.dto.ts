import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateTransactionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  mobile: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchantId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchantName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  storeCode: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  storeName: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  totalCommission: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalAmount?: number | null;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  orderAmount: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  firstAmount?: number | null;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  secondAmount?: number | null;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  totalVUI?: number | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  firstTimeVUI?: number | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  secondTimeVUI: number | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  issueStatus: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  earnRate: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  earnCommissionRate: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  firstTimeIssuedRate: number;

  @ApiProperty()
  @IsOptional()
  transactionDate?: Date | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productCategory: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  productPrice: number;

  @ApiProperty()
  @IsOptional()
  salesTime: Date | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  platform: string | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  referrer: string | null;

  @ApiProperty()
  @IsNotEmpty()
  clickTime: Date;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  status: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  isConfirmed: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  utmSource?: string | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  utmCampaign?: string | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  utmContent?: string | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  utmMedium?: string | null;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  coreTransactionIds?: string[] | null;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  firstProcess?: boolean | null;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  secondProcess?: boolean | null;

  @ApiProperty()
  @IsString()
  @IsOptional()
  reasonRejected: string | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}