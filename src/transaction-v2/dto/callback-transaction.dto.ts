import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';

export class PaymentDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  mode: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  value: string;
}

export class LineItemDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  itemId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  itemCode: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  itemName?: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  quantity: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  price: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  totalLineDiscount?: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  amount: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  taxAmount?: number;

  @IsString()
  @IsOptional()
  @ApiProperty()
  remark?: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  type?: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  categories?: string;
}

export class TransactionDataDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  id: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  storeCode: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  storeCodeOrigin?: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  brandCode: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  type: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  billNumber: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  number: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  billDate: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  billAmount: number;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  amount?: number;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty()
  grossAmount: number;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  remarks: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  userId: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  mobile: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  agent?: string;

  @IsArray()
  @IsOptional()
  @ApiProperty()
  vouchers?: string[];

  @IsArray()
  @IsOptional()
  @ApiProperty()
  customFields?: string[];

  @IsArray()
  @IsOptional()
  @ApiProperty()
  effects?: string[];

  @IsArray()
  @IsOptional()
  @ApiProperty()
  segments?: string[];

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  createdAt: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  updatedAt: string;

  @IsArray()
  @Type(() => PaymentDto)
  @ValidateNested({ each: true })
  @IsOptional()
  @ApiProperty()
  payments?: PaymentDto[];

  @IsArray()
  @Type(() => LineItemDto)
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @ApiProperty()
  lineItems: LineItemDto[];
}

export class TclTransactionCallBackDataRequestModelMetaDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  topic: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  action: string;

  @Type(() => TransactionDataDto)
  @ValidateNested({ each: true })
  @IsNotEmpty()
  @ApiProperty()
  data: TransactionDataDto;
}

export class TclTransactionCallBackDataRewardListMetaDto {
  @ApiProperty({
    required: false,
    example: 12,
  })
  @IsNumber()
  @IsOptional()
  brandCurrencyPoint: number;

  @ApiProperty({
    required: false,
    example: 'CHAUCHAU',
  })
  @IsString()
  @IsOptional()
  brandCurrencyCode: string;

  @ApiProperty({
    required: false,
    example: 12,
  })
  @IsNumber()
  @IsOptional()
  vuiPoint: number;

  @ApiProperty({
    required: false,
    example: 'CHAUA597YI11',
  })
  @IsString()
  @IsOptional()
  voucherCode: string;

  @ApiProperty({
    required: false,
    example: '12 VUI và 12 Xu Chau Chau và ưu đãi vào',
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    required: false,
    example: 'Yay! Bạn đã tích được 12 VUI và 12 Xu Chau Chau và ưu đãi tại Chau Chau.',
  })
  @IsString()
  @IsOptional()
  body: string;
}

export class TclTransactionCallBackDataRewardListDto {
  @ApiProperty({
    examples: ['BRAND_POINT', 'VUI_POINT', 'VOUCHER', 'NOTIFICATION'],
  })
  @IsString()
  @IsNotEmpty()
  rewardCode: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => TclTransactionCallBackDataRewardListMetaDto)
  meta: TclTransactionCallBackDataRewardListMetaDto;
}

export class TclTransactionCallBackDataDto {
  @ApiProperty({
    example: 'ADD_TRANSACTION',
  })
  @IsString()
  @IsNotEmpty()
  triggerMasterCode: string;

  @ApiProperty({
    example: '62f47397c3d7f168edeadb41',
  })
  @IsString()
  @IsNotEmpty()
  triggerId: string;

  @ApiProperty({
    example: '12345678901234567890',
  })
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiProperty({
    example: '9d3ac9a0-bd77-49df-9497-baeae368f4d6',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    example: '84989481478',
  })
  @IsString()
  @IsNotEmpty()
  mobile: string;

  @ApiProperty({
    example: 'ChauChau',
  })
  @IsString()
  @IsNotEmpty()
  brandCode: string;

  @ApiProperty({
    example: 'CHAUCHAU',
  })
  @IsString()
  @IsNotEmpty()
  storeCode: string;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => TclTransactionCallBackDataRewardListDto)
  rewardResultList: [TclTransactionCallBackDataRewardListDto];

  @ApiProperty()
  @ValidateNested()
  @Type(() => TclTransactionCallBackDataRequestModelMetaDto)
  requestModel: TclTransactionCallBackDataRequestModelMetaDto;
}

export class TclTransactionCallBackDto {
  @ApiProperty({ example: 'trigger' })
  @IsString()
  @IsNotEmpty()
  topic: string;

  @ApiProperty({ example: 'reward' })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty()
  @ValidateNested()
  @Type(() => TclTransactionCallBackDataDto)
  data: TclTransactionCallBackDataDto;
}
