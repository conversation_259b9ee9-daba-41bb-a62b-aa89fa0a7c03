import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateTransactionDto } from './create-transaction.dto';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

export interface ICoreTransaction {
  id?: string;
  success?: boolean;
  message?: string;
  code?: number;
  issueDate?: string;
  mobile?: string;
  billNumber?: string;
  number?: string;
  storeCode?: string;
  brandCode?: string;
  billAmount?: number;
}

export class UpdateTransactionDto extends PartialType(CreateTransactionDto) {
  coreTransactions?: ICoreTransaction[] | null;
}

export class UpdateTransactionFieldDto {
  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  transactionIds: string[];

  @ApiProperty()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  fields: string[];
}
