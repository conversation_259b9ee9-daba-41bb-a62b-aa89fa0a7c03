import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class TransactionLogsDetail {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  firstTimeVUI?: number | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  secondTimeVUI?: number | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  remainingVUI?: number | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  status: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  firstIssueDetails?: {
    totalVUI: number;
    totalRequestedVUI: number;
    status: string;
    issuedVUI: number;
    earnCommissionRate: number;
    firstIssueRate: number;
    totalCommission: number;
    reason?: string;
  };

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  secondIssueDetails?: {
    totalVUI: number;
    totalRequestedVUI: number;
    status: string;
    issuedVUI: number;
    reason?: string;
  };

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class CreateTransactionLogDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  limitVUI?: number | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  remainingVUI?: number | null;

  @ApiProperty()
  @IsArray()
  transactionLogDetails: TransactionLogsDetail;
}