import { forwardR<PERSON>, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConnectionNameEnum, QUEUE_PROCESSOR } from '../common/core/constants';
import { Transaction, TransactionSchema } from './entities/transaction.entity';
import { TransactionService } from './transaction.service';
import TransactionController from './transaction.controller';
import { CrawlDataModule } from 'src/crawl-data/crawl-data.module';
import { CoreTransactionModule } from 'src/core-transaction/core-transaction.module';
import { AffiliateConfigurationModule } from 'src/affiliate-configuration/affiliate-configuration.module';
import { CommonModule } from 'src/common/common.module';
import { UserModule } from '../user/user.module';
import { BrandConfigurationModule } from '../brand-configuration/brand-configuration.module';
import { TransactionTaskLog, TransactionTaskLogSchema } from './entities/transaction-task-log.entity';
import { TransactionLog, TransactionLogSchema } from './entities/transaction-log.entity';
import { BullModule } from '@nestjs/bull';
import { TransactionMobileService } from './transaction-mobile.service';
import { TransactionOperation, TransactionOperationSchema } from "./entities/transaction-operation.entity";
import { AccesstradeModule } from '../accesstrade/accesstrade.module';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Transaction.name,
          schema: TransactionSchema,
          collection: 'transactions',
        },
        {
          name: TransactionTaskLog.name,
          schema: TransactionTaskLogSchema,
          collection: 'transaction_task_logs',
        },
        {
          name: TransactionLog.name,
          schema: TransactionLogSchema,
          collection: 'transaction_logs',
        },
        {
          name: TransactionOperation.name,
          schema: TransactionOperationSchema,
          collection: 'transaction_operations'
        }
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.CALCULATE_VUI,
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_STATUS_TRANSACTION,
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_REAL_VUI,
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.TRANSACTION_OPERATION,
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_TRANSACTION
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.TRANSACTION_TASK
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.REISSUE_VUI,
    }),
    forwardRef(() => CrawlDataModule),
    forwardRef(() => AccesstradeModule),
    CoreTransactionModule,
    UserModule,
    BrandConfigurationModule,
    AffiliateConfigurationModule,
    CommonModule
  ],
  controllers: [TransactionController],
  providers: [TransactionService, TransactionMobileService],
  exports: [TransactionService, TransactionMobileService],
})
export class TransactionV2Module {
}