import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, SortOrder } from 'mongoose';
import { Transaction } from './entities/transaction.entity';
import { ConnectionNameEnum, QUEUE_PROCESSOR } from 'src/common/core/constants';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { RedisService } from 'src/common/services/redis.service';
import { CrawlDataService } from '../crawl-data/crawl-data.service';
import { UserService } from '../user/user.service';
import { BrandConfigurationService } from '../brand-configuration/brand-configuration.service';
import { TransactionTaskLog } from './entities/transaction-task-log.entity';
import { TransactionLog } from './entities/transaction-log.entity';
import { AffiliateConfigurationService } from 'src/affiliate-configuration/affiliate-configuration.service';
import { GetTransactionsDto } from '../affiliate-mobile-v5/dto/get-transactions.dto';

@Injectable()
export class TransactionMobileService {
  private readonly logger = new Logger(TransactionMobileService.name);

  constructor(
    @InjectModel(Transaction.name, ConnectionNameEnum.AFFILIATE_V2) private transactionModel: Model<Transaction>,
    @InjectModel(TransactionTaskLog.name, ConnectionNameEnum.AFFILIATE_V2) private transactionTaskLogModel: Model<TransactionTaskLog>,
    @InjectModel(TransactionLog.name, ConnectionNameEnum.AFFILIATE_V2) private transactionLogModel: Model<TransactionLog>,
    @InjectQueue(QUEUE_PROCESSOR.UPDATE_STATUS_TRANSACTION) private updateStatusTransactionQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.CALCULATE_VUI) private calculateVuiQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.UPDATE_REAL_VUI) private updateRealVUIQueue: Queue,
    private readonly redisService: RedisService,
    private readonly crawlDataService: CrawlDataService,
    private readonly userService: UserService,
    private readonly brandConfigurationService: BrandConfigurationService,
    private readonly affiliateConfigurationService: AffiliateConfigurationService,
  ) {
  }


  async getTransactions(cursor: GetTransactionsDto) {
    const query: any = {};
    let sort: Record<string, SortOrder> = { _id: 1 };
    let limit = cursor.limit ?? 10;

    if (cursor?.after) {
      query._id = { $gt: cursor.after };
    } else if (cursor?.before) {
      query._id = { $lt: cursor.before };
      sort = { _id: -1 };
    }

    let [data, total] = await Promise.all([await this.transactionModel
      .find(query)
      .sort(sort)
      .limit(limit)
      .exec(), this.transactionModel.countDocuments(query)]);

    if (cursor?.before) {
      data = data.reverse();
    }

    const previousCursor = data.length ? data[0]._id : null;
    const nextCursor = data.length >= limit ? data[data.length - 1]._id : null;


    return {
      data: data,
      meta: {
        cursors: {
          before: previousCursor && query?._id ? previousCursor.toString() : null,
          after: nextCursor ? nextCursor.toString() : null,
        },
        total,
      },
    };
  }
}