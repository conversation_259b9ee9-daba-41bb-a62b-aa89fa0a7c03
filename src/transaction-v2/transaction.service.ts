import { BadRequestException, HttpException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import * as moment from 'moment';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { get, isArray, isNil } from 'lodash';
import { Transaction } from './entities/transaction.entity';
import {
  CACHE_TIME,
  ConnectionNameEnum,
  QUEUE_CONSUMER,
  QUEUE_PROCESSOR,
  StatusCommon,
  TransactionInitLogKey,
  TransactionIssueStatus,
  TransactionOperationAction,
} from 'src/common/core/constants';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { UpdateTransactionDto, UpdateTransactionFieldDto } from './dto/update-transaction.dto';
import { QueryTransactionDto } from './dto/query-transaction.dto';
import utils from 'src/common/core/utils';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { RedisService } from 'src/common/services/redis.service';
import { TclTransactionCallBackDto } from './dto/callback-transaction.dto';
import { TransactionStatusHelper } from '../common/helpers/transaction-status.helper';
import { CrawlDataService } from '../crawl-data/crawl-data.service';
import { UserService } from '../user/user.service';
import { BrandConfigurationService } from '../brand-configuration/brand-configuration.service';
import { TransactionTaskLog } from './entities/transaction-task-log.entity';
import { CreateTransactionTaskLogDto } from './dto/create-transaction-task-log.dto';
import { QueryTransactionTaskLogDto } from './dto/query-transaction-task-log.dto';
import { TransactionLog } from './entities/transaction-log.entity';
import { CreateTransactionLogDto } from './dto/create-transaction-log.dto';
import { AffiliateConfigurationService } from 'src/affiliate-configuration/affiliate-configuration.service';
import { BrandConfiguration } from '../brand-configuration/entities/brand-configuration.entity';
import { CrawlTransactionData } from '../crawl-data/entities/crawl-transaction-data.entity';
import { AppEvent } from '../common/core/events';
import { NotificationAction, NotificationCommunicationType, NotificationTopic, NotificationType } from '@taptap-lyt/sdk';
import { NotificationService } from '../common/services/notification.service';
import { TransactionOperation, TransactionOperationDocument } from './entities/transaction-operation.entity';
import { ProcessFailedTransactionsDto } from './dto/process-failed-transactions.dto';
import { TransactionTaskDTO } from './dto/transaction-task.dto';

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  constructor(
    @InjectModel(Transaction.name, ConnectionNameEnum.AFFILIATE_V2) private transactionModel: Model<Transaction>,
    @InjectModel(TransactionTaskLog.name, ConnectionNameEnum.AFFILIATE_V2) private transactionTaskLogModel: Model<TransactionTaskLog>,
    @InjectModel(TransactionLog.name, ConnectionNameEnum.AFFILIATE_V2) private transactionLogModel: Model<TransactionLog>,
    @InjectModel(TransactionOperation.name, ConnectionNameEnum.AFFILIATE_V2) private transactionOperationModel: Model<TransactionOperationDocument>,
    @InjectQueue(QUEUE_PROCESSOR.UPDATE_STATUS_TRANSACTION) private updateStatusTransactionQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.CALCULATE_VUI) private calculateVuiQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.UPDATE_REAL_VUI) private updateRealVUIQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.TRANSACTION_OPERATION) private transactionOperationQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.UPDATE_TRANSACTION) private updateTransactionQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.TRANSACTION_TASK) private transactionTaskQueue: Queue,
    @InjectQueue(QUEUE_PROCESSOR.REISSUE_VUI) private reissueVuiQueue: Queue,
    private readonly redisService: RedisService,
    private readonly crawlDataService: CrawlDataService,
    private readonly userService: UserService,
    private readonly brandConfigurationService: BrandConfigurationService,
    private readonly affiliateConfigurationService: AffiliateConfigurationService,
    private readonly eventEmitter: EventEmitter2,
    private readonly notificationService: NotificationService,
  ) {}

  private async getMax1stIssuedPoints(): Promise<number> {
    const affiliateConfiguration = await this.affiliateConfigurationService.findOne();
    return affiliateConfiguration.total1stTimeIssuedPerUser;
  }

  async list(query: QueryTransactionDto) {
    try {
      const _size = Number(get(query, 'size', 10));
      const _page = Number(get(query, 'page', 1));
      const { limit, skip } = utils.sanitizePageSize(_page, _size);
      const { size, page, startDate, endDate, issueStatus, ...restData } = query;
      const _query = JSON.parse(JSON.stringify(restData));
      if (startDate || endDate) {
        const queryDate = {};
        if (startDate) queryDate['$gte'] = moment(startDate).startOf('days').toDate();
        if (endDate) queryDate['$lte'] = moment(endDate).endOf('days').toDate();
        _query['transactionDate'] = queryDate;
      }

      if (issueStatus) {
        _query['issueStatus'] = {
          $in: Array.isArray(issueStatus) ? issueStatus : [issueStatus],
        };
      }
      const [total, data] = await Promise.all([
        this.transactionModel.count(_query),
        this.transactionModel.find(_query).limit(limit).skip(skip).sort({ createdAt: -1 }).lean(),
      ]);
      return {
        data,
        meta: {
          currentPage: +_page,
          pageSize: +_size,
          totalPages: Math.ceil(total / _size),
          totalRows: total,
        },
      };
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  getLogsByUser(userId: string) {
    try {
      return this.transactionLogModel.findOne({ userId }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async listTaskLogs(query: QueryTransactionTaskLogDto) {
    try {
      const _size = Number(get(query, 'size', 10));
      const _page = Number(get(query, 'page', 1));
      const { limit, skip } = utils.sanitizePageSize(_page, _size);
      const { size, page, startDate, endDate, status, taskKey, ...restData } = query;
      const _query = JSON.parse(JSON.stringify(restData));
      if (startDate || endDate) {
        const queryDate = {};
        if (startDate) queryDate['$gte'] = moment(startDate).startOf('days').toDate();
        if (endDate) queryDate['$lte'] = moment(endDate).endOf('days').toDate();
        _query['createdAt'] = queryDate;
      }

      if (status) {
        _query['status'] = {
          $in: Array.isArray(status) ? status : [status],
        };
      }

      if (taskKey) {
        _query['taskKey'] = {
          $in: Array.isArray(taskKey) ? taskKey : [taskKey],
        };
      }

      const [total, data] = await Promise.all([
        this.transactionTaskLogModel.count(_query),
        this.transactionTaskLogModel.find(_query).limit(limit).skip(skip).sort({ createdAt: -1 }).lean(),
      ]);
      return {
        data,
        meta: {
          currentPage: +_page,
          pageSize: +_size,
          totalPages: Math.ceil(total / _size),
          totalRows: total,
        },
      };
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async getOne(transactionId: string): Promise<Transaction> {
    try {
      return await this.transactionModel.findOne({ transactionId });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async getTransactionByIds(transactionIds: string[], statuses?: string[]): Promise<Transaction[]> {
    try {
      const query: any = {};

      if (transactionIds && transactionIds.length > 0) {
        query.transactionId = { $in: transactionIds };
      }

      if (statuses && statuses.length > 0) {
        query.issueStatus = { $in: statuses };
      }

      return await this.transactionModel.find(query).exec();
    } catch (error) {
      this.logger.error(error);
    }
  }

  async create(data: CreateTransactionDto): Promise<Transaction> {
    try {
      const transaction = new this.transactionModel(data);
      return await transaction.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async update(transactionId: string, data: UpdateTransactionDto) {
    try {
      const transaction = await this.transactionModel.findOne({ transactionId });
      if (!transaction) {
        this.logger.error(`Transaction not found with transactionId: ${transactionId}`);
        throw new HttpException(`Transaction with transactionId ${transactionId} not found`, 404);
      }
      return await this.transactionModel.findOneAndUpdate({ transactionId }, { $set: data }, { new: true }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async delete(transactionId: string): Promise<boolean> {
    try {
      const result = await this.transactionModel.deleteOne({ transactionId }).exec();
      if (result.deletedCount > 0) {
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async updateTransactionLog(userId: string, data: any) {
    try {
      const transactionLog = await this.transactionLogModel.findOne({ userId });
      if (!transactionLog) {
        this.logger.error(`Transaction log not found with userId: ${userId}`);
        throw new HttpException(`Transaction log with userId ${userId} not found`, 404);
      }
      return await this.transactionLogModel.findOneAndUpdate({ userId }, { $set: data }, { new: true }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async createTransactionTaskLog(data: CreateTransactionTaskLogDto): Promise<TransactionTaskLog> {
    try {
      const transactionTaskLog = new this.transactionTaskLogModel(data);
      return await transactionTaskLog.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async createTransactionLog(userId: string, data: CreateTransactionLogDto) {
    try {
      return await this.transactionLogModel.updateOne(
        { userId },
        {
          $push: { transactionLogDetails: data.transactionLogDetails },
          $setOnInsert: { limitVUI: data.limitVUI },
          $set: { remainingVUI: data.remainingVUI },
        },
        { upsert: true },
      );
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  getTransactionLog(userId: string) {
    return this.transactionLogModel.findOne({ userId }).lean();
  }

  private async logMissingFieldError(transaction: CrawlTransactionData, fieldName: string) {
    const message = `The transaction initialization failed due to missing the required field: ${fieldName}.`;

    const existingLog = await this.transactionTaskLogModel.findOne({
      orderId: transaction.transactionId,
      transactionId: transaction.conversionId,
      message,
    });

    if (!existingLog) {
      await this.createTransactionTaskLog({
        orderId: transaction.transactionId,
        transactionId: transaction.conversionId,
        status: StatusCommon.FAILED,
        taskKey: TransactionInitLogKey.INIT_TRANSACTION,
        taskMessage: message,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }

  private async getBrandConfiguration(utmMedium: string) {
    const cacheKey = `brand_configuration:${utmMedium}`;
    return await this.redisService.cacheWithTags([BrandConfiguration.name], cacheKey, CACHE_TIME.TWO_HOURS, async () => {
      return await this.brandConfigurationService.findOne(utmMedium);
    });
  }

  private validateBrandConfiguration(brandConfiguration: BrandConfiguration, transaction: CrawlTransactionData) {
    if (!brandConfiguration.isEnabled) {
      const message = `The transaction initialization failed because brand configuration is disabled for the specified utmMedium: ${transaction.utmMedium}.`;

      this.logger.error(message);
      throw new HttpException(message, 400);
    }
  }

  private buildCalculateIssueData(transaction: CrawlTransactionData, brandConfiguration: BrandConfiguration) {
    return {
      earnCommissionRate: brandConfiguration.earnCommissionRate,
      earnRate: brandConfiguration?.earnRate ?? 0,
      firstIssueRate: brandConfiguration.firstTimeIssuedRate,
      totalCommission: transaction.commission,
    };
  }

  private isMissingRequiredField(transaction: CrawlTransactionData, fieldName: string): boolean {
    return !transaction[fieldName] || transaction[fieldName] === '' || transaction[fieldName] === 'encode_mobile';
  }

  private determineIssueStatus(transaction: Transaction, status: number, isConfirmed: number, calculateIssueData: any) {
    const statusHelper = new TransactionStatusHelper(status, isConfirmed);

    let issueStatus = transaction?.issueStatus ?? TransactionIssueStatus.HOLD;
    let firstProcess = transaction?.firstProcess ?? false;
    let secondProcess = transaction?.secondProcess ?? false;
    let totalVUI = transaction?.totalVUI ?? 0;
    let totalAmount = transaction?.totalAmount ?? 0;

    if (statusHelper.isPending()) {
      issueStatus = TransactionIssueStatus.HOLD;
      totalVUI = utils.calculateTotalIssue(calculateIssueData, false);
      totalAmount = totalVUI;
    } else if (statusHelper.isTemporarilyApproved()) {
      issueStatus = TransactionIssueStatus.ISSUING_1ST;
      firstProcess = true;
      totalVUI = utils.calculateTotalIssue(calculateIssueData, false);
      totalAmount = totalVUI;
    } else if (statusHelper.isApproved()) {
      issueStatus = TransactionIssueStatus.ISSUING_2ND;
      secondProcess = true;
      totalVUI = utils.calculateTotalIssue(calculateIssueData, false);
      totalAmount = totalVUI;
    }

    return { issueStatus, firstProcess, secondProcess, totalVUI, totalAmount };
  }

  private async handleExistingTransaction(
    crawlTransaction: CrawlTransactionData,
    existingTransaction: Transaction,
    issueStatus: string,
    firstProcess: boolean,
    secondProcess: boolean,
    totalVUI: number,
    totalAmount: number,
  ) {
    const currentStatus = existingTransaction.status;
    const currentIsConfirmed = existingTransaction.isConfirmed;
    const newStatus = crawlTransaction.status;
    const newIsConfirmed = crawlTransaction.isConfirmed;
    const currentCommission = existingTransaction.totalCommission;
    const newCommission = crawlTransaction.commission;

    /* Cập nhật nếu status hoặc isConfirmed thay đổi */
    if (currentStatus !== newStatus || currentIsConfirmed !== newIsConfirmed) {
      await Promise.all([
        this.createTransactionTaskLog({
          orderId: crawlTransaction.transactionId,
          transactionId: crawlTransaction.conversionId,
          status: StatusCommon.SUCCESS,
          taskKey: TransactionInitLogKey.UPDATE_TRANSACTION_STATUS,
          taskMessage: `Transaction status updated from status: ${currentStatus}, isConfirmed: ${currentIsConfirmed} to status: ${crawlTransaction.status}, isConfirmed: ${crawlTransaction.isConfirmed}.`,
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
        this.update(crawlTransaction.conversionId, {
          status: crawlTransaction.status,
          isConfirmed: crawlTransaction.isConfirmed,
          reasonRejected: crawlTransaction.reasonRejected,
          firstProcess,
          secondProcess,
          issueStatus,
          totalVUI,
          totalAmount,
          updatedAt: new Date(),
        }),
      ]);
    }

    /* Chỉ update commission nếu commission mới khác và KHÁC 0 */
    if (currentCommission !== newCommission && newCommission !== 0) {
      const isIncreased = newCommission > currentCommission;
      const notiBody = isIncreased
        ? `Đơn hàng ${existingTransaction.transactionId} tại ${
            existingTransaction.merchantName
          } đã được cập nhật. Số VUI tạm tính tăng từ ${currentCommission.toLocaleString()} -> ${newCommission.toLocaleString()}. Cùng kiểm tra ngay để không bỏ lỡ điểm thưởng nhé!`
        : `Đơn hàng ${existingTransaction.transactionId} tại ${
            existingTransaction.merchantName
          } đã được cập nhật. Số VUI tạm tính thay đổi từ ${currentCommission.toLocaleString()} -> ${newCommission.toLocaleString()}. Mong bạn thông cảm và vui lòng kiểm tra lại thông tin mới nhất nhé!`;

      // Khai báo biến secondTimeVUI và chỉ tính khi firstProcess là true
      let secondTimeVUI = 0;
      if (existingTransaction.firstProcess) {
        // Tính lại secondTimeVUI dựa trên commission mới
        secondTimeVUI = totalVUI;
        if (existingTransaction.firstTimeVUI > 0) {
          secondTimeVUI = totalVUI - existingTransaction.firstTimeVUI;
        }
      } else {
        // Nếu chưa qua firstProcess, giữ nguyên giá trị cũ
        secondTimeVUI = existingTransaction.secondTimeVUI || 0;
      }

      const transactionLog = await this.getTransactionLog(existingTransaction.userId);

      if (transactionLog && transactionLog.transactionLogDetails) {
        // Chỉ cập nhật secondTimeVUI khi firstProcess là true
        const updatedTransactionDetails = transactionLog.transactionLogDetails.map((detail) => {
          if (detail.transactionId === existingTransaction.transactionId) {
            return {
              ...detail,
              secondTimeVUI: existingTransaction.firstProcess ? secondTimeVUI : detail.secondTimeVUI,
            };
          }
          return detail;
        });

        await this.updateTransactionLog(existingTransaction.userId, {
          transactionLogDetails: updatedTransactionDetails,
        });
      }

      await Promise.all([
        this.createTransactionTaskLog({
          orderId: crawlTransaction.transactionId,
          transactionId: crawlTransaction.conversionId,
          status: StatusCommon.SUCCESS,
          taskKey: TransactionInitLogKey.UPDATE_TRANSACTION_COMMISSION,
          taskMessage: `Transaction commission updated from ${currentCommission} to ${newCommission}.`,
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
        this.update(crawlTransaction.conversionId, {
          totalCommission: newCommission,
          totalVUI,
          totalAmount,
          // Chỉ cập nhật secondTimeVUI và secondAmount khi firstProcess là true
          ...(existingTransaction.firstProcess
            ? {
                secondTimeVUI,
                secondAmount: secondTimeVUI,
              }
            : {}),
          updatedAt: new Date(),
        }),
        this.notificationService.sendNotification({
          action: NotificationAction.CUSTOM,
          userId: existingTransaction.userId,
          mobile: existingTransaction.mobile,
          storeCode: existingTransaction.storeCode,
          title: isIncreased ? 'Tin vui! Bạn vừa được cộng thêm VUI 🎉' : 'Có chút thay đổi về VUI đơn hàng của bạn',
          body: notiBody,
          type: NotificationType.CONTENT,
          brandCode: existingTransaction.merchantId,
          communicationType: NotificationCommunicationType.HAVING_NOTIFICATION,
          isNotify: true,
          topic: NotificationTopic.ONE_USER,
          templateCode: 'Order_Change_Commission',
          ctaButton: 'Xem đơn hàng',
          ctaLink: `taptapvui://myOnlineOrder_detail?orderId=${existingTransaction.orderId}`,
        }),
      ]);

      this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
        orderId: crawlTransaction.transactionId,
        userId: crawlTransaction.utmContent,
        transactions: [
          {
            transactionId: crawlTransaction.conversionId,
            commission: newCommission,
            totalVUI: totalVUI,
            firstAmount: existingTransaction.firstTimeVUI || 0,
            secondAmount: existingTransaction.secondTimeVUI || 0,
            firstAmountStatus: null,
            secondAmountStatus: null,
            updatedAt: new Date(),
          },
        ],
      });
    }
  }

  private async handleNewTransaction(
    crawlTransaction: CrawlTransactionData,
    brandConfiguration: BrandConfiguration,
    firstProcess: boolean,
    secondProcess: boolean,
    issueStatus: string,
    totalVUI: number,
    totalAmount: number,
  ) {
    const user = await this.userService.getCMSUserById(crawlTransaction.utmContent);

    if (!user) {
      this.logger.error(`Invalid user id: ${crawlTransaction.utmContent}`);
      throw new HttpException('Invalid user id', 400);
    }

    const data = {
      orderId: crawlTransaction.transactionId,
      transactionId: crawlTransaction.conversionId,
      userId: user.id,
      mobile: user.mobile,
      merchantId: brandConfiguration.merchantId,
      merchantName: brandConfiguration.merchantName,
      storeCode: brandConfiguration.storeCode,
      storeName: brandConfiguration.storeName,
      totalCommission: crawlTransaction.commission,
      orderAmount: crawlTransaction.productPrice * crawlTransaction.productQuantity,
      totalVUI: totalVUI,
      totalAmount: totalAmount,
      firstTimeVUI: 0,
      secondTimeVUI: 0,
      issueStatus,
      earnRate: brandConfiguration?.earnRate ?? 0,
      earnCommissionRate: brandConfiguration.earnCommissionRate,
      firstTimeIssuedRate: brandConfiguration.firstTimeIssuedRate,
      transactionDate: crawlTransaction.transactionTime,
      productId: crawlTransaction.productId,
      productCategory: crawlTransaction.productCategory,
      productPrice: crawlTransaction.productPrice,
      salesTime: null,
      platform: crawlTransaction.platform,
      referrer: null,
      clickTime: crawlTransaction.clickTime,
      status: crawlTransaction.status,
      isConfirmed: crawlTransaction.isConfirmed,
      utmSource: crawlTransaction.utmSource,
      utmCampaign: crawlTransaction.utmCampaign,
      utmContent: crawlTransaction.utmContent,
      utmMedium: crawlTransaction.utmMedium,
      reasonRejected: crawlTransaction.reasonRejected,
      firstProcess,
      secondProcess,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await Promise.all([
      this.create(data),
      this.createTransactionTaskLog({
        orderId: crawlTransaction.transactionId,
        transactionId: crawlTransaction.conversionId,
        status: StatusCommon.SUCCESS,
        taskKey: TransactionInitLogKey.INIT_TRANSACTION,
        taskMessage: `The transaction initialization was successful.`,
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    ]);

    if (issueStatus === TransactionIssueStatus.HOLD) {
      const data = await this.notificationService.sendNotification({
        action: NotificationAction.CUSTOM,
        userId: user.id,
        mobile: user.mobile,
        storeCode: brandConfiguration.storeCode,
        title: 'Ghi nhận tích VUI đơn hàng online',
        body: `Đơn hàng #${crawlTransaction.conversionId} tại ${
          brandConfiguration.merchantName
        } đã được ghi nhận. Bạn sẽ được ${totalVUI.toLocaleString()} VUI sau khi đơn hàng hoàn tất`,
        type: NotificationType.CONTENT,
        brandCode: brandConfiguration.merchantId,
        communicationType: NotificationCommunicationType.HAVING_NOTIFICATION,
        isNotify: true,
        topic: NotificationTopic.ONE_USER,
        templateCode: 'Order_Tracked',
        ctaButton: 'Xem đơn hàng',
        ctaLink: `taptapvui://myOnlineOrder_detail?orderId=${crawlTransaction.transactionId}`,
      });

      this.logger.log(`[NOTIFICATION][PENDING]: ${data}`);
    }
  }

  private isProcessableTransaction(crawlTx: CrawlTransactionData) {
    const helper = new TransactionStatusHelper(crawlTx.status, crawlTx.isConfirmed);

    const valid = helper.isPending() || helper.isTemporarilyApproved() || helper.isApproved() || helper.isRejected() || helper.isRefunded();

    if (!valid) {
      this.logger.log(
        `[CRON][INIT_TRANSACTION]Transaction ${crawlTx.conversionId} skipped due to invalid status: ${crawlTx.status}, isConfirmed: ${crawlTx.isConfirmed}`,
      );
    }
    return valid;
  }

  private buildAffiliateOrderCreatedEvent(crawlTx: CrawlTransactionData, brandConfig: BrandConfiguration, totalVUI: number) {
    const helper = new TransactionStatusHelper(crawlTx.status, crawlTx.isConfirmed);
    const status = helper.isPending()
      ? 'PENDING'
      : helper.isTemporarilyApproved()
      ? 'TEMP_APPROVED'
      : helper.isApproved()
      ? 'APPROVED'
      : helper.isRejected()
      ? 'CANCELLED'
      : helper.isRefunded()
      ? 'REFUNDED'
      : 'UNKNOWN';

    return {
      orderId: crawlTx.transactionId,
      userId: crawlTx.utmContent,
      merchantId: brandConfig.merchantId,
      merchantName: brandConfig.merchantName,
      merchantLogo: brandConfig.iconImageLink,
      status: 'TEMP_APPROVED',
      transactions: [
        {
          transactionId: crawlTx.conversionId,
          commission: crawlTx.commission,
          orderAmount: crawlTx.productPrice * crawlTx.productQuantity,
          totalVUI: totalVUI,
          status: status,
          transactionDate: crawlTx.transactionTime,
        },
      ],
      orderDate: crawlTx.transactionTime,
    };
  }

  async processInitTransactions(batchSize = 100) {
    let page = 0;
    let hasMore = true;

    while (hasMore) {
      const crawlTransactions = await this.crawlDataService.getCrawlTransactionsByPage(page, batchSize);

      if (!isArray(crawlTransactions) || !crawlTransactions.length) {
        hasMore = false;
        break;
      }

      for (const crawlTx of crawlTransactions) {
        if (isNil(crawlTx)) continue;
        if (!this.isProcessableTransaction(crawlTx)) continue;

        const lockKey = `transaction:lock:${crawlTx.conversionId}`;
        const ttl = CACHE_TIME.THIRTY_FIVE_MINUTES; // 35 minutes

        const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, crawlTx.conversionId, ttl);

        if (!lockAcquired) {
          this.logger.warn(`Transaction ${crawlTx.conversionId} already locked.`);
          continue;
        }

        let shouldEmitEvent = false;
        let eventPayload = null;

        try {
          if (this.isMissingRequiredField(crawlTx, 'utmContent')) {
            await this.logMissingFieldError(crawlTx, 'utmContent');
            continue;
          }

          const existingTx = await this.getOne(crawlTx.conversionId);
          const brandConfiguration: BrandConfiguration = await this.getBrandConfiguration(crawlTx.utmMedium);
          this.validateBrandConfiguration(brandConfiguration, crawlTx);

          const calculateIssueData = this.buildCalculateIssueData(crawlTx, brandConfiguration);

          const { issueStatus, firstProcess, secondProcess, totalVUI, totalAmount } = this.determineIssueStatus(
            existingTx,
            crawlTx.status,
            crawlTx.isConfirmed,
            calculateIssueData,
          );

          if (existingTx) {
            const transactionStatusHelper = new TransactionStatusHelper(crawlTx.status, crawlTx.isConfirmed);
            if (crawlTx.commission === 0 && transactionStatusHelper.isPending()) {
              this.logger.warn(`Transaction ${crawlTx.conversionId} skipped due to zero commission.`);
              await this.delete(crawlTx.conversionId);

              this.eventEmitter.emit(AppEvent.AffiliateTransactionDeleted, {
                transactionId: crawlTx.conversionId,
                userId: crawlTx.utmContent,
              });
              continue;
            }
            await this.handleExistingTransaction(crawlTx, existingTx, issueStatus, firstProcess, secondProcess, totalVUI, totalAmount);
          } else {
            if (crawlTx.commission > 0) {
              await this.handleNewTransaction(crawlTx, brandConfiguration, firstProcess, secondProcess, issueStatus, totalVUI, totalAmount);

              shouldEmitEvent = true;
              eventPayload = this.buildAffiliateOrderCreatedEvent(crawlTx, brandConfiguration, totalVUI);
            } else {
              this.logger.warn(`Transaction ${crawlTx.conversionId} skipped due to zero commission on initial processing.`);
            }
          }
        } catch (error) {
          this.logger.error(`Error processing transaction ${crawlTx.conversionId}: ${error.message}`);
        } finally {
          await this.redisService.delAsync(lockKey);
          //this.logger.log(`Key ${lockKey} deleted successfully.`);
        }

        if (shouldEmitEvent && eventPayload) {
          this.eventEmitter.emit(AppEvent.AffiliateTransactionCreated, eventPayload);
        }
      }

      page++;
    }
  }

  async processPendingApprovalTransactions(batchSize = 100) {
    let page = 0;
    let hasMore = true;

    const max1stIssuedPoints = await this.getMax1stIssuedPoints();

    while (hasMore) {
      const pendingApprovalTransactions = await this.transactionModel
        .find({ status: 1, isConfirmed: 0, issueStatus: TransactionIssueStatus.ISSUING_1ST })
        .skip(page * batchSize)
        .limit(batchSize)
        .exec();

      if (!isArray(pendingApprovalTransactions) || !pendingApprovalTransactions.length) {
        hasMore = false;
        break;
      }

      for (const transaction of pendingApprovalTransactions) {
        if (isNil(transaction)) {
          continue;
        }

        const lockKey = `pending-approval:locks:transaction:${transaction.transactionId}`;
        const ttl = CACHE_TIME.THIRTY_FIVE_MINUTES; // 35 minutes

        try {
          const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, transaction.transactionId, ttl);

          if (lockAcquired) {
            await this.calculateVuiQueue.add(
              QUEUE_CONSUMER.CALCULATE_VUI,
              {
                data: { transaction: transaction, max1stIssuedPoints: max1stIssuedPoints, lock: lockKey },
              },
              {
                priority: 2,
                delay: 5000,
                removeOnComplete: true,
                removeOnFail: true,
              },
            );
          } else {
            this.logger.warn(`Transaction ${transaction.transactionId} is already being processed by another worker.`);
          }
        } catch (redisError) {
          this.logger.error(`Failed to acquire lock or add transaction ${transaction.transactionId} to queue: ${redisError}`);
        }
      }

      page++;
    }
  }

  async processApprovalTransactions(batchSize = 100) {
    let page = 0;
    let hasMore = true;

    const max1stIssuedPoints = await this.getMax1stIssuedPoints();

    while (hasMore) {
      const approvalTransactions = await this.transactionModel
        .find({ status: 1, isConfirmed: 1, issueStatus: TransactionIssueStatus.ISSUING_2ND })
        .skip(page * batchSize)
        .limit(batchSize)
        .exec();

      if (!isArray(approvalTransactions) || !approvalTransactions.length) {
        hasMore = false;
        break;
      }

      for (const transaction of approvalTransactions) {
        if (isNil(transaction)) {
          continue;
        }

        const lockKey = `approval:locks:transaction:${transaction.transactionId}`;
        const ttl = CACHE_TIME.THIRTY_FIVE_MINUTES; // 35 minutes

        try {
          const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, transaction.transactionId, ttl);

          if (lockAcquired) {
            await this.calculateVuiQueue.add(
              QUEUE_CONSUMER.CALCULATE_VUI,
              {
                data: { transaction: transaction, max1stIssuedPoints: max1stIssuedPoints, lock: lockKey },
              },
              {
                priority: 2,
                delay: 5000,
                removeOnComplete: true,
                removeOnFail: true,
              },
            );
          } else {
            this.logger.warn(`Transaction ${transaction.transactionId} is already being processed by another worker.`);
          }
        } catch (redisError) {
          this.logger.error(`Failed to acquire lock or add transaction ${transaction.transactionId} to queue: ${redisError}`);
        }
      }

      page++;
    }
  }

  async processRefundTransactions(batchSize = 100) {
    let page = 0;
    let hasMore = true;

    const max1stIssuedPoints = await this.getMax1stIssuedPoints();

    while (hasMore) {
      const refundTransactions = await this.transactionModel
        .find({ status: 3 })
        .skip(page * batchSize)
        .limit(batchSize)
        .exec();

      if (!isArray(refundTransactions) || !refundTransactions.length) {
        hasMore = false;
        break;
      }

      for (const transaction of refundTransactions) {
        if (isNil(transaction)) {
          continue;
        }

        const lockKey = `refund:locks:transaction:${transaction.transactionId}`;
        const ttl = CACHE_TIME.THIRTY_FIVE_MINUTES; // 35 minutes

        try {
          const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, transaction.transactionId, ttl);

          if (lockAcquired) {
            await this.updateStatusTransactionQueue.add(
              QUEUE_CONSUMER.UPDATE_STATUS_TRANSACTION,
              {
                data: { transactionId: transaction.transactionId, lock: lockKey },
              },
              {
                priority: 2,
                delay: 5000,
                removeOnComplete: true,
                removeOnFail: true,
              },
            );

            await this.calculateVuiQueue.add(
              QUEUE_CONSUMER.CALCULATE_VUI,
              {
                data: { transaction: transaction, max1stIssuedPoints: max1stIssuedPoints, lock: lockKey },
              },
              {
                priority: 2,
                delay: 5000,
                removeOnComplete: true,
                removeOnFail: true,
              },
            );
          } else {
            this.logger.warn(`Transaction ${transaction.transactionId} is already being processed by another worker.`);
          }
        } catch (redisError) {
          this.logger.error(`Failed to acquire lock or add transaction ${transaction.transactionId} to queue: ${redisError}`);
        }
      }

      page++;
    }
  }

  async processRejectTransactions(batchSize = 100) {
    let page = 0;
    let hasMore = true;

    while (hasMore) {
      const rejectTransactions = await this.transactionModel
        .find({ status: 2, isConfirmed: 0, issueStatus: TransactionIssueStatus.HOLD })
        .skip(page * batchSize)
        .limit(batchSize)
        .exec();

      if (!isArray(rejectTransactions) || !rejectTransactions.length) {
        hasMore = false;
        break;
      }

      for (const transaction of rejectTransactions) {
        if (isNil(transaction)) {
          continue;
        }

        const lockKey = `reject:locks:transaction:${transaction.transactionId}`;
        const ttl = CACHE_TIME.THIRTY_FIVE_MINUTES; // 35 minutes

        try {
          const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, transaction.transactionId, ttl);

          if (lockAcquired) {
            await this.updateStatusTransactionQueue.add(
              QUEUE_CONSUMER.UPDATE_STATUS_TRANSACTION,
              {
                data: { transactionId: transaction.transactionId, lock: lockKey },
              },
              {
                priority: 2,
                delay: 5000,
                removeOnComplete: true,
                removeOnFail: true,
              },
            );
          } else {
            this.logger.warn(`Transaction ${transaction.transactionId} is already being processed by another worker.`);
          }
        } catch (redisError) {
          this.logger.error(`Failed to acquire lock or add transaction ${transaction.transactionId} to queue: ${redisError}`);
        }
      }

      page++;
    }
  }

  async callback(body: TclTransactionCallBackDto) {
    const { action } = body;
    if (action !== 'reward') return;

    await this.updateRealVUIQueue.add(QUEUE_CONSUMER.UPDATE_REAL_VUI, body, { delay: 5000 });
    return 'success';
  }

  async createTransactionOperation(data: {
    action: string;
    transactionId: string;
    status: string;
    metadata: Object;
    reason?: string;
  }): Promise<TransactionOperation> {
    try {
      const operation = new this.transactionOperationModel({
        action: data.action,
        transactionId: data.transactionId,
        reason: data.reason || null,
        status: data.status,
        error: null,
        metadata: data.metadata,
      });

      const result = await operation.save();
      this.logger.log(`Transaction operation log created: ${result._id}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to create transaction operation log: ${error.message}`);
      throw error;
    }
  }

  async updateTransactionFields(data: UpdateTransactionFieldDto) {
    const { transactionIds, fields } = data;

    const transactions = await this.getTransactionByIds(transactionIds);

    for (const transaction of transactions) {
      await this.updateTransactionQueue.add(
        QUEUE_CONSUMER.UPDATE_TRANSACTION,
        {
          data: { transaction: transaction, fields: fields },
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
        },
      );
    }

    return {
      status: {
        success: true,
        code: 200,
        message: 'Update transaction successfully',
      },
    };
  }

  async processFailedTransactions(data: ProcessFailedTransactionsDto) {
    const { action, payload } = data;

    const statuses = ['ISSUE_FAILED_1ST', 'ISSUE_FAILED_2ND', 'ISSUED_1ST'];

    const transactions = await this.getTransactionByIds(payload.transactionIds, statuses);

    const max1stIssuedPoints = await this.getMax1stIssuedPoints();

    let updatedTransactions = transactions;

    if (action === TransactionOperationAction.RETRY) {
      updatedTransactions = await Promise.all(
        transactions.map(async (transaction) => {
          const user = await this.userService.getCMSUserById(transaction.userId);

          return this.update(transaction.transactionId, {
            mobile: user.mobile,
          });
        }),
      );
    }

    for (const transaction of updatedTransactions) {
      await this.transactionOperationQueue.add(
        QUEUE_CONSUMER.TRANSACTION_OPERATION,
        {
          data: {
            transaction: transaction,
            max1stIssuedPoints: max1stIssuedPoints,
            action: action,
          },
        },
        {
          removeOnComplete: true,
          removeOnFail: true,
        },
      );
    }

    return {
      status: {
        success: true,
        code: 200,
        message: 'Create operation successfully',
      },
    };
  }

  async executeTask(dto: TransactionTaskDTO) {
    const { action } = dto;

    this.logger.log(`Queueing ${action} task`);

    await this.transactionTaskQueue.add(
      QUEUE_CONSUMER.TRANSACTION_TASK,
      { action },
      {
        removeOnComplete: true,
        removeOnFail: true,
      },
    );

    return {
      status: {
        success: true,
        code: 200,
        message: `${action} task has been queued successfully`,
      },
    };
  }

  async reissueTransaction(transactionId: string, issueVUI: string) {
    const transaction = await this.getOne(transactionId);

    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }

    let coreTx1st = null;
    let coreTx2nd = null;
    if (Array.isArray(transaction.coreTransactions)) {
      coreTx1st = transaction.coreTransactions.find((tx) => tx.billNumber && tx.billNumber.includes('ISSUING_1ST'));
      coreTx2nd = transaction.coreTransactions.find((tx) => tx.billNumber && tx.billNumber.includes('ISSUING_2ND'));
    }

    if (coreTx1st && (!coreTx2nd || issueVUI === '1ST')) {
      if (coreTx1st.billAmount !== 0) {
        throw new BadRequestException('billAmount is not zero, cannot reissue 1st time');
      }
      if (transaction.firstTimeVUI <= 0) {
        throw new BadRequestException('firstTimeVUI must be greater than zero to reissue 1st time');
      }
    } else if (coreTx2nd) {
      if (coreTx2nd.billAmount !== 0) {
        throw new BadRequestException('billAmount is not zero, cannot reissue 2nd time');
      }
      if (transaction.secondTimeVUI <= 0) {
        throw new BadRequestException('secondTimeVUI must be greater than zero to reissue 2nd time');
      }
    } else {
      throw new BadRequestException('Cannot determine reissue type (ISSUING_1ST or ISSUING_2ND)');
    }

    await this.reissueVuiQueue.add(
      QUEUE_CONSUMER.REISSUE_VUI,
      {
        data: { transaction: transaction, issueVUI: issueVUI },
      },
      {
        removeOnComplete: true,
        removeOnFail: true,
      },
    );

    return {
      status: {
        success: true,
        code: 200,
        message: `Reissue transaction ${transactionId} successfully`,
      },
    };
  }
}
