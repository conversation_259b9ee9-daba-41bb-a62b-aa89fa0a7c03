import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { TransactionOperationAction, TransactionOperationStatus } from "../../common/core/constants";

export type TransactionOperationDocument = TransactionOperation & Document;

@Schema({
  timestamps: true,
})
export class TransactionOperation {
  @Prop({ type: String, enum: TransactionOperationAction, required: true })
  action: TransactionOperationAction;

  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: String, required: false, default: '' })
  reason?: string | null;

  @Prop({ type: String, enum: TransactionOperationStatus, required: true })
  status: TransactionOperationStatus;

  @Prop({ type: Object, required: false })
  metadata: Record<string, any>;
}

export const TransactionOperationSchema = SchemaFactory.createForClass(TransactionOperation);