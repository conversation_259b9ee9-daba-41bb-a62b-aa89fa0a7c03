import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { ICoreTransaction } from '../dto/update-transaction.dto';

@Schema()
export class Transaction {
  @Prop({ type: String, required: true })
  orderId: string;

  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: String, required: true })
  mobile: string;

  @Prop({ type: String, required: true })
  merchantId: string;

  @Prop({ type: String, required: true })
  merchantName: string;

  @Prop({ type: String, required: true })
  storeCode: string;

  @Prop({ type: String, required: true })
  storeName: string;

  @Prop({ type: Number, required: true, default: 0 })
  totalCommission: number;

  @Prop({ type: Number, required: false, default: 0 })
  totalAmount: number | null;

  @Prop({ type: Number, required: true, default: 0 })
  orderAmount: number;

  @Prop({ type: Number, required: false, default: 0 })
  firstAmount: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  secondAmount: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  totalVUI: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  firstTimeVUI: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  secondTimeVUI: number | null;

  @Prop({ type: String, required: false })
  issueStatus: string;

  @Prop({ type: Number, required: true, default: 0 })
  earnRate: number;

  @Prop({ type: Number, required: true, default: 0 })
  earnCommissionRate: number;

  @Prop({ type: Number, required: true, default: 0 })
  firstTimeIssuedRate: number;

  @Prop({ type: Date, required: false })
  transactionDate: Date | null;

  @Prop({ type: String, required: true })
  productId: string;

  @Prop({ type: String, required: true })
  productCategory: string;

  @Prop({ type: Number, required: true })
  productPrice: number;

  @Prop({ type: Date, required: false })
  salesTime: Date | null;

  @Prop({ type: String, required: false })
  platform: string | null;

  @Prop({ type: String, required: false })
  referrer: string;

  @Prop({ type: Date, required: true })
  clickTime: Date;

  @Prop({ type: Number, required: true })
  status: number;

  @Prop({ type: Number, required: true })
  isConfirmed: number;

  @Prop({ type: String, required: false })
  utmSource: string | null;

  @Prop({ type: String, required: false })
  utmCampaign: string | null;

  @Prop({ type: String, required: false })
  utmContent: string | null;

  @Prop({ type: String, required: false })
  utmMedium: string | null;

  @Prop({ type: Boolean, required: true, default: false })
  firstProcess: boolean;

  @Prop({ type: Boolean, required: true, default: false })
  secondProcess: boolean;

  @Prop({ required: false, type: Array, default: [] })
  coreTriggerIds: string[];

  @Prop({ type: Array, required: false, default: [] })
  coreTransactionIds: string[] | null;

  @Prop({ type: Array, required: false, default: [] })
  coreTransactions: ICoreTransaction[] | null;

  @Prop({ type: String, required: false, default: '' })
  reasonRejected: string | null;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  updatedAt: Date | null;
}

export const TransactionSchema = SchemaFactory.createForClass(Transaction);
TransactionSchema.index({ status: 1, isConfirmed: 1, issueStatus: 1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ transactionId: 1 });
TransactionSchema.index({ status: 1, isConfirmed: 1, issueStatus: 1, createdAt: 1 });
TransactionSchema.index({ createdAt: -1 });
TransactionSchema.index({ orderId: 1, transactionId: 1 }, { unique: true });
