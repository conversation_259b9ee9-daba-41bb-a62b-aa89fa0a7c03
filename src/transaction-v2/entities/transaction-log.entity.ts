import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { TransactionLogDetail, TransactionLogDetailSchema } from './transaction-log-detail.entity';
import { Types } from 'mongoose';

@Schema()
export class TransactionLog {
  @Prop({ type: String, required: true })
  userId: string;

  @Prop({ type: Number, required: false, default: 0 })
  limitVUI: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  remainingVUI: number | null;

  @Prop({ type: [TransactionLogDetailSchema], default: [] })
  transactionLogDetails: Types.DocumentArray<TransactionLogDetail>;
}

export const TransactionLogSchema = SchemaFactory.createForClass(TransactionLog);

TransactionLogSchema.index({ userId: 1 });