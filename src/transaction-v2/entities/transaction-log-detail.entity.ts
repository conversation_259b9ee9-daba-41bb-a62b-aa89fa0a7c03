import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema()
export class TransactionLogDetail {
  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: Number, required: false, default: 0 })
  firstTimeVUI: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  secondTimeVUI: number | null;

  @Prop({ type: Number, required: false, default: 0 })
  remainingVUI: number | null;

  @Prop({ type: Object, required: false, default: null })
  firstIssueDetails: {
    totalVUI: number;
    totalRequestedVUI: number;
    status: string;
    issuedVUI: number;
    earnCommissionRate: number;
    firstIssueRate: number;
    totalCommission: number;
    reason: string;
  };

  @Prop({ type: Object, required: false, default: null })
  secondIssueDetails: {
    totalVUI: number;
    totalRequestedVUI: number;
    status: string;
    issuedVUI: number;
    reason: string;
  };

  @Prop({ type: String, required: true })
  status: string;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  updatedAt: Date | null;
}

export const TransactionLogDetailSchema = SchemaFactory.createForClass(TransactionLogDetail);