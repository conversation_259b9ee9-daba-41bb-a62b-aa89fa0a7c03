import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { StatusCommon } from '../../common/core/constants';

@Schema()
export class TransactionTaskLog {
  @Prop({ type: String, required: true })
  orderId: string;

  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: String, required: true, enum: StatusCommon })
  status: StatusCommon;

  @Prop({ type: String, required: true })
  taskKey: string;

  @Prop({ type: String, required: false })
  taskMessage: string | null;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  updatedAt: Date | null;
}

export const TransactionTaskLogSchema = SchemaFactory.createForClass(TransactionTaskLog);

TransactionTaskLogSchema.index({ createdAt: -1 });