import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AccessTokenGuard } from '../common/guards/access-token.guard';
import { TransactionService } from './transaction.service';
import { TclTransactionCallBackDto } from './dto/callback-transaction.dto';
import { QueryTransactionDto } from './dto/query-transaction.dto';
import { QueryTransactionTaskLogDto } from './dto/query-transaction-task-log.dto';
import { ProcessFailedTransactionsDto } from './dto/process-failed-transactions.dto';
import { UpdateTransactionFieldDto } from './dto/update-transaction.dto';
import { TransactionTaskDTO } from './dto/transaction-task.dto';

@ApiTags('Transactions V2')
@ApiBearerAuth()
@Controller()
@UseGuards(AccessTokenGuard)
export default class TransactionController {
  constructor(private readonly transactionService: TransactionService) { }

  @Get()
  getAll(@Query() query: QueryTransactionDto) {
    return this.transactionService.list(query);
  }

  @Get('task-logs')
  getAllTaskLogs(@Query() query: QueryTransactionTaskLogDto) {
    return this.transactionService.listTaskLogs(query);
  }

  @Get(':id')
  async detail(@Param('id') id: string) {
    const transaction = await this.transactionService.getOne(id);
    return {
      data: transaction,
    };
  }

  @Get(':userId/logs')
  async getLogsByUser(@Param('userId') userId: string) {
    const transactionLog = await this.transactionService.getLogsByUser(userId);
    return {
      data: transactionLog,
    };
  }

  @Post()
  async updateTransaction(@Body() body: UpdateTransactionFieldDto) {
    return await this.transactionService.updateTransactionFields(body);
  }

  @Post('failed/actions')
  async processFailedTransactions(@Body() body: ProcessFailedTransactionsDto) {
    return await this.transactionService.processFailedTransactions(body);
  }

  @Post('/callback')
  callback(@Body() data: TclTransactionCallBackDto) {
    return this.transactionService.callback(data);
  }

  @Post('execute')
  @ApiOperation({ summary: 'Execute transaction task by action' })
  @ApiResponse({ status: 200, description: 'Task executed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid action or task execution failed' })
  async executeTask(@Body() dto: TransactionTaskDTO) {
    return await this.transactionService.executeTask(dto);
  }

  @Post('reissue')
  @ApiOperation({ summary: 'Reissue transaction' })
  @ApiResponse({ status: 200, description: 'Reissue transaction successfully' })
  async reissueTransaction(@Body() body: { transactionId: string, issueVUI: string }) {
    return await this.transactionService.reissueTransaction(body.transactionId, body.issueVUI);
  }
}
