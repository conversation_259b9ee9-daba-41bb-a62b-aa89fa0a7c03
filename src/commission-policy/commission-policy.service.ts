import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { CommissionPolicy, CommissionPolicyDocument } from "./entities/commission-policy.entity";
import { ConnectionNameEnum } from "../common/core/constants";
import { Model } from "mongoose";
import * as csvParser from 'csv-parser';
import { Readable } from 'stream';
import { QueryCommissionPolicyDto } from "./dto/query-commission-policy.dto";
import { get } from "lodash";
import { Parser } from 'json2csv';
import utils from "../common/core/utils";

@Injectable()
export class CommissionPolicyService {
  private readonly logger = new Logger(CommissionPolicyService.name);

  constructor(
    @InjectModel(CommissionPolicy.name, ConnectionNameEnum.AFFILIATE_V2)
    private commissionPolicyModel: Model<CommissionPolicyDocument>
  ) {}

  getCommissionPolicies(merchantId: string) {
    return this.commissionPolicyModel.find({ merchantId }).lean();
  }

  async findByMerchant(merchantId: string, query: QueryCommissionPolicyDto) {
    const _size = Number(get(query, 'size', 10));
    const _page = Number(get(query, 'page', 1));
    const { limit, skip } = utils.sanitizePageSize(_page, _size);
    const { size, page, ...restData } = query;
    const _query = JSON.parse(JSON.stringify(restData));

    _query['merchantId'] = merchantId;

    const [total, data] = await Promise.all([
      this.commissionPolicyModel.count(_query),
      this.commissionPolicyModel.find(_query).limit(limit).skip(skip).sort({ createdAt: -1 }).lean(),
    ]);
    return {
      data,
      meta: {
        currentPage: +_page,
        pageSize: +_size,
        totalPages: Math.ceil(total / _size),
        totalRows: total,
      }
    }
  }

  async uploadCSV(merchantId: string, fileBuffer: Buffer) {
    const newPolicies = [];
    const existingPolicies = await this.commissionPolicyModel.find({ merchantId }).lean().exec();

    const detectDelimiter = (fileContent: string): string => {
      const firstLine = fileContent.split('\n')[0];
      return firstLine.includes(';') ? ';' : ',';
    };

    const parseCSV = async (fileContent: string, delimiter: string): Promise<any[]> => {
      const rows: any[] = [];
      const stream = Readable.from(fileContent);

      return new Promise((resolve, reject) => {
        stream
          .pipe(
            csvParser({
              separator: delimiter,
              skipLines: 0,
              quote: '"',
              escape: '\\',
              headers: false,
            })
          )
          .on('data', (row) => rows.push(Object.values(row)))
          .on('end', () => resolve(rows))
          .on('error', (error) => reject(error));
      });
    };

    const fileContent = fileBuffer.toString('utf8');
    const delimiter = detectDelimiter(fileContent);

    try {
      await this.commissionPolicyModel.deleteMany({ merchantId });

      const rows = await parseCSV(fileContent, delimiter);

      const [headerRow, ...dataRows] = rows;

      const headers = headerRow.map((header: string) => header.trim());
      if (
        !headers.includes('Category Name') ||
        (!headers.includes('Commission (%)') && !headers.includes('Commission (%)'))
      ) {
        throw new Error('Invalid headers in CSV file.');
      }

      const parsedRows = dataRows.map((row: string[]) => {
        const categoryNameParts = row.slice(0, row.length - 1).join(', ').trim();
        const commissionPercent = parseFloat(row[row.length - 1]);
        return {
          'Category Name': categoryNameParts,
          'Commission (%)': commissionPercent,
        };
      });

      parsedRows.forEach((row) => {
        const categoryName = row['Category Name'];
        const commissionPercent = row['Commission (%)'];

        if (!categoryName || isNaN(commissionPercent)) {
          console.warn(`Invalid row: ${JSON.stringify(row)}`);
          return;
        }

        newPolicies.push({
          merchantId,
          name: categoryName,
          commission: commissionPercent,
        });
      });

      if (newPolicies.length === 0) {
        throw new Error('No valid new or updated data found in CSV.');
      }

      for (const policy of newPolicies) {
        await this.commissionPolicyModel.create(policy);
      }

      return { message: 'Upload successful', processedCount: newPolicies.length };
    } catch (error) {
      throw new Error(`Failed to process CSV file: ${error.message}`);
    }
  }

  async exportCSV(merchantId: string): Promise<string> {
    const policies = await this.commissionPolicyModel.find({ merchantId }).lean().exec();

    if (!policies.length) {
      throw new BadRequestException(`No data found for merchantId: ${merchantId}`);
    }

    const formattedData = policies.map((policy) => ({
      'Category name': policy.name,
      'Commission (%)': policy.commission,
    }));

    const fields = ['Category name', 'Commission (%)'];
    const opts = { fields };

    const parser = new Parser(opts);
    return parser.parse(formattedData);
  }
}