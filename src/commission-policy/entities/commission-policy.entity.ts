import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CommissionPolicyDocument = CommissionPolicy & Document;

@Schema({
  timestamps: true
})
export class CommissionPolicy {
  @Prop({ type: String, required: true })
  merchantId: string;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: Number, required: true })
  commission: number;
}

export const CommissionPolicySchema = SchemaFactory.createForClass(CommissionPolicy);

CommissionPolicySchema.index({ merchantId: 1 });
