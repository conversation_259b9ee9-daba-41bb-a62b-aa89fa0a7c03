import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConnectionNameEnum } from "../common/core/constants";
import { CommissionPolicy, CommissionPolicySchema } from "./entities/commission-policy.entity";
import { CommissionPolicyService } from "./commission-policy.service";
import { CommissionPolicyController } from "./commission-policy.controller";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: CommissionPolicy.name,
          schema: CommissionPolicySchema,
          collection: 'commission_policies'
        }
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
  ],
  controllers: [CommissionPolicyController],
  providers: [CommissionPolicyService],
  exports: [CommissionPolicyService]
})
export class CommissionPolicyModule {}