import { ApiBearerAuth } from "@nestjs/swagger";
import {
  BadRequestException,
  Controller,
  Get,
  Param,
  Post, Query, Res,
  UploadedFile, UseFilters,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";
import { Response } from 'express';
import { CommissionPolicyService } from "./commission-policy.service";
import { FileInterceptor } from "@nestjs/platform-express";
import { FileSizeExceptionFilter } from "../common/filter/file-size-exception.filter";
import { QueryCommissionPolicyDto } from "./dto/query-commission-policy.dto";
import { AccessTokenGuard } from "../common/guards/access-token.guard";

@ApiBearerAuth()
@Controller()
@UseGuards(AccessTokenGuard)
export class CommissionPolicyController {
  constructor(private readonly commissionPolicyService: CommissionPolicyService) {}

  @Get(':merchantId')
  async getPolicies(@Param('merchantId') merchantId: string, @Query() query: QueryCommissionPolicyDto) {
    return this.commissionPolicyService.findByMerchant(merchantId, query);
  }

  @Get('export/:merchantId')
  async exportCSV(@Param('merchantId') merchantId: string, @Res() res: Response) {
    const csvData = await this.commissionPolicyService.exportCSV(merchantId);

    res.header('Content-Type', 'text/csv');
    res.attachment(`commission-policies-${merchantId}.csv`);
    res.send(csvData);
  }

  @Post('upload/:merchantId')
  @UseInterceptors(FileInterceptor('file'))
  @UseFilters(FileSizeExceptionFilter)
  async uploadCSV(@Param('merchantId') merchantId: string, @UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('File upload is required');
    }

    if (file.mimetype !== 'text/csv' && file.originalname.split('.').pop() !== 'csv') {
      throw new BadRequestException('Please upload a valid CSV file');
    }

    return this.commissionPolicyService.uploadCSV(merchantId, file.buffer);
  }
}