import { HttpException, Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { get, isArray } from 'lodash';
import { tz } from 'moment-timezone';
import { ConfigService } from '@nestjs/config';
import { ConnectionNameEnum, QUEUE_PROCESSOR, QUEUE_CONSUMER, CACHE_TIME } from 'src/common/core/constants';
import { CrawlHistory } from './entities/crawl-history.entity';
import { AccessTradeApiService } from 'src/accesstrade/accesstrade-api.service';
import { CrawlTransactionData } from './entities/crawl-transaction-data.entity';
import { CreateCrawlTransactionDataDto } from './dto/create-crawl-transaction-data.dto';
import { RedisService } from '../common/services/redis.service';
import { TransactionStatusHelper } from '../common/helpers/transaction-status.helper';
import { ITransaction } from '../common/core/interfaces';
import { CrawlTransactionLog, CrawlTransactionLogDocument } from './entities/crawl-transaction-log.entity';
import { AccessTradeFakeService } from '../accesstrade-fake/accesstrade-fake.service';

@Injectable()
export class CrawlDataService {
  private readonly logger = new Logger(CrawlDataService.name);

  constructor(
    @InjectModel(CrawlHistory.name, ConnectionNameEnum.AFFILIATE_V2) private crawlHistoryModel: Model<CrawlHistory>,
    @InjectModel(CrawlTransactionData.name, ConnectionNameEnum.AFFILIATE_V2) private crawlTransactionDataModel: Model<CrawlTransactionData>,
    @InjectModel(CrawlTransactionLog.name, ConnectionNameEnum.AFFILIATE_V2) private crawlTransactionLogModel: Model<CrawlTransactionLogDocument>,
    @InjectQueue(QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API)
    private accessTradeCrawlAPIQueue: Queue,
    private readonly accesstradeApiService: AccessTradeApiService,
    private readonly redisService: RedisService,
    private readonly accessTradeFakeService: AccessTradeFakeService,
    private readonly configService: ConfigService,
  ) {}

  async getCrawlTransactionsByPage(page: number, batchSize: number) {
    try {
      return await this.crawlTransactionDataModel
        .find()
        .skip(page * batchSize)
        .limit(batchSize)
        .exec();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async processCrawlTransactionData(startDate: Date, endDate: Date, page: number | null = 1) {
    const jobId = `CRAWL_PAGE_${new Date(startDate).getTime()}_${new Date(endDate).getTime()}_PAGE_${page}`;

    const existingJob = await this.accessTradeCrawlAPIQueue.getJob(jobId);
    if (existingJob) {
      this.logger.log(`Job ${jobId} already exists. Skipping. Start Date: ${startDate}, End Date: ${endDate}.`);
      return;
    }

    await this.accessTradeCrawlAPIQueue.add(
      QUEUE_CONSUMER.CRAWL_PAGE,
      {
        data: { since: startDate, until: endDate, page },
      },
      {
        jobId,
        priority: 2,
        removeOnComplete: true,
        removeOnFail: true,
      },
    );
  }

  async processTransactionData(startDate: Date, endDate: Date) {
    const jobId = `CRAWL_DAY_${new Date(startDate).getTime()}_${new Date(endDate).getTime()}`;

    const existingJob = await this.accessTradeCrawlAPIQueue.getJob(jobId);
    if (existingJob) {
      this.logger.log(`Job ${jobId} already exists. Skipping. Start Date: ${startDate}, End Date: ${endDate}.`);
      return;
    }

    await this.accessTradeCrawlAPIQueue.add(
      QUEUE_CONSUMER.CRAWL_DAY,
      {
        data: { since: startDate, until: endDate },
      },
      {
        jobId,
        priority: 2,
        removeOnComplete: true,
        removeOnFail: true,
      },
    );
  }

  async getCrawlTransactionData(conversionId: string) {
    try {
      return await this.crawlTransactionDataModel.findOne({ conversionId }).exec();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async saveCrawlTransactionLogs(transactionId: string, orderId: string, newData: any, oldData: any, changes: string[]) {
    const logs = changes.map((field) => ({
      transactionId,
      orderId,
      fieldName: field,
      oldValue: oldData[field] ?? null,
      newValue: newData[field] ?? null,
      changedAt: new Date(),
    }));

    if (logs.length > 0) {
      await this.crawlTransactionLogModel.insertMany(logs);
    }
  }

  async upsertCrawlTransactionData(item: ITransaction, isPostback = false) {
    const lockKey = `crawl-transaction:locks:${item.conversion_id}`;
    const ttl = CACHE_TIME.FIVE_MINUTES; // 5 minutes

    const lockAcquired = await this.redisService.setKeyWithTTL(lockKey, 'crawl-data', ttl);

    if (!lockAcquired) {
      this.logger.warn(`Transaction ${item.conversion_id} is already being processed by another worker.`);
      return;
    }

    try {
      const exists = await this.getCrawlTransactionData(item.conversion_id);

      const dataToUpsert = {
        merchant: item.merchant,
        status: item.status,
        clickUrl: item.click_url,
        platform: item.conversion_platform,
        utmCampaign: item.utm_campaign,
        productCategory: item.product_category,
        utmContent: item.utm_content,
        transactionTime: item.transaction_time,
        productImage: item.product_image,
        utmSource: item.utm_source,
        transactionValue: item.transaction_value,
        reasonRejected: item.reason_rejected,
        categoryName: item.category_name,
        utmTerm: item.utm_term,
        productId: item.product_id,
        isConfirmed: item.is_confirmed,
        confirmedTime: item.confirmed_time,
        productPrice: item.product_price,
        commission: item.commission,
        customerType: item.customer_type,
        conversionId: item.conversion_id,
        utmMedium: item.utm_medium,
        productQuantity: item.product_quantity,
        clickTime: item.click_time,
        productName: item.product_name,
        transactionId: item.transaction_id,
        updatedAt: new Date(),
      };

      const fieldsToTrack: string[] = ['status', 'transactionValue', 'isConfirmed', 'productPrice', 'commission', 'productQuantity'];

      if (exists) {
        const changes = fieldsToTrack.filter(
          (field) => exists[field] !== undefined && dataToUpsert[field] !== undefined && exists[field] !== dataToUpsert[field],
        );

        if (changes.length > 0) {
          await this.saveCrawlTransactionLogs(item.conversion_id, item.transaction_id, dataToUpsert, exists, changes);
        }

        const helper = new TransactionStatusHelper(exists.status, exists.isConfirmed);
        if (helper.canTransitionTo(item.status, item.is_confirmed) || changes.length > 0) {
          const now = tz('UTC');

          const updateData: Partial<typeof dataToUpsert> = {
            status: dataToUpsert.status,
            isConfirmed: dataToUpsert.isConfirmed,
            updatedAt: dataToUpsert.updatedAt,
          };

          if (isPostback || now.diff(tz(exists.updatedAt, 'UTC').startOf('day'), 'days') >= 1) {
            updateData.commission = dataToUpsert.commission;
          }

          await this.updateCrawlTransactionData(item.conversion_id, updateData);
        }
      } else {
        const transactionAge = tz('UTC').diff(tz(item.transaction_time, 'UTC'), 'days');
        if (isPostback || transactionAge > 20) {
          await this.createCrawlTransactionData({
            ...dataToUpsert,
            createdAt: new Date(),
          });
        } else {
          this.logger.warn(
            `Transaction ${item.conversion_id} was not created by the cronjob because postback is required for creating new transactions or the transaction is too recent.`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error adding job for transaction ${item.conversion_id}: ${error.message}`);
    } finally {
      if (lockKey) {
        await this.redisService.delAsync(lockKey);
        //this.logger.log(`Key ${lockKey} deleted successfully.`);
      }
    }
  }

  async runCrawlTransactionData({ since, until, page }: { since: string; until: string; page: number }) {
    try {
      let result: { data: ITransaction[]; total: number };

      if (this.configService.get<boolean>('accesstrade.fake_data_active')) {
        result = await this.accessTradeFakeService.list({ since, until, page });
      } else {
        result = await this.accesstradeApiService.getTransactions({ since, until, page });
      }

      this.logger.log(`[QUEUE][DATA] ${since} to ${until} - Page ${page} - ${result.total} records`);

      const rawData = get(result, 'data', []);

      const data = isArray(rawData) ? rawData : [];

      if (data.length === 0) {
        this.logger.warn(`No valid data found for page ${page} between ${since} and ${until}.`);
      }

      for (const item of data) {
        await this.upsertCrawlTransactionData(item);
        this.logger.log(`[QUEUE][TRANSACTION] transaction_id = ${item.transaction_id}, conversion_id = ${item.conversion_id}`);
        this.logger.log(`[QUEUE][CRAWL_TRANSACTION] ${JSON.stringify(item)}`);
      }
    } catch (error) {
      this.logger.error(error);
    }
  }

  async createCrawlTransactionData(transactionData: CreateCrawlTransactionDataDto) {
    try {
      const crawlTransactionData = new this.crawlTransactionDataModel(transactionData);
      return await crawlTransactionData.save();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async updateCrawlTransactionData(conversionId: string, data: any) {
    try {
      const crawlTransactionData = await this.crawlTransactionDataModel.findOne({ conversionId });
      if (!crawlTransactionData) {
        this.logger.error(`Crawl transaction not found with transactionId: ${conversionId}`);
        throw new HttpException(`Crawl transaction with transactionId ${conversionId} not found`, 404);
      }
      return this.crawlTransactionDataModel.findOneAndUpdate({ conversionId }, data, { new: true }).lean();
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }
}
