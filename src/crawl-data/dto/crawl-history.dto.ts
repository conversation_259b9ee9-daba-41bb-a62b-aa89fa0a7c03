import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CrawlHistoryDto {
  @ApiProperty()
  since: Date;

  @ApiProperty()
  until: Date;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  status: string;

  @ApiProperty()
  @IsNumber()
  retryAttempts: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  error?: string | null;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  page: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  totalRecords: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}