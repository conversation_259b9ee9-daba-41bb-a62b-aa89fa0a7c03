import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCrawlOrderDataDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  atProductLink: string;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  billing?: number | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  categoryName?: string | null;

  @ApiPropertyOptional()
  @IsOptional()
  clickTime?: Date | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  clientPlatform: string;

  @ApiPropertyOptional()
  @IsOptional()
  confirmedTime?: Date | null;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  isConfirmed: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  landingPage?: string | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchant: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  orderPending: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  orderReject: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  orderApproved: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productCategory: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  productsCount: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  pubCommission: number;

  @ApiPropertyOptional()
  @IsOptional()
  salesTime?: Date | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmCampaign?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmContent?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmMedium?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmSource?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  website?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  websiteUrl?: string | null;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}