import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCrawlTransactionDataDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  merchant?: string | null;

  @ApiProperty()
  @IsNumber()
  status: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  clickUrl?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  platform?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmCampaign?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  productCategory?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmContent?: string | null;
  
  @ApiPropertyOptional()
  @IsOptional()
  transactionTime?: Date | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  productImage?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmSource?: string | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  transactionValue?: number | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  reasonRejected?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  categoryName?: string | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmTerm?: string | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  isConfirmed: number;

  @ApiPropertyOptional()
  @IsOptional()
  confirmedTime?: Date | null;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  productPrice: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  commission: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  customerType?: string | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  conversionId: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utmMedium?: string | null;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  productQuantity: number | null;

  @ApiPropertyOptional()
  @IsOptional()
  clickTime?: Date | null;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  productName?: string | null;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  transactionId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}