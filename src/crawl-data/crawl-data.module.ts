import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConnectionNameEnum, QUEUE_PROCESSOR } from "../common/core/constants";
import { CrawlDataService } from './crawl-data.service';
import { CrawlHistory, CrawlHistorySchema } from './entities/crawl-history.entity';
import { AccesstradeModule } from 'src/accesstrade/accesstrade.module';
import { CrawlTransactionData, CrawlTransactionDataSchema } from './entities/crawl-transaction-data.entity';
import { TransactionV2Module } from 'src/transaction-v2/transaction.module';
import { AffiliateConfigurationModule } from 'src/affiliate-configuration/affiliate-configuration.module';
import { CommonModule } from "../common/common.module";
import { BullModule } from "@nestjs/bull";
import { CrawlTransactionLog, CrawlTransactionLogSchema } from "./entities/crawl-transaction-log.entity";
import { AccessTradeFakeModule } from "../accesstrade-fake/accesstrade-fake.module";

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: CrawlHistory.name,
          schema: CrawlHistorySchema,
          collection: 'crawl_histories'
        },
        {
          name: CrawlTransactionData.name,
          schema: CrawlTransactionDataSchema,
          collection: 'crawl_transaction_data'
        },
        {
          name: CrawlTransactionLog.name,
          schema: CrawlTransactionLogSchema,
          collection: 'crawl_transaction_logs'
        }
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API,
      limiter: {
        max: 8,        // Maximum 8 jobs
        duration: 60000 // Per 60,000 milliseconds (1 minute)
      },
    }),
    AffiliateConfigurationModule,
    forwardRef(() => AccessTradeFakeModule),
    forwardRef(() => AccesstradeModule),
    forwardRef(() => TransactionV2Module),
    CommonModule
  ],
  providers: [CrawlDataService],
  exports: [CrawlDataService]
})
export class CrawlDataModule {}