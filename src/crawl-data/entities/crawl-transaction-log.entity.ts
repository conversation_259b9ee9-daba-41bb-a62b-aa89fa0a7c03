import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CrawlTransactionLogDocument = CrawlTransactionLog & Document;

@Schema({ timestamps: true })
export class CrawlTransactionLog {
  @Prop({ type: String, required: true })
  orderId: string;

  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: String, required: true })
  fieldName: string;

  @Prop({ type: Number, required: false })
  oldValue: string | number | null;

  @Prop({ type: Number, required: false })
  newValue: string | number | null;

  @Prop({ type: Date, required: true })
  changedAt: Date;
}

export const CrawlTransactionLogSchema = SchemaFactory.createForClass(CrawlTransactionLog);

CrawlTransactionLogSchema.index({ transactionId: 1, changedAt: -1 });
CrawlTransactionLogSchema.index({ orderId: 1 });