import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

@Schema()
export class CrawlTransactionData {
  @Prop({ type: String, required: false })
  merchant: string | null;

  @Prop({ type: Number, required: true, default: 0 })
  status: number;

  @Prop({ type: String, required: false })
  clickUrl: string | null;

  @Prop({ type: String, required: false })
  platform: string | null;

  @Prop({ type: String, required: false })
  utmCampaign: string | null;
  
  @Prop({ type: String, required: false })
  productCategory: string | null;

  @Prop({ type: String, required: false })
  utmContent: string | null;

  @Prop({ type: Date, required: false })
  transactionTime: Date | null;

  @Prop({ type: String, required: false })
  productImage: string | null;

  @Prop({ type: String, required: false })
  utmSource: string | null;

  @Prop({ type: Number, required: false })
  transactionValue: number | null;

  @Prop({ type: String, required: false })
  reasonRejected: string | null;

  @Prop({ type: String, required: false })
  categoryName: string | null;

  @Prop({ type: String, required: false })
  utmTerm: string | null;

  @Prop({ type: String, required: false })
  productId: string | null;

  @Prop({ type: Number, required: true, default: 0 })
  isConfirmed: number;

  @Prop({ type: Date, required: false })
  confirmedTime: Date | null;

  @Prop({ type: Number, required: true })
  productPrice: number;

  @Prop({ type: Number, required: true })
  commission: number;

  @Prop({ type: String, required: false })
  customerType: string | null;

  @Prop({ type: String, required: true })
  conversionId: string;

  @Prop({ type: String, required: false })
  utmMedium: string | null;

  @Prop({ type: Number, required: false, default: 0 })
  productQuantity: number | null;

  @Prop({ type: Date, required: false })
  clickTime: Date | null;

  @Prop({ type: String, required: false })
  productName: string | null;

  @Prop({ type: String, required: true })
  transactionId: string;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  updatedAt: Date | null;
}

export const CrawlTransactionDataSchema = SchemaFactory.createForClass(CrawlTransactionData);

CrawlTransactionDataSchema.index({ createdAt: 1 })
CrawlTransactionDataSchema.index({ conversionId: 1 })
CrawlTransactionDataSchema.index({ transactionId: 1, conversionId: 1 }, { unique: true })