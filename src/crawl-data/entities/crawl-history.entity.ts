import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { CrawlHistoryStatus, CrawlHistoryType } from 'src/common/core/constants';

@Schema()
export class CrawlHistory {
  @Prop({ required: true, type: Date })
  since: Date;

  @Prop({ required: true, type: Date })
  until: Date;
  
  @Prop({ type: String, required: true, enum: CrawlHistoryType })
  type: CrawlHistoryType;

  @Prop({ type: String, required: true, enum: CrawlHistoryStatus, default: CrawlHistoryStatus.IN_PROGRESS })
  status: CrawlHistoryStatus;

  @Prop({ type: Number, required: true, default: 0 })
  retryAttempts: number;

  @Prop({ type: String, required: false })
  error: string | null;

  @Prop({ type: Number, required: true })
  page: number;

  @Prop({ type: Number, required: true })
  totalRecords: number;

  @Prop({ type: Boolean, required: true, default: false })
  isCompleted: boolean;

  @Prop({ type: Date, required: true })
  createdAt: Date;

  @Prop({ type: Date, required: false })
  updatedAt: Date | null;
}

export const CrawlHistorySchema = SchemaFactory.createForClass(CrawlHistory);

CrawlHistorySchema.index({ type: 1, until: -1 })