import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';

import { AccessTokenMobileGuard } from '../common/guards/access-token-mobile.guard';
import { AffiliateMobileService } from './affiliate-mobile.service';
import { FindBrandConfigurationMobileDto } from './dto/find-brand-configuration-mobile.dto';
import { GetFieldByKeyDto } from './dto/get-field-by-key.dto';

@ApiBearerAuth()
@Controller()
@UseGuards(AccessTokenMobileGuard)
export class AffiliateMobileController {
  constructor(private readonly affiliateMobileService: AffiliateMobileService) {}

  @Get('brand')
  getBrand(@Query() query: FindBrandConfigurationMobileDto) {
    return this.affiliateMobileService.getBrand(query);
  }

  @Get('config')
  getConfig() {
    return this.affiliateMobileService.getConfig();
  }

  @Get('content/withField')
  getWithField(@Query() query: GetFieldByKeyDto) {
    return this.affiliateMobileService.getWithField(query);
  }
}
