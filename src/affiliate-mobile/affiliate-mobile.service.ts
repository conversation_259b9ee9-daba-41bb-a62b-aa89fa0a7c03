import { get } from 'lodash';

import { HttpException, Injectable, Logger } from '@nestjs/common';

import { AffiliateConfigurationService } from '../affiliate-configuration/affiliate-configuration.service';
import { BrandConfigurationService } from '../brand-configuration/brand-configuration.service';
import { UserService } from '../user/user.service';
import { FindBrandConfigurationMobileDto } from './dto/find-brand-configuration-mobile.dto';
import { GetAffiliateConfigurationMobileDto } from './dto/get-affiliate-configuration-mobile.dto';
import { GetBrandConfigurationMobileDto } from './dto/get-brand-configuration-mobile.dto';
import { GetFieldByKeyDto } from './dto/get-field-by-key.dto';
import { RedisService } from "../common/services/redis.service";
import { CACHE_TIME } from "../common/core/constants";
import { BrandConfiguration } from "../brand-configuration/entities/brand-configuration.entity";

@Injectable()
export class AffiliateMobileService {
  private readonly logger = new Logger(AffiliateMobileService.name);

  constructor(
    private readonly userService: UserService,
    private readonly brandConfigurationService: BrandConfigurationService,
    private readonly affiliateConfigurationService: AffiliateConfigurationService,
    private readonly redisService: RedisService
  ) {}

  async getBrand(query: FindBrandConfigurationMobileDto) {
    try {
      const cacheKey = `affiliate_mobile:getBrand:${JSON.stringify(query)}`;
      const cacheTags = [BrandConfiguration.name];

      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const affiliateConfiguration = await this.affiliateConfigurationService.findOne();
        if (affiliateConfiguration.activeConfiguration) {
          query.isEnabled = true;
          const brands = await this.brandConfigurationService.findAllForMobile(query);
          const data: GetBrandConfigurationMobileDto[] = await Promise.all(
            brands.map(async (brand) => {
              const url = await this.encodeMobileToLink(query.mobile, brand.affiliateLink);
              return {
                index: brand.index,
                brandCode: brand.merchantId,
                icon: brand.iconImageLink,
                name: brand.merchantName,
                desc: '',
                earnRate: brand.earnRate,
                earnRateType: 0, // 1 = upto, 1 = fixed
                earnType: brand.earnType,
                earnValue: brand.earnAmount,
                url,
                termOfUse: '',
                guide: '',
                isEnabled: brand.isEnabled,
              };
            }),
          );
          return { data };
        } else {
          this.logger.error(`Service is turned off`);
          throw new HttpException(`Service is turned off`, 503);
        }
      })
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async getConfig() {
    const cacheKey = `affiliate_mobile:getConfig`;
    const cacheTags = [BrandConfiguration.name];
    try {
      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        const affiliateConfiguration = await this.affiliateConfigurationService.findOne();
        const configForMobile: GetAffiliateConfigurationMobileDto = {
          banner: affiliateConfiguration.banner,
          maxUserCredit: affiliateConfiguration.total1stTimeIssuedPerUser,
          status: affiliateConfiguration.activeConfiguration ? 1 : 0,
          updatedAt: null,
        };
        const response = {
          data: configForMobile,
        };
        await this.redisService.setAsync(cacheKey, JSON.stringify(response), CACHE_TIME.THIRTY_MINUTES);
        return response;
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }

  async getWithField(query: GetFieldByKeyDto) {
    const cacheKey = `affiliate_mobile:getWithField:${JSON.stringify(query)}`;
    const cacheTags = [BrandConfiguration.name];

    try {
      return await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
        let content = '';

        if (query.key === 'service' && query.field.toLowerCase() === 'termAndCondition'.toLowerCase()) {
          const affiliateConfiguration = await this.affiliateConfigurationService.findOne();
          content = affiliateConfiguration.termAndCondition;
        } else if (query.key !== 'service' && query.field.toLowerCase() === 'instruction'.toLowerCase()) {
          const brandConfiguration = await this.brandConfigurationService.findById(query.key);
          content = brandConfiguration.instruction;
        }

        return {
          data: { content },
        };
      });
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(get(error, 'message', error), get(error, 'status', 400));
    }
  }


  async encodeMobileToLink(mobile: string, affiliateLink: string) {
    const cacheKey = `affiliate_mobile:encodeMobileToLink:${mobile}`;
    const cacheTags = [BrandConfiguration.name];

    const CMSUser = await this.redisService.cacheWithTags(cacheTags, cacheKey, CACHE_TIME.THIRTY_MINUTES, async () => {
      return await this.userService.getCMSUserByMobile(mobile);
    });

    const updatedUrl = affiliateLink.replace('encode_mobile', CMSUser ? CMSUser.id : '');

    if (updatedUrl.includes('encode_mobile')) {
      this.logger.error(`encode_mobile found in affiliate link`, mobile, JSON.stringify(CMSUser), affiliateLink, updatedUrl);
    }

    return updatedUrl;
  }
}
