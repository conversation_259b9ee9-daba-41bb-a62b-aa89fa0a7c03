import { Modu<PERSON> } from '@nestjs/common';
import { UserModule } from 'src/user/user.module';

import { AffiliateConfigurationModule } from '../affiliate-configuration/affiliate-configuration.module';
import { BrandConfigurationModule } from '../brand-configuration/brand-configuration.module';
import { AffiliateMobileController } from './affiliate-mobile.controller';
import { AffiliateMobileService } from './affiliate-mobile.service';
import { CommonModule } from "../common/common.module";

@Module({
  controllers: [AffiliateMobileController],
  imports: [BrandConfigurationModule, AffiliateConfigurationModule, UserModule, CommonModule],
  providers: [AffiliateMobileService],
})
export class AffiliateMobileModule {}
