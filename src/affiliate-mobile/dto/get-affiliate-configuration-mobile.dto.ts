import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsOptional, IsString, Matches } from 'class-validator';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GetAffiliateConfigurationMobileDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  status: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Matches(/(http)?s?:?(\/\/[^"']*\.(?:png|jpg|jpeg|gif|png|svg))/, {
    message: 'Affiliate banner must be a valid image URL',
  })
  banner: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  maxUserCredit: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  updatedAt: string;
}
