import { IsBoolean, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';

import { PickType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import QueryCommonDto from '../../common/core/query';
import { Type } from 'class-transformer';

export class FindBrandConfigurationMobileDto extends PickType(QueryCommonDto, ['page', 'size'] as const) {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  mobile: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  @Type(() => Boolean)
  isEnabled?: boolean;
}
