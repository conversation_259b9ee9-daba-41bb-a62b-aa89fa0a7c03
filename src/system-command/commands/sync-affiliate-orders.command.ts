import { Logger } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';
import { AffiliateMobileV5Service } from '../../affiliate-mobile-v5/affiliate-mobile-v5.service';
import { TransactionService } from '../../transaction-v2/transaction.service';

@Command({
  name: 'sync:affiliate-orders',
  description: 'Sync affiliate orders with new fields (firstAmount, secondAmount, firstAmountStatus, secondAmountStatus)',
})
export class SyncAffiliateOrdersCommand extends CommandRunner {
  private readonly logger = new Logger(SyncAffiliateOrdersCommand.name);

  constructor(private readonly affiliateMobileV5Service: AffiliateMobileV5Service, private readonly transactionService: TransactionService) {
    super();
  }

  // Custom logging method that outputs to both console and NestJS logger
  private log(message: string) {
    console.log(`[${new Date().toISOString()}] ${message}`);
    this.logger.log(message);
  }

  private error(message: string, error?: any) {
    console.error(`[${new Date().toISOString()}] ERROR: ${message}`, error || '');
    this.logger.error(message, error);
  }

  async run(): Promise<void> {
    this.log('Starting sync process for affiliate orders...');

    try {
      // Log database status before starting
      this.log('Checking affiliate orders in database...');

      const result = await this.affiliateMobileV5Service.syncOrdersWithNewFields();

      this.log('='.repeat(50));
      this.log('SYNC COMPLETED SUCCESSFULLY!');
      this.log(`Total processed: ${result.processed} orders`);
      this.log(`Total errors: ${result.errors} orders`);
      this.log('='.repeat(50));
    } catch (error) {
      this.error('Sync process failed:', error);
      throw error;
    }
  }
}
