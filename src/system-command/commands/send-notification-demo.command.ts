import { Logger } from '@nestjs/common';
import { Command, CommandRunner } from 'nest-commander';
import { NotificationService } from '../../common/services/notification.service';
import {
  NotificationAction,
  NotificationCommunicationType,
  NotificationTopic,
  NotificationType,
} from '@taptap-lyt/sdk';


@Command({ name: 'demo:notification:send', description: 'Demo - Send notification' })
export class SendNotificationDemoCommand extends CommandRunner {
  private readonly logger = new Logger(SendNotificationDemoCommand.name);

  constructor(
    private readonly notificationService: NotificationService,
  ) {
    super();
  }

  async run(): Promise<void> {
    this.logger.debug('Hello world');


    const data = await this.notificationService.sendNotification({
      body: 'Hello world',
      communicationType: NotificationCommunicationType.HAVING_NOTIFICATION,
      isNotify: false,
      mobile: '84356432506',
      topic: NotificationTopic.ONE_USER,
      userId: '36d04fe8-bdcd-43e4-b8dc-85f6590412dd',
      title: 'Demo notification',
      action: NotificationAction.CUSTOM,
      type: NotificationType.CONTENT,
      vuiPoint: "11"
    });

    this.logger.debug(data)
  }


}
