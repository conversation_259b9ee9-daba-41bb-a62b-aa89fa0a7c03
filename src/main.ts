import { ValidationPipe } from "@nestjs/common";
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';
import { ConfigService } from '@nestjs/config';
import { ApiLogger } from '@taptap-discovery/api-logger';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { HttpErrorFilter } from '@taptap-discovery/http-exception-filter';
import { getLoggerService, isElasticEnable } from './pino.config';
import { AppWorker } from "./app-worker.module";

async function bootstrap() {
  const isWorker = process.env.NODE_ENV === 'worker';

  if (isWorker) {
    await bootstrapWorker()
  } else {
    await bootstrapServer()
  }
}

async function bootstrapWorker() {
  const app = await NestFactory.create(AppWorker, {
    bufferLogs: isElasticEnable(),
  });

  app.useLogger(getLoggerService(app));

  const configService: ConfigService = app.get(ConfigService);

  await app.listen(configService.get<number>('port', 3004));
  console.log(`Application is running on: ${await app.getUrl()}`);
}

async function bootstrapServer() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: isElasticEnable(),
  });
  app.useLogger(getLoggerService(app));

  const configService: ConfigService = app.get(ConfigService);
  app.enableCors();
  app.use(cookieParser());
  app.useGlobalInterceptors(new TransformInterceptor());
  app.useGlobalFilters(new HttpErrorFilter({ enableTeamAlert: true }));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
    }),
  );
  if (configService.get<string>('api_logger') == 'true') {
    const apiLogger = new ApiLogger();
    app.use(apiLogger.httpLogger);
  }
  // mongoose.set('debug', true); //Not use in production
  const config = new DocumentBuilder().setTitle('Affiliate-v2 API').addBearerAuth().setVersion('1.0').addTag('affiliate-v2').build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(configService.get<number>('port', 3000));
  console.log(`Application is running on: ${await app.getUrl()}`);
}

bootstrap();
