import {
  <PERSON>,
  Post,
} from "@nestjs/common";
import { RedisService } from "../common/services/redis.service";
import { ApiTags } from "@nestjs/swagger";

@ApiTags('Redis')
@Controller()
export class RedisController {
  constructor(private redisService: RedisService) {}

  @Post('create')
  async createKeys() {
    await this.redisService.createKeysWithTag('test_tag', 'test_key', 10000);
    return { message: `10000 keys created successfully with tag "test_tag".` };
  }

  @Post('clear')
  async clearAllTestTag() {
    const tag = 'test_tag';

    await this.redisService.clearCacheByTags(tag);
    return { message: `Cache cleared for tag: ${tag}` };
  }
}
