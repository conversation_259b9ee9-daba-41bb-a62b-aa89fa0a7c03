import { configStub } from './../stubs/config.stub';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { MerchantService } from './merchant.service';

describe('MerchantService', () => {
  let service: MerchantService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MerchantService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              if (Object.keys(configStub()).includes(key)) {
                return configStub()[key];
              }
            }),
          },
        },
      ],
    }).compile();

    service = module.get<MerchantService>(MerchantService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
