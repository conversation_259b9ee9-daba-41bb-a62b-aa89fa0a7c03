import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpRequestUtils } from '@taptap-discovery/http-request-utils';
import { ConfigTokenRequestParam, RequestParams } from '@taptap-discovery/http-request-utils/lib/type';
import { IMerchant, IMerchantStore } from './merchant.interface';
import { get } from 'lodash';

@Injectable()
export class MerchantService {
  private readonly logger = new Logger(MerchantService.name);

  constructor(private readonly configService: ConfigService) {}

  private readonly request = new HttpRequestUtils();

  private sendRequest({ pathUrl = '', method = 'GET', params }: { pathUrl?: string; method?: string; params: any }) {
    const configOption: ConfigTokenRequestParam = {
      url: this.configService.get<string>('id_service.url_token'),
      method: 'POST',
      clientName: 'authentication-service',
      secret: this.configService.get<string>('id_service.secret_key'),
    };
    const options: RequestParams = {
      url: `${this.configService.get<string>('tcl.tcl_merchant_service_host')}${pathUrl}`,
      method,
      paramers: params,
    };

    return this.request.requestInternal(configOption, options);
  }

  public async validateMerchantCode(merchantCode: string) {
    const params = {
      code: merchantCode,
    };

    interface IValidateMerchant {
      status: {
        success?: boolean;
        code?: number;
        message?: string;
      };
      data: IMerchant;
    }
    const response: IValidateMerchant = await this.sendRequest({ params });

    if (!get(response, 'status.success')) {
      this.logger.error(`Error validate merchant code: ${response.status.message}`);
      throw new Error(get(response, 'status.message', 'invalid merchant code'));
    }

    return response.data;
  }

  public async validateMerchantStoreCode(merchantCode: string, storeCode: string) {
    const params = {
      page: 1,
      limit: 10,
      action: 'show',
      merchantCode,
      code: storeCode
    };

    interface IValidateMerchantStore {
      status: {
        success?: boolean;
        code?: number;
        message?: string;
      };
      data: IMerchantStore[];
    }
    const response: IValidateMerchantStore = await this.sendRequest({ pathUrl: '/store', params });

    if (!get(response, 'status.success') || !response.data.find(({ code }) => code === storeCode)) {
      this.logger.error(`Store code ${storeCode} not found in merchant ${merchantCode}`);
      throw new Error(`Store code not found`);
    }

    return response.data.find(({ code }) => code === storeCode);
  }
}
