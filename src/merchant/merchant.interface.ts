export class IMerchantContact {
  email: string;
  phone: string;
  website: string;
  zaloOA: string;
  fanpage: string;
  fanpageID?: string | null;
}

export class IMerchant {
  id: string;
  name: string;
  code: string;
  backgroundColor: string;
  logo: string;
  banner: string;
  description: string;
  note: string;
  popular: string;
  collections: string[];
  images: string[];
  contact: IMerchantContact;
  isShowOnlineStore: boolean;
  isShowEarnModel: boolean;
  onlineStore: IMerchantContact;
  isEnableOfflineStore: boolean;
  earnRate: number;
  earnRateDescription?: string;
  status: string;
  isUsingBC: boolean;
  earnRateImage?: any;
  homeMerchantDescription?: string;
  offlineStoreQuantity: 0;
  earnModels?: any;
  index: number;
  offlineStores?: any;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy?: any;
  updatedBy?: any;
}

export class IMerchantStore {
  id: string;
  code: string;
  name: string;
  description: string;
  website: string;
  address: string;
  phone: string;
  image: string;
  distance: number;
  merchantCode: string;
  merchantId: string;
  status: boolean;
  isDefault: boolean;
  district: string;
  city: string;
  storeType: string;
  lng: number;
  lat: number;
  collectionCodes: string;
  createdAt: string;
  updatedAt: string;
}
