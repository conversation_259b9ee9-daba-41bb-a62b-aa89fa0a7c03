import { forwardRef, Module } from '@nestjs/common';
import { AccessTradeFakeController } from './accesstrade-fake.controller';
import { AccessTradeFakeService } from './accesstrade-fake.service';
import { MongooseModule } from '@nestjs/mongoose';
import { AccessTradeFakeTransaction, AccessTradeFakeTransactionSchema } from './entities/accesstrade-fake-transaction.entity';
import { ConnectionNameEnum } from '../common/core/constants';
import { AccesstradeModule } from '../accesstrade/accesstrade.module';
import { AffiliateMobileV5Module } from '../affiliate-mobile-v5/affiliate-mobile-v5.module';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: AccessTradeFakeTransaction.name,
          schema: AccessTradeFakeTransactionSchema,
          collection: 'accesstrade_fake_transactions',
        },
      ],
      ConnectionNameEnum.AFFILIATE_V2,
    ),
    forwardRef(() => AccesstradeModule),
    AffiliateMobileV5Module,
  ],
  controllers: [AccessTradeFakeController],
  providers: [AccessTradeFakeService],
  exports: [AccessTradeFakeService],
})
export class AccessTradeFakeModule {}
