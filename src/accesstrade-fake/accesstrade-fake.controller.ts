import { ApiBearerAuth, <PERSON>pi<PERSON>peration, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { IdTokenAuthenGuard } from '../common/guards/internalAuthen.guard';
import { AccessTradeFakeService } from './accesstrade-fake.service';
import { CreateAccessTradeFakeTransactionDto } from './dto/create-accesstrade-fake-transaction.dto';
import { QueryAccessTradeFakeTransactionDto } from './dto/query-accesstrade-fake-transaction.dto';
import { UpdateAccessTradeFakeTransactionDto } from './dto/update-accesstrade-fake-transaction.dto';
import { AffiliateMobileV5Service } from '../affiliate-mobile-v5/affiliate-mobile-v5.service';

@ApiBearerAuth()
@ApiTags('accesstrade-fake')
@Controller()
@UseGuards(IdTokenAuthenGuard)
export class AccessTradeFakeController {
  constructor(private readonly accessTradeFakeService: AccessTradeFakeService, private readonly affiliateMobileV5Service: AffiliateMobileV5Service) {}

  @Get()
  async list(@Query() query: QueryAccessTradeFakeTransactionDto) {
    const data = await this.accessTradeFakeService.list(query);
    return { data };
  }

  @Post()
  async create(@Body() body: CreateAccessTradeFakeTransactionDto) {
    return this.accessTradeFakeService.create(body);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() body: UpdateAccessTradeFakeTransactionDto) {
    return this.accessTradeFakeService.update(id, body);
  }

  @Post('debug/sync-affiliate-orders')
  @ApiOperation({ summary: 'Debug endpoint for syncing affiliate orders with new fields' })
  @ApiResponse({ status: 200, description: 'Returns the result of the sync process' })
  async debugSyncAffiliateOrders() {
    try {
      console.log('🔍 Starting debug sync of affiliate orders...');

      // Add a small delay to ensure we can attach debugger
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Call the sync method
      const result = await this.affiliateMobileV5Service.syncOrdersWithNewFields();

      console.log('✅ Debug sync completed:', result);

      return {
        success: true,
        message: 'Sync process completed successfully',
        result,
      };
    } catch (error) {
      console.error('❌ Debug sync failed:', error);

      return {
        success: false,
        message: 'Sync process failed',
        error: error.message,
        stack: error.stack,
      };
    }
  }
}
