import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON>idateNested, IsOptional, IsNotEmpty } from "class-validator";
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateAccessTradeFakeTransactionDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  merchant: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  status: number;

  @ApiProperty()
  @IsString()
  update_time: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  click_url?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  conversion_platform?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utm_campaign?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  product_category?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utm_content?: string;

  @ApiProperty()
  @IsString()
  transaction_time: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  product_image?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utm_source: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  transaction_value: number;

  @ApiPropertyOptional()
  @IsOptional()
  _extra: Object;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  reason_rejected?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  category_name?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utm_term?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  product_id: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  is_confirmed: number;

  @ApiPropertyOptional()
  @IsOptional()
  confirmed_time?: Date;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  product_price: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  commission: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  customer_type?: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  conversion_id: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  utm_medium?: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  product_quantity: number;

  @ApiPropertyOptional()
  @IsOptional()
  click_time?: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  product_name?: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  transaction_id: string;
}