import { <PERSON><PERSON><PERSON>y, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";

export class QueryAccessTradeFakeTransactionDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
  })
  since: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    required: true,
  })
  until: string;

  @Transform(({ value }) => Number.parseInt(value))
  @IsNumber()
  @Min(1)
  @IsOptional()
  @ApiProperty({
    type: 'number',
    example: 1,
    required: false,
  })
  page? = 1;

  @Transform(({ value }) => Number.parseInt(value))
  @IsNumber()
  @IsOptional()
  @Min(1)
  @ApiProperty({
    type: 'number',
    default: 10,
    example: 10,
    required: false,
  })
  limit? = 100;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  transaction_id?: string[];
}