import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AccessTradeFakeTransactionDocument = AccessTradeFakeTransaction & Document;

@Schema({
  timestamps: true,
})

export class AccessTradeFakeTransaction {
  @Prop({ type: String, required: true, default: '' })
  merchant: string;

  @Prop({ type: Number, required: true, default: 0 })
  status: number;

  @Prop({ type: Date, required: true })
  update_time: Date;

  @Prop({ type: String, required: false, default: '' })
  click_url: string | null;

  @Prop({ type: String, required: false, default: '' })
  conversion_platform: string | null;

  @Prop({ type: String, required: false, default: '' })
  utm_campaign: string | null;

  @Prop({ type: String, required: false, default: '' })
  product_category: string | null;

  @Prop({ type: String, required: false, default: '' })
  utm_content: string | null;

  @Prop({ type: Date, required: true })
  transaction_time: Date;

  @Prop({ type: String, required: false, default: '' })
  product_image: string | null;

  @Prop({ type: String, required: false, default: '' })
  utm_source: string | null;

  @Prop({ type: Number, required: true, default: 0 })
  transaction_value: number;

  @Prop({ type: Object, required: false })
  _extra: Object;

  @Prop({ type: String, required: false, default: '' })
  reason_rejected: string | null;

  @Prop({ type: String, required: false, default: '' })
  category_name: string | null;

  @Prop({ type: String, required: false, default: '' })
  utm_term: string | null;

  @Prop({ type: String, required: true, default: '' })
  product_id: string;

  @Prop({ type: Number, required: true, default: 0 })
  is_confirmed: number;

  @Prop({ type: Date, required: false })
  confirmed_time: Date | null;

  @Prop({ type: Number, required: true, default: 0 })
  product_price: number;

  @Prop({ type: String, required: true })
  id: string;

  @Prop({ type: Number, required: true, default: 0 })
  commission: number;

  @Prop({ type: String, required: false, default: '' })
  customer_type: string | null;

  @Prop({ type: Number, required: true })
  conversion_id: string;

  @Prop({ type: String, required: false, default: '' })
  utm_medium: string | null;

  @Prop({ type: Number, required: true, default: 0 })
  product_quantity: number;

  @Prop({ type: Date, required: false })
  click_time: Date | null;

  @Prop({ type: String, required: false, default: '' })
  product_name: string | null;

  @Prop({ type: String, required: true, default: '' })
  transaction_id: string;
}

export const AccessTradeFakeTransactionSchema = SchemaFactory.createForClass(AccessTradeFakeTransaction);