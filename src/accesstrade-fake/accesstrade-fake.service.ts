import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { plainToInstance } from 'class-transformer';
import {
  AccessTradeFakeTransaction,
  AccessTradeFakeTransactionDocument
} from "./entities/accesstrade-fake-transaction.entity";
import { ConnectionNameEnum } from "../common/core/constants";
import { Model } from "mongoose";
import { CreateAccessTradeFakeTransactionDto } from "./dto/create-accesstrade-fake-transaction.dto";
import { QueryAccessTradeFakeTransactionDto } from "./dto/query-accesstrade-fake-transaction.dto";
import { get } from "lodash";
import utils from "../common/core/utils";
import { ITransaction } from "../common/core/interfaces";
import { PostbackService } from "../accesstrade/postback.service";
import { CreateAccesstradeTransactionDto } from "../accesstrade/dto/create-accesstrade-transaction.dto";
import { UpdateAccessTradeFakeTransactionDto } from "./dto/update-accesstrade-fake-transaction.dto";

@Injectable()
export class AccessTradeFakeService {
  private readonly logger = new Logger(AccessTradeFakeService.name);
  
  constructor(
    @InjectModel(AccessTradeFakeTransaction.name, ConnectionNameEnum.AFFILIATE_V2)
    private accesstradeFakeTransaction: Model<AccessTradeFakeTransactionDocument>,
    private readonly postbackService: PostbackService
  ) {}

  private mapToCreateAccesstradeTransactionDto(updateDto: UpdateAccessTradeFakeTransactionDto): CreateAccesstradeTransactionDto {
    return plainToInstance(CreateAccesstradeTransactionDto, {
      transactionId: updateDto.conversion_id,
      orderId: updateDto.transaction_id,
      campaignId: updateDto.utm_campaign,
      productId: updateDto.product_id,
      quantity: updateDto.product_quantity,
      productCategory: updateDto.product_category,
      productPrice: updateDto.product_price,
      reward: updateDto.commission,
      salesTime: updateDto.transaction_time,
      browser: '',
      conversionPlatform: updateDto.conversion_platform,
      ip: '',
      referrer: '',
      clickTime: updateDto.click_time,
      status: updateDto.status,
      isConfirmed: updateDto.is_confirmed,
      utmSource: updateDto.utm_source,
      utmCampaign: updateDto.utm_campaign,
      utmContent: updateDto.utm_content,
      utmMedium: updateDto.utm_medium,
      customerType: updateDto.customer_type,
      publisherLoginName: '',
    });
  };

  async list(query: QueryAccessTradeFakeTransactionDto): Promise<{ data: ITransaction[], total: number }> {
    const _size = Number(get(query, 'limit', 100));
    const _page = Number(get(query, 'page', 1));

    const { limit, skip } = utils.sanitizePageSize(_page, _size);
    const { limit: size, page, ...restData } = query;
    const _query = JSON.parse(JSON.stringify(restData));
    if (_query.since && _query.until) {
      _query['transaction_time'] = {
        $gte: new Date(_query.since),
        $lte: new Date(_query.until),
      };
      delete _query.since;
      delete _query.until;
    }

    const [total, data] = await Promise.all([
      this.accesstradeFakeTransaction.count(_query),
      this.accesstradeFakeTransaction.find(_query).limit(limit).skip(skip).sort({ transaction_time: -1 }).lean(),
    ]);

    return {
      data,
      total: total
    }
  }

  async create(createDto: CreateAccessTradeFakeTransactionDto): Promise<AccessTradeFakeTransaction> {
    const newTransaction = new this.accesstradeFakeTransaction(createDto);
    return newTransaction.save();
  }

  async update(conversionId: string, updateDto: UpdateAccessTradeFakeTransactionDto): Promise<AccessTradeFakeTransaction> {
    const updatedTransaction = await this.accesstradeFakeTransaction
      .findOneAndUpdate({ conversion_id: conversionId }, updateDto, { new: true })
      .exec();

    const createAccesstradeTransactionDto = this.mapToCreateAccesstradeTransactionDto(updateDto);

    await this.postbackService.runPostBack('', createAccesstradeTransactionDto, 'post');

    if (!updatedTransaction) {
      throw new NotFoundException(`Transaction with ID ${conversionId} not found`);
    }

    return updatedTransaction;
  }
}