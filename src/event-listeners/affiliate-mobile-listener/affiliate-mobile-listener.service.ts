import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { AffiliateMobileV5Service } from "../../affiliate-mobile-v5/affiliate-mobile-v5.service";
import { AppEvent, AppEventPayload } from "../../common/core/events";

@Injectable()
export class AffiliateMobileListenerService {
  private readonly logger = new Logger(AffiliateMobileListenerService.name);

  constructor(
    private readonly affiliateMobileV5Service: AffiliateMobileV5Service
  ) {}

  @OnEvent(AppEvent.AffiliateTransactionCreated)
  async onAffiliateTransactionCreated(payload: AppEventPayload[AppEvent.AffiliateTransactionCreated]) {
    this.logger.log(payload, AppEvent.AffiliateTransactionCreated);

    return await this.affiliateMobileV5Service.createAffiliateOrder({ ...payload });
  }

  @OnEvent(AppEvent.AffiliateTransactionUpdated)
  async onAffiliateTransactionUpdated(payload: AppEventPayload[AppEvent.AffiliateTransactionCreated]) {
    this.logger.log(payload, AppEvent.AffiliateTransactionUpdated);

    return await this.affiliateMobileV5Service.updateAffiliateOrder(payload.userId, payload.orderId, payload);
  }

  @OnEvent(AppEvent.AffiliateTransactionDeleted)
  async onAffiliateTransactionDeleted(payload: AppEventPayload[AppEvent.AffiliateTransactionDeleted]) {
    this.logger.log(payload, AppEvent.AffiliateTransactionDeleted);

    return await this.affiliateMobileV5Service.deleteAffiliateOrder(payload);
  }
}