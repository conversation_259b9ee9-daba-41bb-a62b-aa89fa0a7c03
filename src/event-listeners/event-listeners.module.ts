import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { AffiliateMobileListenerService } from "./affiliate-mobile-listener/affiliate-mobile-listener.service";
import { AffiliateMobileV5Module } from "../affiliate-mobile-v5/affiliate-mobile-v5.module";

@Module({
  imports: [EventEmitterModule, AffiliateMobileV5Module],
  providers: [AffiliateMobileListenerService]
})
export class EventListenersModule {}