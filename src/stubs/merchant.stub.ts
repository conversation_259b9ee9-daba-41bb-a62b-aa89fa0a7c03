import { IMerchant } from 'src/merchant/merchant.interface';

export const merchantStub = (): IMerchant => {
  return {
    id: '61b71397129f787420b373b6',
    name: 'Test ',
    code: 'test',
    backgroundColor: 'test',
    logo: 'https://d7efb8x1bjjxk.cloudfront.net/test/media/image/712d085266d8cbfbb8fbb09c1b8bc0cc.jpg',
    banner: 'https://d7efb8x1bjjxk.cloudfront.net/test/media/image/4296cb6a69c4860190538bb64be2edad.jpg',
    description: '',
    note: '',
    popular: 'false',
    collections: ['popular', 'entertaiment', ' '],
    images: ['https://d7efb8x1bjjxk.cloudfront.net/test/media/image/0f948da174d0a9e5492b5b13cf2b0527.jpg'],
    contact: {
      email: '',
      phone: '',
      website: '',
      zaloOA: '',
      fanpage: '',
      fanpageID: null,
    },
    isShowOnlineStore: false,
    isShowEarnModel: false,
    onlineStore: { email: '', phone: '', website: '', zaloOA: '', fanpage: '', fanpageID: null },
    isEnableOfflineStore: false,
    earnRate: 0,
    earnRateDescription: null,
    status: 'unpublished',
    isUsingBC: false,
    earnRateImage: null,
    homeMerchantDescription: null,
    offlineStoreQuantity: 0,
    earnModels: null,
    index: 9,
    offlineStores: null,
    createdAt: '2021-12-13 16:34:15',
    updatedAt: '2022-05-12 22:07:24',
    createdBy: null,
    updatedBy: null,
  };
};
