import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpRequestUtils } from '@taptap-discovery/http-request-utils';
import {
  ConfigTokenRequestParam, RequestParams
} from '@taptap-discovery/http-request-utils/lib/type';
import { RedisService } from "../common/services/redis.service";
import { CACHE_TIME } from "../common/core/constants";

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(private readonly configService: ConfigService, private readonly redisService: RedisService) { }

  private readonly request = new HttpRequestUtils();

  private generateOptions(key: string, value: string) {
    const configOption: ConfigTokenRequestParam = {
      url: this.configService.get<string>('id_service.url_token'),
      method: 'POST',
      clientName: 'authentication-service',
      secret: this.configService.get<string>('id_service.secret_key'),
    };
    const options: RequestParams = {
      url: this.configService.get<string>('tcl.tcl_user_service_host') + '/filter',
      method: 'GET',
      paramers: { [key]: value },
    };

    return { configOption, options };
  }

  async getCMSUserById(id: string) {
    const cacheKey = `cmsUser:${id}`;
    const cacheTTL = CACHE_TIME.ONE_DAY;

    const cachedUser = await this.redisService.getAsync(cacheKey);
    if (cachedUser) {
      return cachedUser
    }

    const { configOption, options } = this.generateOptions('id', id);

    const request = await this.request.requestInternal(configOption, options);

    if (request.data.length === 0) {
      this.logger.error(`User not found by id ${id}`);
      return null;
    }

    const user = request.data[0];

    await this.redisService.setAsync(cacheKey, JSON.stringify(user), cacheTTL);

    return user
  }

  async getCMSUserByMobile(mobile: string) {
    const { configOption, options } = this.generateOptions('mobile', mobile);

    const request = await this.request.requestInternal(configOption, options);

    if (request.data.length === 0) {
      this.logger.error(`User not found by mobile ${mobile}`);
      return null;
    }

    return request.data[0];
  }
}
