import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR } from '../../common/core/constants';
import { CrawlDataService } from '../../crawl-data/crawl-data.service';
import { AccessTradeApiService } from '../../accesstrade/accesstrade-api.service';
import { AccessTradeFakeService } from '../../accesstrade-fake/accesstrade-fake.service';
import { ITransaction } from '../../common/core/interfaces';
import { addDays } from 'date-fns';
import { isArray } from 'lodash';
import { ConfigService } from '@nestjs/config';

@Processor(QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API)
export class AccessTradeCrawlAPIConsumer {
  private readonly logger = new Logger(AccessTradeCrawlAPIConsumer.name);
  constructor(
    private readonly accesstradeApiService: AccessTradeApiService,
    private readonly crawlDataService: CrawlDataService,
    private readonly accessTradeFakeService: AccessTradeFakeService,
    private readonly configService: ConfigService,
  ) {}

  @Process({ name: QUEUE_CONSUMER.CRAWL_DAY })
  async handleCrawlDay(job: Job<any>) {
    const { since, until } = job.data.data;
    try {
      let result: { data: ITransaction[], total: number };

      if (this.configService.get<boolean>('accesstrade.fake_data_active')) {
        result = await this.accessTradeFakeService.list({ since, until })
      } else {
        result = await this.accesstradeApiService.getTransactions({ since, until })
      }

      if (result) {
        const { total } = result;
        const limit = 100;
        const totalPages = Math.ceil(total / limit);

        for (let i = 1; i <= totalPages; i++) {
          await this.crawlDataService.processCrawlTransactionData(since, until, i);
          this.logger.log(`[QUEUE][PAGE] ${since} to ${until} - Page ${i}`);
        }
      }

    } catch (error) {
      this.logger.error(`Error processing job: ${error}`);
    }
  }

  @Process({ name: QUEUE_CONSUMER.CRAWL_PAGE })
  async handleCrawlPage(job: Job<any>) {
    const { since, until, page } = job.data.data;
    try {
      this.logger.log('Run process transaction data');
      await this.crawlDataService.runCrawlTransactionData({ since, until, page });
    } catch (error) {
      this.logger.error(`Error processing job for page ${page}: ${error}`);
    }
  }

  @Process({ name: QUEUE_CONSUMER.ACCESSTRADE_POSTBACK })
  async handleTransactionPostback(job: Job<any>) {
    const task = job.data.data;
    try {
      const since = task.query.salesTime;
      const until = addDays(task.query.salesTime, 5);

      let results: { data: ITransaction[], total: number };

      if (this.configService.get<boolean>('accesstrade.fake_data_active')) {
        results = await this.accessTradeFakeService.list({
          transaction_id: [task.query.orderId],
          since,
          until
        });
      } else {
        results = await this.accesstradeApiService.getTransactions({
          transaction_id: [task.query.orderId],
          since,
          until
        });
      }

      if (!results || !results.data) {
        this.logger.warn(`No data returned for order ID: ${task.query.orderId}`);
        return { success: false, message: 'No transaction data found' };
      }

      const transactionData = results.data;

      if (isArray(transactionData) && transactionData.length > 0) {
        for (let i = 0; i < transactionData.length; i++) {
          const item = transactionData[i]
          await this.crawlDataService.upsertCrawlTransactionData({ ...item, commission: task.query.reward }, true);
          this.logger.log(`[QUEUE][POSTBACK_TRANSACTION] ${JSON.stringify({ ...item, commission: task.query.reward })}`);
        }
      } else {
        this.logger.warn(`No transactions found for order ID: ${task.query.orderId}`);
      }

      return { success: true, message: 'Transaction processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing transaction ${task.query.transactionId}: ${error}`);
    }
  }
}
