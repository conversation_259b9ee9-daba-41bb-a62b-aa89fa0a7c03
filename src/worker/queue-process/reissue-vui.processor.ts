import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR, TransactionIssueStatus } from 'src/common/core/constants';
import { CoreTransactionService } from 'src/core-transaction/core-transaction.service';
import { TransactionService } from 'src/transaction-v2/transaction.service';
import { UpdateTransactionDto } from 'src/transaction-v2/dto/update-transaction.dto';
import { BillType } from 'src/core-transaction/interfaces/core-transaction.interface';
import { ICoreTransactionStatus } from 'src/accesstrade/dto/update-accesstrade-order.dto';

@Processor(QUEUE_PROCESSOR.REISSUE_VUI)
export class ReissueVuiConsumer {
  private readonly logger = new Logger(ReissueVuiConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly coreTransactionService: CoreTransactionService,
  ) { }

  @Process({ name: QUEUE_CONSUMER.REISSUE_VUI })
  async handleReissueVui(job: Job<{ data: { transaction: any, issueVUI: string } }>) {
    const { transaction, issueVUI } = job.data.data;

    try {
      if (issueVUI === '1ST') {
        await this.reissue1stProcess(transaction);
      }
      if (issueVUI === '2ND') {
        await this.reissue2ndProcess(transaction);
      }
    } catch (error) {
      this.logger.error(`Error processing transaction ${transaction.transactionId}:`, error);
    }
  }

  async reissue1stProcess(transaction: UpdateTransactionDto) {
    const firstTimeVUI = transaction.firstTimeVUI;
    const coreTransactionData = this.buildCoreTransactionForReissuePointData(transaction, firstTimeVUI, true);
    const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);
    const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);
    transaction.coreTransactions.push(coreTransactionStatus);

    if (resultRequest.status.success) {
      transaction.coreTransactionIds.push(resultRequest.data.id);
      const updatedTransaction = {
        coreTransactions: transaction.coreTransactions,
        coreTransactionIds: transaction.coreTransactionIds,
      };

      await this.transactionService.update(transaction.transactionId, updatedTransaction);
    } else {
      this.logger.error(resultRequest.status.message);

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUE_FAILED_1ST,
        coreTransactions: transaction.coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
      ]);
    }
  }
  async reissue2ndProcess(transaction: UpdateTransactionDto) {
    const secondTimeVUI = transaction.secondTimeVUI;
    const coreTransactionData = this.buildCoreTransactionForReissuePointData(transaction, secondTimeVUI, false);
    const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);
    const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);
    transaction.coreTransactions.push(coreTransactionStatus);

    if (resultRequest.status.success) {
      transaction.coreTransactionIds.push(resultRequest.data.id);
      const updatedTransaction = {
        coreTransactions: transaction.coreTransactions,
        coreTransactionIds: transaction.coreTransactionIds,
      };

      await this.transactionService.update(transaction.transactionId, updatedTransaction);
    } else {
      this.logger.error(resultRequest.status.message);

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUE_FAILED_2ND,
        coreTransactions: transaction.coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
      ]);
    }
  }

  private buildCoreTransactionStatus(resultRequest: any): ICoreTransactionStatus {
    return {
      ...resultRequest.status,
      ...resultRequest.data,
      issueDate: new Date().toISOString(),
    };
  }

  buildCoreTransactionForReissuePointData(transaction: UpdateTransactionDto, billAmount: number, flag: boolean) {
    const issueStatus = flag ? TransactionIssueStatus.ISSUING_1ST : TransactionIssueStatus.ISSUING_2ND;

    return {
      type: BillType.LOYALTY,
      storeCode: transaction.storeCode,
      storeCodeOrigin: transaction.storeCode,
      brandCode: transaction.utmMedium,
      billNumber: `REISSUE_${transaction.transactionId}_${issueStatus}`,
      mobile: transaction.mobile,
      billDate: new Date().toISOString(),
      sync: false,
      billAmount,
      remarks: 'affiliate',
      customFields: [
        {
          key: 'orderId',
          value: transaction.orderId,
        },
      ],
    };
  }
}
