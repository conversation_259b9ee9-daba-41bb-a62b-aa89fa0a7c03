import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR, TransactionIssueStatus, TransactionOperationAction } from '../../common/core/constants';
import { TransactionService } from '../../transaction-v2/transaction.service';
import { CoreTransactionService } from '../../core-transaction/core-transaction.service';
import { Transaction } from '../../transaction-v2/entities/transaction.entity';
import { ICalculateIssue } from '../../common/core/interfaces';
import utils from '../../common/core/utils';
import { BillType, ICoreTransaction } from '../../core-transaction/interfaces/core-transaction.interface';
import { ICoreTransactionStatus } from '../../accesstrade/dto/update-accesstrade-order.dto';
import { TransactionStatusHelper } from '../../common/helpers/transaction-status.helper';
import { AppEvent } from '../../common/core/events';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TransactionLog } from '../../transaction-v2/entities/transaction-log.entity';
import { LeanDocument } from 'mongoose';
import { AffiliateTransactionStatusHelper } from '../../affiliate-mobile-v5/helpers/transaction-status.helper';

@Processor(QUEUE_PROCESSOR.TRANSACTION_OPERATION)
export class TransactionOperationConsumer {
  private readonly logger = new Logger(TransactionOperationConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly coreTransactionService: CoreTransactionService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @Process({ name: QUEUE_CONSUMER.TRANSACTION_OPERATION })
  async handle(job: Job<{ data: { transaction: Transaction; action: string; max1stIssuedPoints: number } }>) {
    const { transaction, action, max1stIssuedPoints } = job.data.data;

    switch (action) {
      case TransactionOperationAction.RETRY:
        await this.retry(transaction, max1stIssuedPoints);
        break;
      case TransactionOperationAction.ROLLBACK_DUPLICATE:
        await this.rollbackDuplicate(transaction);
        break;
      case TransactionOperationAction.RECALCULATE:
        await this.recalculateTransaction(transaction, max1stIssuedPoints);
        break;
      default:
        this.logger.log(`Unsupported action: ${action}`);
    }
  }

  private async retry(transaction: Transaction, max1stIssuedPoints: number) {
    const hasMissingMobile = transaction.coreTransactions.some((coreTransaction) => coreTransaction.mobile === null);

    if (hasMissingMobile) {
      if (transaction.issueStatus === 'ISSUE_FAILED_1ST') {
        await this.issuing1stRetry(transaction, max1stIssuedPoints);
      }
      if (transaction.issueStatus === 'ISSUE_FAILED_2ND') {
        await this.issuing2stRetry(transaction, max1stIssuedPoints);
      }
    }
  }

  private async issuing1stRetry(transaction, max1stIssuedPoints: number) {
    const metadata: any = {
      transactionId: transaction.transactionId,
      oldData: {},
      newData: {},
      status: '',
    };

    try {
      const calculateIssueData: ICalculateIssue = {
        earnCommissionRate: transaction.earnCommissionRate,
        earnRate: transaction.earnRate,
        firstIssueRate: transaction.firstTimeIssuedRate,
        totalCommission: transaction.totalCommission,
      };

      metadata.oldData = JSON.parse(JSON.stringify(transaction));

      const [{ data: transactionData }, transactionLog] = await Promise.all([
        this.transactionService.list({ userId: transaction.userId }),
        this.transactionService.getTransactionLog(transaction.userId),
      ]);

      const total1stIssuedPoints = transactionData.reduce(
        (total, _transaction) => (_transaction.issueStatus === TransactionIssueStatus.ISSUED_1ST ? total + _transaction.firstTimeVUI : total),
        0,
      );

      const { expectedBillPoints, billAmount, currentOrder1stIssuedPoints } = utils.calculateBillForCoreTransaction(
        max1stIssuedPoints,
        total1stIssuedPoints,
        calculateIssueData,
      );

      let firstTimeVUI = expectedBillPoints;

      let remainingVUI = transactionLog?.remainingVUI ?? max1stIssuedPoints;

      const firstIssueDetails = {
        totalVUI: transaction.totalVUI,
        totalRequestedVUI: firstTimeVUI,
        status: '',
        reason: '',
        issuedVUI: 0,
        earnCommissionRate: transaction.earnCommissionRate,
        firstIssueRate: transaction.firstTimeIssuedRate,
        totalCommission: transaction.totalCommission,
      };

      if (remainingVUI < currentOrder1stIssuedPoints) {
        firstTimeVUI = 0;
        firstIssueDetails.status = 'failed';
        firstIssueDetails.reason = 'Remaining VUI không đủ cho lần 1, chuyển toàn bộ số VUI sang lần 2.';
      } else {
        remainingVUI -= firstTimeVUI;
        firstIssueDetails.status = 'success';
        firstIssueDetails.issuedVUI = firstTimeVUI;
      }

      const coreTransactionData: ICoreTransaction = this.buildCoreTransactionForAddPointData(transaction, billAmount);

      const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);

      const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);

      transaction.coreTransactions.push(coreTransactionStatus);

      if (resultRequest.status.success) {
        transaction.coreTransactionIds.push(resultRequest.data.id);
        const updatedTransaction = {
          issueStatus: TransactionIssueStatus.ISSUED_1ST,
          firstAmount: billAmount,
          secondAmount: transaction.totalAmount - billAmount,
          firstTimeVUI: firstTimeVUI,
          secondTimeVUI: transaction.totalAmount - billAmount,
          coreTransactions: transaction.coreTransactions,
          coreTransactionIds: transaction.coreTransactionIds,
        };

        await Promise.all([
          this.transactionService.update(transaction.transactionId, updatedTransaction),
          this.transactionService.createTransactionLog(transaction.userId, {
            limitVUI: max1stIssuedPoints,
            remainingVUI: remainingVUI,
            transactionLogDetails: {
              transactionId: transaction.transactionId,
              firstTimeVUI: firstTimeVUI,
              remainingVUI: remainingVUI,
              status: 'ISSUED_1ST',
              firstIssueDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          }),
        ]);

        metadata.newData = JSON.parse(JSON.stringify(updatedTransaction));
        metadata.status = 'COMPLETED';

        const transactionStatusHelper = new TransactionStatusHelper(transaction.status, transaction.isConfirmed);

        const transactionStatus = transactionStatusHelper.isPending()
          ? 'PENDING'
          : transactionStatusHelper.isTemporarilyApproved()
          ? 'TEMP_APPROVED'
          : transactionStatusHelper.isApproved()
          ? 'APPROVED'
          : transactionStatusHelper.isRejected()
          ? 'CANCELLED'
          : transactionStatusHelper.isRefunded()
          ? 'REFUNDED'
          : 'UNKNOWN';

        this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
          orderId: transaction.orderId,
          userId: transaction.userId,
          transactions: [
            {
              transactionId: transaction.transactionId,
              status: transactionStatus,
            },
          ],
        });
      } else {
        firstIssueDetails.status = 'failed';
        firstIssueDetails.reason = resultRequest.status.message;

        this.logger.error(resultRequest.status.message);

        const updatedTransaction = {
          issueStatus: TransactionIssueStatus.ISSUE_FAILED_1ST,
          coreTransactions: transaction.coreTransactions,
        };

        await Promise.all([
          this.transactionService.update(transaction.transactionId, updatedTransaction),
          this.transactionService.createTransactionLog(transaction.userId, {
            limitVUI: max1stIssuedPoints,
            remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
            transactionLogDetails: {
              transactionId: transaction.transactionId,
              firstTimeVUI: 0,
              remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
              status: 'ISSUE_FAILED_1ST',
              firstIssueDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          }),
        ]);

        metadata.newData = JSON.parse(JSON.stringify(updatedTransaction));
        metadata.status = 'FAILED';
        metadata.error = resultRequest.status.message;
      }
    } catch (error) {
      metadata.status = 'FAILED';
      metadata.error = error.message;
    }

    await this.transactionService.createTransactionOperation({
      action: TransactionOperationAction.RETRY,
      transactionId: transaction.transactionId,
      status: metadata.status,
      metadata,
    });
  }

  async issuing2stRetry(transaction: Transaction, max1stIssuedPoints: number) {
    const metadata: any = {
      transactionId: transaction.transactionId,
      oldData: {},
      newData: {},
      status: '',
    };

    try {
      const transactionLog = await this.transactionService.getTransactionLog(transaction.userId);

      let remainingVUI = transactionLog?.remainingVUI ?? max1stIssuedPoints;

      // secondAmount field removed, using totalAmount instead
      let secondTimeVUI = transaction.totalAmount ? transaction.totalAmount - (transaction.firstTimeVUI || 0) : 0;

      const billAmount = secondTimeVUI;

      const secondIssueDetails = {
        totalVUI: transaction.totalVUI,
        totalRequestedVUI: billAmount,
        status: '',
        reason: '',
        issuedVUI: 0,
      };

      if (secondTimeVUI <= 0) {
        secondTimeVUI = 0;
        secondIssueDetails.status = 'failed';
        secondIssueDetails.reason = 'Không còn VUI để phát hành lần 2';
      }

      const coreTransactionData: ICoreTransaction = this.buildCoreTransactionForAddPointData(transaction, billAmount);

      const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);

      const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);

      transaction.coreTransactions.push(coreTransactionStatus);

      if (resultRequest.status.success) {
        transaction.coreTransactionIds.push(resultRequest.data.id);
        secondIssueDetails.status = 'success';
        secondIssueDetails.issuedVUI = billAmount;
        const updatedTransaction = {
          issueStatus: TransactionIssueStatus.DONE,
          coreTransactions: transaction.coreTransactions,
          coreTransactionIds: transaction.coreTransactionIds,
          secondTimeVUI: secondTimeVUI,
        };

        const updatedRemaining = remainingVUI + transaction.firstTimeVUI;

        await Promise.all([
          this.transactionService.update(transaction.transactionId, updatedTransaction),
          this.transactionService.createTransactionLog(transaction.userId, {
            limitVUI: max1stIssuedPoints,
            remainingVUI: updatedRemaining,
            transactionLogDetails: {
              transactionId: transaction.transactionId,
              firstTimeVUI: 0,
              secondTimeVUI: secondTimeVUI,
              remainingVUI: updatedRemaining,
              status: 'ISSUED_2ND',
              secondIssueDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          }),
        ]);

        metadata.newData = JSON.parse(JSON.stringify(updatedTransaction));
        metadata.status = 'COMPLETED';

        const transactionStatusHelper = new TransactionStatusHelper(transaction.status, transaction.isConfirmed);

        const transactionStatus = transactionStatusHelper.isPending()
          ? 'PENDING'
          : transactionStatusHelper.isTemporarilyApproved()
          ? 'TEMP_APPROVED'
          : transactionStatusHelper.isApproved()
          ? 'APPROVED'
          : transactionStatusHelper.isRejected()
          ? 'CANCELLED'
          : transactionStatusHelper.isRefunded()
          ? 'REFUNDED'
          : 'UNKNOWN';

        this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
          orderId: transaction.orderId,
          userId: transaction.userId,
          transactions: [
            {
              transactionId: transaction.transactionId,
              status: transactionStatus,
            },
          ],
        });
      } else {
        secondIssueDetails.status = 'failed';
        secondIssueDetails.reason = resultRequest.status.message;

        this.logger.error(resultRequest.status.message);
        const updatedTransaction = {
          issueStatus: TransactionIssueStatus.ISSUE_FAILED_2ND,
          coreTransactions: transaction.coreTransactions,
        };
        await Promise.all([
          this.transactionService.update(transaction.transactionId, updatedTransaction),
          this.transactionService.createTransactionLog(transaction.userId, {
            limitVUI: max1stIssuedPoints,
            remainingVUI: remainingVUI,
            transactionLogDetails: {
              transactionId: transaction.transactionId,
              firstTimeVUI: 0,
              secondTimeVUI: secondTimeVUI,
              remainingVUI: remainingVUI,
              status: 'ISSUE_FAILED_2ND',
              secondIssueDetails,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          }),
        ]);

        metadata.newData = JSON.parse(JSON.stringify(updatedTransaction));
        metadata.status = 'FAILED';
        metadata.error = resultRequest.status.message;
      }
    } catch (error) {
      metadata.status = 'FAILED';
      metadata.error = error.message;
    }

    await this.transactionService.createTransactionOperation({
      action: TransactionOperationAction.RETRY,
      transactionId: transaction.transactionId,
      status: metadata.status,
      metadata,
    });
  }

  buildCoreTransactionRecalculate(transaction: Transaction, billAmount: number): ICoreTransaction {
    return {
      type: BillType.LOYALTY,
      storeCode: transaction.storeCode,
      storeCodeOrigin: transaction.storeCode,
      brandCode: transaction.utmMedium,
      billNumber: `${transaction.transactionId}_${transaction.issueStatus}_RECALCULATE`,
      mobile: transaction.mobile,
      billDate: new Date().toISOString(),
      sync: false,
      billAmount,
      remarks: 'affiliate',
      customFields: [
        {
          key: 'orderId',
          value: transaction.orderId,
        },
      ],
    };
  }

  private buildCoreTransactionForAddPointData(transaction: Transaction, billAmount: number): ICoreTransaction {
    const issueStatusMapping: { [key: string]: string } = {
      ISSUE_FAILED_1ST: 'ISSUING_1ST',
      ISSUE_FAILED_2ND: 'ISSUING_2ND',
    };

    const issueStatus = issueStatusMapping[transaction.issueStatus] || transaction.issueStatus;

    return {
      type: BillType.LOYALTY,
      storeCode: transaction.storeCode,
      storeCodeOrigin: transaction.storeCode,
      brandCode: transaction.utmMedium,
      billNumber: `${transaction.transactionId}_${issueStatus}_RETRY`,
      mobile: transaction.mobile,
      billDate: new Date().toISOString(),
      sync: false,
      billAmount,
      remarks: 'affiliate',
      customFields: [
        {
          key: 'orderId',
          value: transaction.orderId,
        },
      ],
    };
  }

  private buildCoreTransactionStatus(resultRequest: any): ICoreTransactionStatus {
    return {
      ...resultRequest.status,
      ...resultRequest.data,
      issueDate: new Date().toISOString(),
    };
  }

  private async rollbackDuplicate(transaction: Transaction) {
    if (!transaction.coreTransactions || !Array.isArray(transaction.coreTransactions)) {
      throw new Error('coreTransactions is not valid.');
    }

    const metadata: any = {
      transactionId: transaction.transactionId,
      oldData: {},
      newData: {},
      status: '',
    };

    try {
      metadata.oldData.transaction = JSON.parse(JSON.stringify(transaction));

      const groupedByBillNumberOrNumber = transaction.coreTransactions.reduce((acc: any, coreTransaction: any) => {
        const key = coreTransaction.billNumber || coreTransaction.number;
        if (!key) {
          return acc;
        }
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(coreTransaction);
        return acc;
      }, {});

      transaction.coreTransactions = Object.values(groupedByBillNumberOrNumber).flatMap((transactions: any[]) => {
        const successTransaction = transactions.find((t) => t.success);
        if (successTransaction) {
          return [successTransaction];
        }
        return transactions;
      });

      if (transaction.issueStatus === 'ISSUE_FAILED_1ST') {
        const hasSuccessTransaction = transaction.coreTransactions.some((t) => t.success);
        if (hasSuccessTransaction) {
          transaction.issueStatus = 'ISSUED_1ST';
        }
      } else if (transaction.issueStatus === 'ISSUE_FAILED_2ND') {
        const hasSuccessTransaction = transaction.coreTransactions.some((t) => t.billNumber?.includes('_ISSUING_2ND') || t.number?.includes('_ISSUING_2ND'));
        if (hasSuccessTransaction) {
          transaction.issueStatus = 'ISSUED_2ND';
        }
      }

      const transactionLog: LeanDocument<TransactionLog> = await this.transactionService.getTransactionLog(transaction.userId);

      if (!transactionLog || !transactionLog.transactionLogDetails || !Array.isArray(transactionLog.transactionLogDetails)) {
        throw new Error('transactionLogDetails is not valid.');
      }

      metadata.oldData.transactionLog = JSON.parse(JSON.stringify(transactionLog));

      const groupedByTransactionId = transactionLog.transactionLogDetails.reduce((acc: any, detail: any) => {
        if (!acc[detail.transactionId]) {
          acc[detail.transactionId] = [];
        }
        acc[detail.transactionId].push(detail);
        return acc;
      }, {});

      transactionLog.transactionLogDetails = Object.values(groupedByTransactionId).flatMap((details: any[]) => {
        const allSecondIssueNull = details.every((detail: any) => detail.secondIssueDetails === null);

        if (allSecondIssueNull) {
          return details.filter(
            (detail: any) =>
              detail.transactionId !== transaction.transactionId || (detail.status !== 'ISSUE_FAILED_1ST' && detail.status !== 'ISSUE_FAILED_2ND'),
          );
        }

        return details;
      });

      metadata.newData.transaction = JSON.parse(JSON.stringify(transaction));
      metadata.newData.transactionLog = JSON.parse(JSON.stringify(transactionLog));
      metadata.status = 'COMPLETED';

      await Promise.all([
        this.transactionService.update(transaction.transactionId, transaction),
        this.transactionService.updateTransactionLog(transactionLog.userId, transactionLog),
      ]);
    } catch (error) {
      metadata.status = 'FAILED';
      metadata.error = error.message;
    }

    await this.transactionService.createTransactionOperation({
      action: TransactionOperationAction.ROLLBACK_DUPLICATE,
      transactionId: transaction.transactionId,
      status: metadata.status,
      metadata,
    });
  }

  private async recalculateTransaction(transaction: Transaction, max1stIssuedPoints: number) {
    if (transaction.issueStatus === 'ISSUED_1ST') {
      const metadata: any = {
        transactionId: transaction.transactionId,
        oldData: {},
        newData: {},
        status: '',
        error: '',
      };

      try {
        const transactionLog = await this.transactionService.getTransactionLog(transaction.userId);

        metadata.oldData = {
          ...transaction,
          transactionLog,
        };

        const calculateIssueData: ICalculateIssue = {
          earnCommissionRate: transaction.earnCommissionRate,
          earnRate: transaction.earnRate,
          firstIssueRate: transaction.firstTimeIssuedRate,
          totalCommission: transaction.totalCommission,
        };

        const totalVUI = utils.calculateTotalIssue(calculateIssueData, false);

        const [{ data: transactionData }] = await Promise.all([this.transactionService.list({ userId: transaction.userId })]);

        const total1stIssuedPoints = transactionData.reduce(
          (total, _transaction) => (_transaction.issueStatus === TransactionIssueStatus.ISSUED_1ST ? total + _transaction.firstTimeVUI : total),
          0,
        );

        const { expectedBillPoints, billAmount, currentOrder1stIssuedPoints } = utils.calculateBillForCoreTransaction(
          max1stIssuedPoints,
          total1stIssuedPoints,
          calculateIssueData,
        );

        let firstAmount = expectedBillPoints;
        let remainingVUI = transactionLog?.remainingVUI ?? max1stIssuedPoints;

        const firstIssueDetails = {
          totalVUI: transaction.totalVUI,
          totalRequestedVUI: firstAmount,
          status: '',
          reason: '',
          issuedVUI: 0,
          earnCommissionRate: transaction.earnCommissionRate,
          firstIssueRate: transaction.firstTimeIssuedRate,
          totalCommission: transaction.totalCommission,
        };

        if (remainingVUI < currentOrder1stIssuedPoints) {
          firstAmount = 0;
          firstIssueDetails.status = 'failed';
          firstIssueDetails.reason = 'Remaining VUI không đủ cho lần 1, chuyển toàn bộ số VUI sang lần 2.';
        } else {
          remainingVUI -= firstAmount;
          firstIssueDetails.status = 'recalculated';
          firstIssueDetails.issuedVUI = firstAmount;
        }

        const secondAmount = totalVUI - firstAmount;

        const updatedTransactionLogDetails = transactionLog.transactionLogDetails.map((log) => log);

        const updatedTransactionLog: any = {
          ...transactionLog,
          remainingVUI,
          transactionLogDetails: updatedTransactionLogDetails,
        };

        updatedTransactionLog.transactionLogDetails.push({
          transactionId: transaction.transactionId,
          firstTimeVUI: firstAmount,
          secondTimeVUI: secondAmount,
          remainingVUI,
          firstIssueDetails,
          status: 'ISSUED_1ST_RECALCULATED',
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        const coreTransactionData: ICoreTransaction = this.buildCoreTransactionRecalculate(transaction, billAmount);

        const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);

        const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);

        transaction.coreTransactions.push(coreTransactionStatus);

        if (resultRequest.status.success) {
          transaction.coreTransactionIds.push(resultRequest.data.id);
          const updatedTransaction = {
            ...transaction,
            totalAmount: totalVUI,
            firstAmount,
            secondAmount,
            firstTimeVUI: firstAmount,
            secondTimeVUI: secondAmount,
            totalVUI,
            updatedAt: new Date(),
            coreTransactions: transaction.coreTransactions,
            coreTransactionIds: transaction.coreTransactionIds,
          };

          await Promise.all([
            this.transactionService.update(transaction.transactionId, updatedTransaction),
            this.transactionService.updateTransactionLog(transaction.userId, updatedTransactionLog),
            this.transactionService.createTransactionLog(transaction.userId, {
              limitVUI: max1stIssuedPoints,
              remainingVUI,
              transactionLogDetails: {
                transactionId: transaction.transactionId,
                firstTimeVUI: firstAmount,
                secondTimeVUI: secondAmount,
                remainingVUI,
                status: 'ISSUED_1ST_RECALCULATE',
                firstIssueDetails,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            }),
          ]);

          metadata.newData = {
            ...updatedTransaction,
            transactionLog: updatedTransactionLog,
          };
          metadata.status = 'COMPLETED';
        } else {
          firstIssueDetails.status = 'failed';
          firstIssueDetails.reason = resultRequest.status.message;

          this.logger.error(resultRequest.status.message);

          const updatedTransaction = {
            issueStatus: TransactionIssueStatus.ISSUE_FAILED_1ST,
            coreTransactions: transaction.coreTransactions,
          };

          await Promise.all([
            this.transactionService.update(transaction.transactionId, updatedTransaction),
            this.transactionService.updateTransactionLog(transaction.userId, updatedTransactionLog),
            this.transactionService.createTransactionLog(transaction.userId, {
              limitVUI: max1stIssuedPoints,
              remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
              transactionLogDetails: {
                transactionId: transaction.transactionId,
                firstTimeVUI: 0,
                secondTimeVUI: 0,
                remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
                status: 'ISSUE_FAILED_1ST_RECALCULATE',
                firstIssueDetails,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
            }),
          ]);
        }
      } catch (error) {
        metadata.status = 'FAILED';
        metadata.error = error.message;
        this.logger.error(`Error in recalculateTransaction: ${error.message}`);
      }

      await this.transactionService.createTransactionOperation({
        action: TransactionOperationAction.RECALCULATE,
        transactionId: transaction.transactionId,
        status: metadata.status,
        metadata,
      });
    }
  }
}
