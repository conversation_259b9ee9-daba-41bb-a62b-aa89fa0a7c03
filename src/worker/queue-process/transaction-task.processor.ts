import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { QUEUE_PROCESSOR, QUEUE_CONSUMER, TransactionTaskAction } from '../../common/core/constants';
import { TransactionService } from '../../transaction-v2/transaction.service';

@Processor(QUEUE_PROCESSOR.TRANSACTION_TASK)
export class TransactionTaskConsumer {
  private readonly logger = new Logger(TransactionTaskConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
  ) { }

  @Process(QUEUE_CONSUMER.TRANSACTION_TASK)
  async handleTransactionTask(job: Job<{ action: TransactionTaskAction }>) {
    const { action } = job.data;

    this.logger.log(`Processing ${action} task`);

    try {
      switch (action) {
        case TransactionTaskAction.INIT:
          await this.transactionService.processInitTransactions();
          break;

        case TransactionTaskAction.REJECT:
          await this.transactionService.processRejectTransactions();
          break;

        case TransactionTaskAction.PENDING_APPROVAL:
          await this.transactionService.processPendingApprovalTransactions();
          break;

        case TransactionTaskAction.APPROVAL:
          await this.transactionService.processApprovalTransactions();
          break;

        case TransactionTaskAction.REFUND:
          await this.transactionService.processRefundTransactions();
          break;

        default:
          throw new Error(`Unsupported action: ${action}`);
      }

      return { executed: true };
    } catch (error) {
      this.logger.error(`Error executing ${action} task: ${error.message}`);
      throw error;
    }
  }
}