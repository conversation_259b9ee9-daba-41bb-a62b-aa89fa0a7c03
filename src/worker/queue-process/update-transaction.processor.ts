import { Job } from 'bull';
import { Process, Processor } from '@nestjs/bull';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR, TransactionFieldType } from '../../common/core/constants';
import { Logger } from '@nestjs/common';
import { TransactionService } from '../../transaction-v2/transaction.service';
import { CrawlDataService } from '../../crawl-data/crawl-data.service';
import { AccessTradeApiService } from '../../accesstrade/accesstrade-api.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as moment from 'moment/moment';
import { ICalculateIssue } from '../../common/core/interfaces';
import utils from '../../common/core/utils';
import { AppEvent } from '../../common/core/events';

@Processor(QUEUE_PROCESSOR.UPDATE_TRANSACTION)
export class UpdateTransactionConsumer {
  private readonly logger = new Logger(UpdateTransactionConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly crawlDataService: CrawlDataService,
    private readonly accessTradeApiService: AccessTradeApiService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @Process({ name: QUEUE_CONSUMER.UPDATE_TRANSACTION })
  async update(job: Job<{ data: any }>) {
    const { transaction, fields } = job.data.data;

    for (const field of fields) {
      switch (field) {
        case TransactionFieldType.COMMISSION:
          await this.handleCommissionUpdate(transaction);
          break;
        case TransactionFieldType.STATUS:
          await this.handleStatusUpdate(transaction);
          break;
        default:
          this.logger.warn(`Unsupported field: ${field}`);
      }
    }
  }

  private async handleStatusUpdate(transaction: any) {
    const results = await this.accessTradeApiService.getTransactions({
      since: moment(transaction.transactionDate).utc().startOf('day').subtract(2, 'days').format('YYYY-MM-DDTHH:mm:ss[Z]'),
      until: moment(transaction.transactionDate).utc().endOf('day').add(3, 'days').subtract(1, 'minute').format('YYYY-MM-DDTHH:mm:ss[Z]'),
      transaction_id: [transaction.orderId],
    });

    const { data } = results;

    const matchedTransaction = data.find((item) => parseInt(item.conversion_id) === parseInt(transaction.transactionId));

    if (!matchedTransaction) {
      this.logger.warn(`No matching transaction found for transactionId: ${transaction.transactionId}`);
      return;
    }

    await this.crawlDataService.updateCrawlTransactionData(matchedTransaction.conversion_id, {
      status: matchedTransaction.status,
      isConfirmed: matchedTransaction.is_confirmed,
    });
  }

  private async handleCommissionUpdate(transaction: any) {
    const results = await this.accessTradeApiService.getTransactions({
      since: moment(transaction.transactionDate).utc().startOf('day').subtract(2, 'days').format('YYYY-MM-DDTHH:mm:ss[Z]'),
      until: moment(transaction.transactionDate).utc().endOf('day').add(3, 'days').subtract(1, 'minute').format('YYYY-MM-DDTHH:mm:ss[Z]'),
      transaction_id: [transaction.orderId],
    });

    const { data } = results;

    const matchedTransaction = data.find((item) => parseInt(item.conversion_id) === parseInt(transaction.transactionId));

    if (!matchedTransaction) {
      this.logger.warn(`No matching transaction found for transactionId: ${transaction.transactionId}`);
      return;
    }

    await this.crawlDataService.updateCrawlTransactionData(matchedTransaction.conversion_id, { commission: matchedTransaction.commission });

    if (transaction.issueStatus === 'ISSUED_1ST') {
      const calculateIssueData: ICalculateIssue = {
        earnCommissionRate: transaction.earnCommissionRate,
        earnRate: transaction.earnRate,
        firstIssueRate: transaction.firstTimeIssuedRate,
        totalCommission: matchedTransaction.commission,
      };
      const dataUpdate = {
        totalCommission: matchedTransaction.commission,
        totalVUI: utils.calculateTotalIssue(calculateIssueData, false),
        totalAmount: utils.calculateTotalIssue(calculateIssueData, false),
      };
      await this.transactionService.update(transaction.transactionId, dataUpdate);

      const tempTransaction = { ...transaction };

      const transactionLog = await this.transactionService.getTransactionLog(tempTransaction.userId);

      if (!transactionLog) {
        return;
      }

      const transactionDetail = transactionLog.transactionLogDetails.find((detail) => detail.transactionId === tempTransaction.transactionId);

      if (!transactionDetail) {
        return;
      }

      let updatedRemainingVUI = transactionLog.remainingVUI;

      if (transactionDetail.firstTimeVUI > 0) {
        updatedRemainingVUI += transactionDetail.firstTimeVUI;
      }

      const updatedTransactionDetails = transactionLog.transactionLogDetails.filter((detail) => detail.transactionId !== tempTransaction.transactionId);

      await this.transactionService.updateTransactionLog(tempTransaction.userId, {
        transactionLogDetails: updatedTransactionDetails,
        remainingVUI: updatedRemainingVUI,
      });

      if (matchedTransaction.commission === 0) {
        this.eventEmitter.emit(AppEvent.AffiliateTransactionDeleted, {
          transactionId: tempTransaction.transactionId,
          userId: tempTransaction.userId,
        });
        await this.transactionService.delete(transaction.transactionId);
      } else {
        this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
          orderId: tempTransaction.transactionId,
          userId: tempTransaction.utmContent,
          transactions: [
            {
              transactionId: tempTransaction.conversionId,
              commission: tempTransaction.commission,
              totalVUI: dataUpdate.totalVUI,
              //firstAmount: tempTransaction.firstAmount || 0,
              //secondAmount: tempTransaction.secondAmount || 0,
              //firstAmountStatus: null,
              //secondAmountStatus: null,
              updatedAt: new Date(),
            },
          ],
        });
      }
    }
  }
}
