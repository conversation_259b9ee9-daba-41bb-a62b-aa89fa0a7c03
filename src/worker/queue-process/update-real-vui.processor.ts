import { Job } from 'bull';
import { Process, Processor } from '@nestjs/bull';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR } from "src/common/core/constants";
import { TransactionService } from "../../transaction-v2/transaction.service";
import { TclTransactionCallBackDto } from "../../transaction-v2/dto/callback-transaction.dto";

@Processor(QUEUE_PROCESSOR.UPDATE_REAL_VUI)
export class UpdateRealVuiConsumer {
  constructor(private readonly transactionService: TransactionService) {}

  @Process({ name: QUEUE_CONSUMER.UPDATE_REAL_VUI })
  async update(job: Job<TclTransactionCallBackDto>) {
    const { transactionId, triggerId, rewardResultList } = job.data.data;

    // Get all status of order what need to update fist time VUI points
    const transactions = await this.transactionService.list({ coreTransactionIds: [transactionId] });

    if (!transactions.data.length) return;

    const transaction = transactions.data[0];
    const coreTransactionIdIndex = transaction.coreTransactionIds.indexOf(transactionId);

    // Update real VUI

    const reward = rewardResultList?.find((e) => e.rewardCode === 'VUI_POINT');
    const vuiPoints = rewardResultList.length > 0 && reward ? reward.meta.vuiPoint : 0;

    if (coreTransactionIdIndex === 0) {
      if (transaction.coreTriggerIds.length === 0) {
        transaction.firstTimeVUI = vuiPoints;
      }
      if (transaction.coreTriggerIds.length > 0 && !transaction.coreTriggerIds.includes(triggerId)) {
        transaction.firstTimeVUI += vuiPoints;
      }
      transaction.coreTriggerIds.push(triggerId);
    }
    if (coreTransactionIdIndex === 1) {
      if (transaction.coreTriggerIds.indexOf(triggerId) === 0) {
        transaction.totalVUI = transaction.firstTimeVUI + vuiPoints;
      } else {
        transaction.totalVUI += vuiPoints;
      }
    }

    await this.transactionService.update(transaction.transactionId, transaction);
  }
}