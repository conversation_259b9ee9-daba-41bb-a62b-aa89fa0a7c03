import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR, TransactionIssueStatus } from 'src/common/core/constants';
import { ICalculateIssue } from 'src/common/core/interfaces';
import utils from 'src/common/core/utils';
import { BillType, ICoreTransaction } from 'src/core-transaction/interfaces/core-transaction.interface';
import { CoreTransactionService } from 'src/core-transaction/core-transaction.service';
import { ICoreTransactionStatus } from 'src/accesstrade/dto/update-accesstrade-order.dto';
import { TransactionService } from '../../transaction-v2/transaction.service';
import { UpdateTransactionDto } from '../../transaction-v2/dto/update-transaction.dto';
import { RedisService } from '../../common/services/redis.service';
import { AppEvent } from '../../common/core/events';
import { TransactionStatusHelper } from '../../common/helpers/transaction-status.helper';

@Processor(QUEUE_PROCESSOR.CALCULATE_VUI)
export class CalculateVuiConsumer {
  private readonly logger = new Logger(CalculateVuiConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly coreTransactionService: CoreTransactionService,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
  ) { }

  @Process({ name: QUEUE_CONSUMER.CALCULATE_VUI })
  async update(job: Job<{ data: { transaction: any; max1stIssuedPoints: number; lock: any } }>) {
    const { transaction, max1stIssuedPoints, lock } = job.data.data;

    try {
      if (transaction.issueStatus === TransactionIssueStatus.ISSUING_1ST) {
        await this.issuing1stProcess(transaction, max1stIssuedPoints);
      }
      if (transaction.issueStatus === TransactionIssueStatus.ISSUING_2ND) {
        await this.issuing2stProcess(transaction, max1stIssuedPoints);
      }
      if (transaction.issueStatus === TransactionIssueStatus.REFUNDING) {
        await this.refundProcess(transaction);
      }
    } catch (error) {
      this.logger.error(`Error processing transaction ${transaction.transactionId}:`, error);
    } finally {
      if (lock) {
        await this.redisService.delAsync(lock);
        this.logger.log(`Key ${lock} deleted successfully.`);
      }
    }
  }

  /**
   * Tính toán và xử lý quá trình phát hành điểm lần 1
   * @param transaction Thông tin giao dịch
   * @param max1stIssuedPoints Số điểm tối đa cho lần phát hành đầu tiên
   * @param calculateOnly Nếu true, chỉ tính toán giá trị mà không gửi request đến core
   */
  async issuing1stProcess(transaction: UpdateTransactionDto, max1stIssuedPoints: number, calculateOnly = false) {
    const calculateIssueData: ICalculateIssue = {
      earnCommissionRate: transaction.earnCommissionRate,
      earnRate: transaction.earnRate,
      firstIssueRate: transaction.firstTimeIssuedRate,
      totalCommission: transaction.totalCommission,
    };

    const [{ data: transactionData }, transactionLog] = await Promise.all([
      this.transactionService.list({ userId: transaction.userId }),
      this.transactionService.getTransactionLog(transaction.userId),
    ]);

    const total1stIssuedPoints = transactionData.reduce(
      (total, _transaction) => (_transaction.issueStatus === TransactionIssueStatus.ISSUED_1ST ? total + _transaction.firstTimeVUI : total),
      0,
    );

    const { expectedBillPoints, billAmount, currentOrder1stIssuedPoints } = utils.calculateBillForCoreTransaction(
      max1stIssuedPoints,
      total1stIssuedPoints,
      calculateIssueData,
    );

    let firstTimeVUI = expectedBillPoints;
    let remainingVUI = transactionLog?.remainingVUI ?? max1stIssuedPoints;

    // Calculate total VUI and second time VUI
    const totalVUI = utils.calculateTotalIssue(calculateIssueData, false);
    let secondTimeVUI = totalVUI - firstTimeVUI;

    const firstIssueDetails = {
      totalVUI: totalVUI,
      totalRequestedVUI: firstTimeVUI,
      status: '',
      reason: '',
      issuedVUI: 0,
      earnCommissionRate: transaction.earnCommissionRate,
      firstIssueRate: transaction.firstTimeIssuedRate,
      totalCommission: transaction.totalCommission,
    };

    if (remainingVUI < currentOrder1stIssuedPoints) {
      firstTimeVUI = 0;
      secondTimeVUI = totalVUI; // If first time VUI is 0, all goes to second time
      firstIssueDetails.status = 'failed';
      firstIssueDetails.reason = 'Remaining VUI không đủ cho lần 1, chuyển toàn bộ số VUI sang lần 2.';
    } else {
      remainingVUI -= firstTimeVUI;
      firstIssueDetails.status = 'success';
      firstIssueDetails.issuedVUI = firstTimeVUI;
    }

    if (calculateOnly) {
      // Nếu chỉ cần tính toán giá trị firstTimeVUI mà không cần gửi request đến core, trả về kết quả tính toán
      return {
        firstTimeVUI,
        secondTimeVUI,
        totalVUI,
        remainingVUI,
        firstIssueDetails,
      };
    }

    // Nếu firstTimeVUI là 0 thì không gửi billAmount qua core, chỉ cập nhật trạng thái và log
    if (firstTimeVUI === 0) {
      firstIssueDetails.status = 'failed';
      firstIssueDetails.reason = 'firstTimeVUI = 0, không gửi billAmount qua core';

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUE_FAILED_1ST,
        coreTransactions: transaction.coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: max1stIssuedPoints,
          remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: 0,
            remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
            status: 'ISSUE_FAILED_1ST',
            firstIssueDetails,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);
      return;
    }

    // Gửi yêu cầu đến core system
    const coreTransactionData: ICoreTransaction = this.buildCoreTransactionForAddPointData(transaction, billAmount, true);
    const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);
    const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);
    transaction.coreTransactions.push(coreTransactionStatus);

    if (resultRequest.status.success) {
      transaction.coreTransactionIds.push(resultRequest.data.id);
      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUED_1ST,
        coreTransactions: transaction.coreTransactions,
        coreTransactionIds: transaction.coreTransactionIds,
        firstTimeVUI: firstTimeVUI,
        firstAmount: firstTimeVUI,
        secondTimeVUI: secondTimeVUI,
        secondAmount: secondTimeVUI,
        totalVUI: totalVUI,
        totalAmount: totalVUI,
        firstProcess: true,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: max1stIssuedPoints,
          remainingVUI: remainingVUI,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: firstTimeVUI,
            secondTimeVUI: secondTimeVUI,
            remainingVUI: remainingVUI,
            status: 'ISSUED_1ST',
            firstIssueDetails,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);

      const transactionStatusHelper = new TransactionStatusHelper(transaction.status, transaction.isConfirmed);
      const transactionStatus = transactionStatusHelper.isPending()
        ? 'PENDING'
        : transactionStatusHelper.isTemporarilyApproved()
          ? 'TEMP_APPROVED'
          : transactionStatusHelper.isApproved()
            ? 'APPROVED'
            : transactionStatusHelper.isRejected()
              ? 'CANCELLED'
              : transactionStatusHelper.isRefunded()
                ? 'REFUNDED'
                : 'UNKNOWN';

      this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
        orderId: transaction.orderId,
        userId: transaction.userId,
        transactions: [
          {
            transactionId: transaction.transactionId,
            firstAmount: transaction.firstAmount || 0,
            secondAmount: transaction.secondAmount || 0,
            firstAmountStatus: null,
            secondAmountStatus: null,
            status: transactionStatus,
          },
        ],
      });
    } else {
      firstIssueDetails.status = 'failed';
      firstIssueDetails.reason = resultRequest.status.message;

      this.logger.error(resultRequest.status.message);

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUE_FAILED_1ST,
        coreTransactions: transaction.coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: max1stIssuedPoints,
          remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: 0,
            remainingVUI: transactionLog?.remainingVUI ?? max1stIssuedPoints,
            status: 'ISSUE_FAILED_1ST',
            firstIssueDetails,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);
    }
  }

  async issuing2stProcess(transaction: UpdateTransactionDto, max1stIssuedPoints: number) {
    let firstTimeVUI = transaction.firstTimeVUI;
    let secondTimeVUI = 0;
    let totalVUI = 0;
    let remainingVUI = null;
    let transactionLog = await this.transactionService.getTransactionLog(transaction.userId);

    // Nếu giao dịch chưa xử lý lần đầu, thực hiện đầy đủ issuing1st trước (gửi request đến core)
    if (!transaction.firstProcess) {
      // Thực hiện đầy đủ quá trình issuing1st (với gửi bill đến core)
      await this.issuing1stProcess(transaction, max1stIssuedPoints, false);

      // Đọc lại transaction sau khi đã cập nhật từ quá trình issuing1st
      const updatedTransaction = await this.transactionService.getOne(transaction.transactionId);
      if (!updatedTransaction) {
        this.logger.error(`Transaction ${transaction.transactionId} not found after issuing1st process`);
        return;
      }

      // Cập nhật lại các giá trị từ giao dịch đã cập nhật
      transaction = updatedTransaction;
      firstTimeVUI = transaction.firstTimeVUI;

      // Lấy lại transaction log sau khi đã cập nhật từ issuing1st
      transactionLog = await this.transactionService.getTransactionLog(transaction.userId);
    }

    // Tính toán lại remainingVUI
    remainingVUI = transactionLog?.remainingVUI ?? max1stIssuedPoints;

    // Tính toán lại totalVUI và secondTimeVUI dựa trên commission hiện tại
    const calculateIssueData: ICalculateIssue = {
      earnCommissionRate: transaction.earnCommissionRate,
      earnRate: transaction.earnRate,
      firstIssueRate: transaction.firstTimeIssuedRate,
      totalCommission: transaction.totalCommission,
    };

    totalVUI = utils.calculateTotalIssue(calculateIssueData, false);
    secondTimeVUI = totalVUI - firstTimeVUI;

    // Cập nhật transaction logs để đồng bộ secondTimeVUI
    if (transactionLog && transactionLog.transactionLogDetails) {
      const updatedTransactionDetails = transactionLog.transactionLogDetails.map((detail) => {
        if (detail.transactionId === transaction.transactionId) {
          return {
            ...detail,
            secondTimeVUI: secondTimeVUI,
          };
        }
        return detail;
      });

      await this.transactionService.updateTransactionLog(transaction.userId, {
        transactionLogDetails: updatedTransactionDetails,
        remainingVUI: transactionLog.remainingVUI,
      });
    }

    const billAmount = secondTimeVUI;

    const secondIssueDetails = {
      totalVUI: totalVUI,
      totalRequestedVUI: billAmount,
      status: '',
      reason: '',
      issuedVUI: 0,
    };

    // Nếu secondTimeVUI là 0 thì không gửi billAmount qua core, chỉ cập nhật trạng thái và log
    if (secondTimeVUI === 0) {
      secondIssueDetails.status = 'failed';
      secondIssueDetails.reason = 'secondTimeVUI = 0, không gửi billAmount qua core';

      await this.transactionService.update(transaction.transactionId, {
        issueStatus: TransactionIssueStatus.DONE,
        secondTimeVUI: 0,
        secondAmount: 0,
      });

      await this.transactionService.createTransactionLog(transaction.userId, {
        limitVUI: max1stIssuedPoints,
        remainingVUI: remainingVUI,
        transactionLogDetails: {
          transactionId: transaction.transactionId,
          firstTimeVUI: firstTimeVUI,
          secondTimeVUI: 0,
          remainingVUI: remainingVUI,
          status: 'NO_SECOND_VUI',
          secondIssueDetails,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      });

      return;
    }

    // Gửi request đến core cho phát hành điểm lần 2
    const coreTransactionData: ICoreTransaction = this.buildCoreTransactionForAddPointData(transaction, billAmount, false);
    const resultRequest = await this.coreTransactionService.sendRequest(coreTransactionData);
    const coreTransactionStatus = this.buildCoreTransactionStatus(resultRequest);

    // Cập nhật coreTransactions
    const coreTransactions = transaction.coreTransactions || [];
    coreTransactions.push(coreTransactionStatus);

    if (resultRequest.status.success) {
      // Cập nhật coreTransactionIds
      const coreTransactionIds = transaction.coreTransactionIds || [];
      coreTransactionIds.push(resultRequest.data.id);

      secondIssueDetails.status = 'success';
      secondIssueDetails.issuedVUI = billAmount;

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.DONE,
        coreTransactions: coreTransactions,
        coreTransactionIds: coreTransactionIds,
        secondTimeVUI: secondTimeVUI,
        secondAmount: secondTimeVUI,
        totalVUI: totalVUI,
        totalAmount: totalVUI,
        secondProcess: true,
      };

      const updatedRemaining = remainingVUI + firstTimeVUI;

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: max1stIssuedPoints,
          remainingVUI: updatedRemaining,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: firstTimeVUI,
            secondTimeVUI: secondTimeVUI,
            remainingVUI: updatedRemaining,
            status: 'ISSUED_2ND',
            secondIssueDetails,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);

      const transactionStatusHelper = new TransactionStatusHelper(transaction.status, transaction.isConfirmed);
      const transactionStatus = transactionStatusHelper.isPending()
        ? 'PENDING'
        : transactionStatusHelper.isTemporarilyApproved()
          ? 'TEMP_APPROVED'
          : transactionStatusHelper.isApproved()
            ? 'APPROVED'
            : transactionStatusHelper.isRejected()
              ? 'CANCELLED'
              : transactionStatusHelper.isRefunded()
                ? 'REFUNDED'
                : 'UNKNOWN';

      this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, {
        orderId: transaction.orderId,
        userId: transaction.userId,
        transactions: [
          {
            transactionId: transaction.transactionId,
            firstAmount: transaction.firstAmount || 0,
            secondAmount: transaction.secondAmount || 0,
            firstAmountStatus: null,
            secondAmountStatus: null,
            status: transactionStatus,
          },
        ],
      });
    } else {
      secondIssueDetails.status = 'failed';
      secondIssueDetails.reason = resultRequest.status.message;

      this.logger.error(resultRequest.status.message);
      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.ISSUE_FAILED_2ND,
        coreTransactions: coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: max1stIssuedPoints,
          remainingVUI: remainingVUI,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: firstTimeVUI,
            secondTimeVUI: secondTimeVUI,
            remainingVUI: remainingVUI,
            status: 'ISSUE_FAILED_2ND',
            secondIssueDetails,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);
    }
  }

  async refundProcess(transaction: UpdateTransactionDto) {
    const refundData: ICoreTransaction = this.buildCoreTransactionForRefundData(transaction);

    const [resultRequest, transactionLog] = await Promise.all([
      this.coreTransactionService.sendRequest(refundData),
      this.transactionService.getTransactionLog(transaction.userId),
    ]);

    const coreTransactionStatus: ICoreTransactionStatus = {
      ...resultRequest.status,
      ...resultRequest.data,
      issueDate: new Date().toISOString(),
    };
    transaction.coreTransactions.push(coreTransactionStatus);

    if (resultRequest.status.success) {
      let remainingVUI = transactionLog?.remainingVUI ?? 0;
      const totalRefundedVUI = transaction.firstTimeVUI || 0;
      remainingVUI += totalRefundedVUI;

      transaction.coreTransactionIds.push(resultRequest.data.id);

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.REFUNDED,
        coreTransactions: transaction.coreTransactions,
        coreTransactionIds: transaction.coreTransactionIds,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: transactionLog?.limitVUI,
          remainingVUI: remainingVUI,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            firstTimeVUI: transaction.firstTimeVUI,
            status: 'REFUNDED',
            remainingVUI: remainingVUI,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);
    } else {
      this.logger.error(resultRequest.status.message);

      const updatedTransaction = {
        issueStatus: TransactionIssueStatus.REFUND_FAILED,
        coreTransaction: transaction.coreTransactions,
      };

      await Promise.all([
        this.transactionService.update(transaction.transactionId, updatedTransaction),
        this.transactionService.createTransactionLog(transaction.userId, {
          limitVUI: transactionLog?.limitVUI ?? 0,
          remainingVUI: transactionLog?.remainingVUI ?? 0,
          transactionLogDetails: {
            transactionId: transaction.transactionId,
            status: 'REFUND_FAILED',
            remainingVUI: transactionLog?.remainingVUI ?? 0,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        }),
      ]);
    }
  }

  private buildCoreTransactionStatus(resultRequest: any): ICoreTransactionStatus {
    return {
      ...resultRequest.status,
      ...resultRequest.data,
      issueDate: new Date().toISOString(),
    };
  }

  /**
   * Tạo data để gửi đến core transaction service
   * @param transaction Thông tin giao dịch
   * @param billAmount Số tiền/điểm của giao dịch
   */
  buildCoreTransactionForAddPointData(transaction: UpdateTransactionDto, billAmount: number, flag: boolean): ICoreTransaction {
    const issueStatus = flag ? TransactionIssueStatus.ISSUING_1ST : TransactionIssueStatus.ISSUING_2ND;

    return {
      type: BillType.LOYALTY,
      storeCode: transaction.storeCode,
      storeCodeOrigin: transaction.storeCode,
      brandCode: transaction.utmMedium,
      billNumber: `${transaction.transactionId}_${issueStatus}`,
      mobile: transaction.mobile,
      billDate: new Date().toISOString(),
      sync: false,
      billAmount,
      remarks: 'affiliate',
      customFields: [
        {
          key: 'orderId',
          value: transaction.orderId,
        },
      ],
    };
  }

  buildCoreTransactionForRefundData(transaction: UpdateTransactionDto): ICoreTransaction {
    const { brandCode, storeCode, billNumber, billAmount, mobile } = transaction && transaction.coreTransactions.length > 0 && transaction.coreTransactions[0];

    return {
      type: BillType.REFUND,
      brandCode,
      storeCode,
      billNumber,
      billAmount,
      mobile,
      customFields: [
        {
          key: 'orderId',
          value: transaction.orderId,
        },
      ],
    };
  }
}
