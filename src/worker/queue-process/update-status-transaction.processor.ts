import { Job } from 'bull';
import { Process, Processor } from '@nestjs/bull';
import { QUEUE_CONSUMER, QUEUE_PROCESSOR, TransactionIssueStatus, TransactionStatusEnum } from 'src/common/core/constants';
import { ICalculateIssue } from 'src/common/core/interfaces';
import utils from 'src/common/core/utils';
import { TransactionService } from '../../transaction-v2/transaction.service';
import { UpdateTransactionDto } from '../../transaction-v2/dto/update-transaction.dto';
import { Logger } from '@nestjs/common';
import { RedisService } from '../../common/services/redis.service';
import { TransactionStatusHelper } from '../../common/helpers/transaction-status.helper';
import { AppEvent } from '../../common/core/events';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { NotificationService } from '../../common/services/notification.service';
import { NotificationAction, NotificationCommunicationType, NotificationTopic, NotificationType } from '@taptap-lyt/sdk';

@Processor(QUEUE_PROCESSOR.UPDATE_STATUS_TRANSACTION)
export class UpdateStatusTransactionConsumer {
  private readonly logger = new Logger(UpdateStatusTransactionConsumer.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly redisService: RedisService,
    private readonly eventEmitter: EventEmitter2,
    private readonly notificationService: NotificationService,
  ) {}

  @Process({ name: QUEUE_CONSUMER.UPDATE_STATUS_TRANSACTION })
  async update(job: Job<{ data: { transactionId: string; lock: any } }>) {
    const { transactionId, lock } = job.data.data;
    let emitEvent = false;
    let eventPayload = null;
    try {
      const transaction = await this.transactionService.getOne(transactionId);
      if (transaction.issueStatus === TransactionIssueStatus.HOLD) {
        if (transaction.status === TransactionStatusEnum.REJECTED) {
          const data: UpdateTransactionDto = {
            issueStatus: TransactionIssueStatus.REJECTED,
          };

          const reasonRejected = transaction?.reasonRejected;
          const body = reasonRejected
            ? `Đơn hàng #${transaction.transactionId} tại ${transaction.merchantName} đã bị hủy tích VUI vì ${reasonRejected}`
            : `Đơn hàng #${transaction.transactionId} tại ${transaction.merchantName} đã bị hủy tích VUI`;

          await Promise.all([
            this.transactionService.update(transactionId, data),
            this.notificationService.sendNotification({
              action: NotificationAction.CUSTOM,
              userId: transaction.userId,
              mobile: transaction.mobile,
              storeCode: transaction.storeCode,
              title: 'Đơn hàng online bị hủy tích VUI',
              body: body,
              type: NotificationType.CONTENT,
              brandCode: transaction.merchantId,
              communicationType: NotificationCommunicationType.HAVING_NOTIFICATION,
              isNotify: true,
              topic: NotificationTopic.ONE_USER,
              templateCode: 'Order_Cancelled',
              ctaButton: 'Xem đơn hàng',
              ctaLink: `taptapvui://myOnlineOrder_detail?orderId=${transaction.orderId}`,
            }),
          ]);

          const transactionStatusHelper = new TransactionStatusHelper(transaction.status, transaction.isConfirmed);

          const transactionStatus = transactionStatusHelper.isPending()
            ? 'PENDING'
            : transactionStatusHelper.isTemporarilyApproved()
            ? 'TEMP_APPROVED'
            : transactionStatusHelper.isApproved()
            ? 'APPROVED'
            : transactionStatusHelper.isRejected()
            ? 'CANCELLED'
            : transactionStatusHelper.isRefunded()
            ? 'REFUNDED'
            : 'UNKNOWN';

          emitEvent = true;
          eventPayload = {
            orderId: transaction.orderId,
            userId: transaction.userId,
            transactions: [
              {
                transactionId: transaction.transactionId,
                totalVUI: 0,
                firstAmount: transaction.firstTimeVUI || 0,
                secondAmount: transaction.secondTimeVUI || 0,
                firstAmountStatus: null,
                secondAmountStatus: null,
                status: transactionStatus,
              },
            ],
          };
        } else {
          const calculateIssueData: ICalculateIssue = {
            earnCommissionRate: transaction.earnCommissionRate,
            earnRate: transaction.earnRate,
            firstIssueRate: transaction.firstTimeIssuedRate,
            totalCommission: transaction.totalCommission,
          };

          /* Update transaction issue status to issuing 1st */
          const data: UpdateTransactionDto = {
            issueStatus: TransactionIssueStatus.ISSUING_1ST,
            totalVUI: utils.calculateTotalIssue(calculateIssueData, false),
            totalAmount: utils.calculateTotalIssue(calculateIssueData, false),
          };
          await this.transactionService.update(transactionId, data);
        }
      }

      if (transaction.issueStatus === TransactionIssueStatus.ISSUED_1ST && transaction.isConfirmed === TransactionStatusEnum.TEMP_REVIEW) {
        const data: UpdateTransactionDto = {
          issueStatus: TransactionIssueStatus.ISSUING_2ND,
        };
        await this.transactionService.update(transactionId, data);
      }

      if (transaction.issueStatus === TransactionIssueStatus.ISSUED_1ST && transaction.status === TransactionStatusEnum.REFUNDED) {
        const data: UpdateTransactionDto = {
          issueStatus: TransactionIssueStatus.REFUNDING,
        };
        await this.transactionService.update(transactionId, data);
      }
    } catch (error) {
      this.logger.error(`Error processing transaction ${transactionId}:`, error);
    } finally {
      if (lock) {
        await this.redisService.delAsync(lock);
        // this.logger.log(`Key ${lock} deleted successfully.`);
      }
    }

    if (emitEvent && eventPayload) {
      this.eventEmitter.emit(AppEvent.AffiliateTransactionUpdated, eventPayload);
    }
  }
}
