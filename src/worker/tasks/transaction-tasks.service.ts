import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { TransactionService } from "../../transaction-v2/transaction.service";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class TransactionTasksService {
  private readonly logger = new Logger(TransactionTasksService.name);
  constructor(
    private readonly transactionService: TransactionService,
    private configService: ConfigService,
  ) {}

  @Cron(process.env.CRON_INIT_TRANSACTION_TIME)
  async initTransaction() {
    if (this.configService.get<boolean>('cron.cron_init_transaction_disabled')) {
      return;
    }
    this.logger.log('Cron job init transaction');
    await this.transactionService.processInitTransactions();
  }

  @Cron(process.env.CRON_REJECTED_TIME)
  async getRejectTransactions() {
    if (this.configService.get<boolean>('cron.cron_rejected_disabled')) {
      return;
    }
    this.logger.log('Cron job get reject transactions');
    await this.transactionService.processRejectTransactions();
  }

  @Cron(process.env.CRON_TEMP_APPROVED_TIME)
  async getPendingApprovalTransactions() {
    if (this.configService.get<boolean>('cron.cron_temp_approved_disabled')) {
      return;
    }
    this.logger.log('Cron job get pending approval transactions');
    await this.transactionService.processPendingApprovalTransactions();
  }

  @Cron(process.env.CRON_APPROVED_TIME)
  async getApprovalTransactions() {
    if (this.configService.get<boolean>('cron.cron_approved_disabled')) {
      return;
    }
    this.logger.log('Cron job get approval transactions');
    await this.transactionService.processApprovalTransactions();
  }

  @Cron(process.env.CRON_REFUND_TIME)
  async getRefundTransactions() {
    if (this.configService.get<boolean>('cron.cron_refund_disabled')) {
      return;
    }
    this.logger.log('Cron job get refund transactions');
    await this.transactionService.processRefundTransactions();
  }
}