import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import * as moment from 'moment-timezone';
import { CrawlDataService } from "../../crawl-data/crawl-data.service";
import { ConfigService } from "@nestjs/config";
import * as process from "node:process";

@Injectable()
export class CrawlTasksService implements OnModuleInit {
  private readonly logger = new Logger(CrawlTasksService.name);

  onModuleInit() {
    console.log('Module đã được khởi tạo.');
    // Thực hiện các tác vụ khởi tạo cần thiết
    // this.crawlTransactionData();
  }
  constructor(
    private readonly crawlDataService: CrawlDataService,
    private readonly configService: ConfigService,
  ) { }

  @Cron(process.env.CRON_CRAWL_TASK_TIME)
  async crawlTransactionData() {
    if (this.configService.get<boolean>('cron.cron_crawl_task_disable')) {
      return;
    }
    this.logger.log('Cron job crawl transaction data from AccessTrade');

    const currentDate = moment.tz('UTC');
    let threeMonthsAgo = currentDate.clone().subtract(4, 'months');

    const minDate = moment.tz('2024-11-01T00:00:00Z', 'UTC');

    if (threeMonthsAgo.isBefore(minDate)) {
      threeMonthsAgo = minDate;
    }

    const differenceInDays = currentDate.diff(threeMonthsAgo, 'days') + 1;

    for (let i = 0; i < differenceInDays; i++) {
      const todayStart = moment(threeMonthsAgo).add(i, 'days').startOf('day').toDate();
      const todayEnd = moment(threeMonthsAgo).add(i, 'days').endOf('day').toDate();
      await this.crawlDataService.processTransactionData(todayStart, todayEnd);
      this.logger.log(`[CRON][DAY] ${todayStart} to ${todayEnd}`);
    }
  }
}