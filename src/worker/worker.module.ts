import { forwardR<PERSON>, Modu<PERSON> } from "@nestjs/common";
import { BullModule } from "@nestjs/bull";
import { QUEUE_PROCESSOR } from "../common/core/constants";
import { CalculateVuiConsumer } from "./queue-process/calculate-vui.processor";
import { UpdateRealVuiConsumer } from "./queue-process/update-real-vui.processor";
import { UpdateStatusTransactionConsumer } from "./queue-process/update-status-transaction.processor";
import { CrawlTasksService } from "./tasks/crawl-tasks.service";
import { TransactionTasksService } from "./tasks/transaction-tasks.service";
import { AccesstradeModule } from "../accesstrade/accesstrade.module";
import { CrawlDataModule } from "../crawl-data/crawl-data.module";
import { TransactionV2Module } from "../transaction-v2/transaction.module";
import { AffiliateConfigurationModule } from "../affiliate-configuration/affiliate-configuration.module";
import { CoreTransactionModule } from "../core-transaction/core-transaction.module";
import { CommonModule } from "../common/common.module";

import { ConfigurationModule } from "../configuration/configuration.module";
import { AccessTradeCrawlAPIConsumer } from "./queue-process/access-trade-crawl-api.processor";
import { AccessTradeFakeModule } from "../accesstrade-fake/accesstrade-fake.module";
import { TransactionOperationConsumer } from "./queue-process/transaction-operation.processor";
import { UpdateTransactionConsumer } from './queue-process/update-transaction.processor';
import { TransactionTaskConsumer } from "./queue-process/transaction-task.processor";
import { ReissueVuiConsumer } from "./queue-process/reissue-vui.processor";

@Module({
  imports: [
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.ACCESSTRADE_CRAWL_API,
      limiter: {
        max: 8, // Maximum 8 jobs
        duration: 60000, // Per 60,000 milliseconds (1 minute)
      },
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.CALCULATE_VUI
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_REAL_VUI
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_STATUS_TRANSACTION
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.TRANSACTION_OPERATION,
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.UPDATE_TRANSACTION
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.TRANSACTION_TASK
    }),
    BullModule.registerQueue({
      name: QUEUE_PROCESSOR.REISSUE_VUI,
    }),
    ConfigurationModule,
    AccesstradeModule,
    CrawlDataModule,
    TransactionV2Module,
    AffiliateConfigurationModule,
    CoreTransactionModule,
    AccessTradeFakeModule,
    forwardRef(() => CommonModule),
  ],
  providers: [
    CalculateVuiConsumer,
    UpdateRealVuiConsumer,
    UpdateStatusTransactionConsumer,
    AccessTradeCrawlAPIConsumer,
    TransactionOperationConsumer,
    UpdateTransactionConsumer,
    TransactionTaskConsumer,
    ReissueVuiConsumer,
    CrawlTasksService,
    TransactionTasksService,
  ]
})
export class WorkerModule { }