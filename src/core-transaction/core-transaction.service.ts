import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpRequestUtils } from '@taptap-discovery/http-request-utils';
import { ConfigTokenRequestParam, RequestParams } from '@taptap-discovery/http-request-utils/lib/type';
import { ICoreTransaction } from './interfaces/core-transaction.interface';
import { get } from 'lodash';

@Injectable()
export class CoreTransactionService {
  private readonly logger = new Logger(CoreTransactionService.name);

  constructor(private readonly configService: ConfigService) {}

  private readonly request = new HttpRequestUtils();

  public async sendRequest(data: ICoreTransaction) {
    const configOption: ConfigTokenRequestParam = {
      url: this.configService.get<string>('id_service.url_token'),
      method: 'POST',
      clientName: 'authentication-service',
      secret: this.configService.get<string>('id_service.secret_key'),
    };
    const options: RequestParams = {
      url: this.configService.get<string>('tcl.tcl_transaction_service_host'),
      method: 'POST',
      data,
    };

    return this.request.requestInternal(configOption, options);
  }
}
