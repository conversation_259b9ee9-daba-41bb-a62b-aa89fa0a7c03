export interface ILineItem {
  itemId: string;
  itemCode: string;
  itemName: string;
  quantity: number;
  price: number;
  amount: number;
}

export interface ICustomField {
  key: string;
  value: string;
}

export interface ICoreTransaction {
  type: BillType;
  storeCode: string;
  storeCodeOrigin?: string;
  brandCode: string;
  billNumber: string;
  mobile: string;
  billDate?: string;
  sync?: boolean;
  billAmount: number;
  remarks?: string;
  lineItems?: ILineItem[];
  customFields?: ICustomField[];
}

export enum BillType {
  LOYALTY = 'LOYALTY',
  REFUND = 'REFUND',
}
