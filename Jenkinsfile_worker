def AGENT_LABEL     // use for seleting NODE
def TAG_VERSION     // use TAG_VERSION if the tag name is available

if (BRANCH_NAME == "prod") {
    echo "Service in branch PROD can not build. Please build service with tag."
    currentBuild.result = 'SUCCESS'
    return
} else {
    try {
        TAG_VERSION = TAG_NAME    // for PROD build
    }
    catch (error) {
        AGENT_LABEL = "AWS_EC2_SMALL"
    }
}

if ( TAG_VERSION != null ) {
    AGENT_LABEL = "AWS_EC2_LARGE"
} else {
    AGENT_LABEL = "AWS_EC2_SMALL"
}

pipeline {
    agent {
        node {
            label AGENT_LABEL
        }
    }
    options { 
        disableConcurrentBuilds()   // Disable multitask build
        buildDiscarder(logRotator(artifactDaysToKeepStr: '', artifactNumToKeepStr: '', daysToKeepStr: '5', numToKeepStr: '5'))
        skipStagesAfterUnstable()
        office365ConnectorWebhooks([
            [name: "jenkins", url: "${O365_URL_WEBHOOK}", notifyBackToNormal: true, notifyFailure: true, notifyRepeatedFailure: true, notifySuccess: true, notifyAborted: true]
        ])
    }
    environment {
        APP_NAME = "affiliate-service-v2-worker"
        VERSION = "v${env.BUILD_NUMBER}"
        GITHASH = "${sh(script: 'git rev-parse HEAD | head -c 8', returnStdout: true).trim()}"
        REPLICAS = "1"
        REPOSITORY_NAME = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com/${APP_NAME}"
        ENVIRONMENT_DEPLOY = "${ TAG_VERSION == null ? "${env.BRANCH_NAME}" : "prod" }"
        PATH_VALUES =  "${ENVIRONMENT_DEPLOY}/$APP_NAME/values.yaml"
        PATH_ARGO = "deploy-k8s/${ENVIRONMENT_DEPLOY}/$APP_NAME/argocd.yaml"
        REPOSITORY_NAME_TAG = "${ ENVIRONMENT_DEPLOY == "prod" ? "${ENVIRONMENT_DEPLOY}_${TAG_VERSION}_${env.BUILD_NUMBER}" : "${env.BRANCH_NAME}_v${GITHASH}_${env.BUILD_NUMBER}"}"
        REPODEPLOY = "${ ENVIRONMENT_DEPLOY == "prod" ? "${REPODEPLOY_REMOTE_URL}" : "${REPODEPLOY_REMOTE_DEV_URL}"}"
        NEW_BRANCH="$APP_NAME/$REPOSITORY_NAME_TAG"
    }
    stages {
        stage('Test') {
            steps {
                echo 'Testing...'
            }
        }
        stage('Build') {
            environment {
                // FILESRC = "${sh(script: 'find ./target -iname *.jar | cut -d / -f3', returnStdout: true).trim()}"
                FILESRC = "${APP_NAME}.jar"
            }
            steps {
                script {                    
                    try {
                        dir('deploy-k8s'){
                        try {
                            git branch: "$REPODEPLOY_BRANCH",
                            credentialsId: "$REPODEPLOY_CREDGIT",
                            url: "https://$REPODEPLOY",
                            changelog: false
                        }  
                        catch (error) {
                            error.printStackTrace()
                        }
                        }
                        sh '''
                            aws ecr describe-repositories --repository-names ${APP_NAME} --query "repositories[].[repositoryName]" --output text --no-cli-pager || aws ecr create-repository --repository-name ${APP_NAME} --image-scanning-configuration scanOnPush=true --output text --no-cli-pager
                            docker build -t "$APP_NAME":"$REPOSITORY_NAME_TAG" .
                            aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
                            docker tag "$APP_NAME":"$REPOSITORY_NAME_TAG" "$REPOSITORY_NAME":"$REPOSITORY_NAME_TAG"
                            docker push "$REPOSITORY_NAME":"$REPOSITORY_NAME_TAG"
                            docker rmi "$REPOSITORY_NAME":"$REPOSITORY_NAME_TAG"
                            docker rmi "$APP_NAME":"$REPOSITORY_NAME_TAG"
                        '''
                    }
                    catch(error) {
                        throw(error)
                    }
                }
            }
        }
        stage('Change image tag repository') {
            steps {
                script {
                    dir('deploy-k8s'){
                        // remote link to repo with credentials
                        withCredentials([string(credentialsId: 'GITLAB_TOKEN_CRED_ID', variable: 'GITLAB_TOKEN_CRED')]) {                   
                            sh '''
                                git remote set-url origin https://$GITLAB_TOKEN_CRED@$REPODEPLOY
                            '''
                        }
                        sh '''
                            git checkout -b $NEW_BRANCH
                            sed -i "s|repository: .*|repository: $REPOSITORY_NAME|; s|tag: .*|tag: $REPOSITORY_NAME_TAG|; s|replicaCount: .*|replicaCount: $REPLICAS|" $PATH_VALUES
                            git add .
                            git commit -m "[Jenkins] change application $APP_NAME with tag $REPOSITORY_NAME_TAG"
                        '''
                        try {

                            sh '''
                                git push origin $NEW_BRANCH
                                git checkout $REPODEPLOY_BRANCH
                            '''
                            retry(5) {
                                
                                sh '''
                                    git pull origin $REPODEPLOY_BRANCH
                                    git merge $NEW_BRANCH -m "Jenkins Merge branch $NEW_BRANCH into $REPODEPLOY_BRANCH"
                                    git push origin $REPODEPLOY_BRANCH
                                    git push origin --delete $NEW_BRANCH
                                '''
                            }

                        } catch(Exception e) {
                            echo "Error during Git operations: ${e.message}"
                            sh '''
                                git push origin --delete $NEW_BRANCH || true
                                cd ..
                                rm -rf deploy-k8s
                            '''
                            error("Failed to push or merge. The branch has been deleted.")
                        }
                    }
                }    
            }
        }
        stage('Deploy application') {
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'ARGOCD_CREDENTIALS_ID', usernameVariable: 'ARGOCD_USERNAME', passwordVariable: 'ARGOCD_PASSWORD')]) {
                        sh '''
                            argocd login $ARGOCD_HOST --grpc-web --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD
                        '''
                    }
                    def appExists = sh(
                        script: "argocd app get $APP_NAME-$ENVIRONMENT_DEPLOY > /dev/null 2>&1;",
                        returnStatus: true
                    ) == 0
                    if (appExists) { 
                        echo "App already exists"
                    } else {
                        echo "Install Application with manifest"
                        sh '''
                            kubectl apply -f $PATH_ARGO --kubeconfig $CONFIG_COMMON
                        '''
                    }
                }    
            }
        }
        stage('Clear folder deploy') {
            steps {
                script {
                    sh '''
                        rm -rf deploy-k8s
                    '''
                }    
            }
        }
    }
}
