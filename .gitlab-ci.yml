variables:
  project1: 'LoyaltyAffiliateService-v2'
  project2: 'LoyaltyAffiliateService-v2-Worker'
stages:
  - trigger

trigger_to_stg:
  stage: trigger
  only:
    - stg
    - test
  tags:
    - cicd
  except:
    - tags
  script:
    - echo "Triggering Jenkins build for staging branch"
    - curl --user $jenkinsToken -X POST $jenkinsUrl/job/$project1/job/$CI_COMMIT_REF_NAME/build?delay=0sec
    - curl --user $jenkinsToken -X POST $jenkinsUrl/job/$project2/job/$CI_COMMIT_REF_NAME/build?delay=0sec
trigger_to_prod:
  stage: trigger
  only:
    - tags
  tags:
    - cicd
  script:
  - curl --user $jenkinsToken -X POST "$jenkinsUrl/job/$project1/build?delay=0"
  - sleep 2
  - curl --user $jenkinsToken -X POST "$jenkinsUrl/job/$project1/view/tags/job/$CI_COMMIT_REF_NAME/build?delay=0sec"
  - curl --user $jenkinsToken -X POST "$jenkinsUrl/job/$project2/build?delay=0"
  - sleep 2
  - curl --user $jenkinsToken -X POST "$jenkinsUrl/job/$project2/view/tags/job/$CI_COMMIT_REF_NAME/build?delay=0sec"
