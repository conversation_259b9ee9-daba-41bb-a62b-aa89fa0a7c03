# affiliate_integration_service_v2

Affiliate Integration Service V2 - NestJS application for managing affiliate orders and transactions.

## Recent Updates

### Affiliate Orders Enhancement (v2.1.0)

**What's New:**
- Added sync command for affiliate orders: `sync:affiliate-orders`
- Enhanced transaction tracking with 4 new dynamic fields
- Improved production deployment workflow
- Added comprehensive console command support

**New Fields Added (Dynamic):**
- `firstAmount`: First-time VUI amount from transaction logs
- `secondAmount`: Second-time VUI amount from transaction logs  
- `firstAmountStatus`: Status of first amount (SUCCESS/FAILED)
- `secondAmountStatus`: Status of second amount (SUCCESS/FAILED)

**Command Usage:**
```bash
# Development
npm run artisan sync:affiliate-orders

# Production  
npm run artisan:prod sync:affiliate-orders
```

**Note:** These fields are added dynamically during sync based on transaction log data, not as permanent entity properties.

---

## Installation

```bash
npm install
```

## Development

```bash
# Start in development mode
npm run start:dev

# Start in watch mode
npm run start:dev:watch
```

## Production Build & Deployment

```bash
# Build for production
npm run build

# Start production server
npm run start:prod

# Using PM2 for production (recommended)
npm install -g pm2
pm2 start ecosystem.config.js
```

## Available Commands

### Sync Commands

```bash
# Development: Sync affiliate orders with new fields from transaction logs
npm run artisan sync:affiliate-orders

# Production: Run sync command in production environment
npm run artisan:prod sync:affiliate-orders

# Alternative production method (direct node command)
node ./dist/console.js sync:affiliate-orders
```

### Console Commands

```bash
# Development: List all available console commands
npm run artisan -- --help

# Production: List all available console commands
npm run artisan:prod -- --help

# Run specific console command in development
npm run artisan <command-name>

# Run specific console command in production
npm run artisan:prod <command-name>
```

## Environment Setup

1. Copy environment file:
```bash
cp .env.example .env
```

2. Configure your environment variables in `.env` file

3. Set up MongoDB connection and Redis configuration

## Testing

```bash
# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run e2e tests
npm test:e2e
```

## Production Deployment Steps

1. **Build the application:**
```bash
npm run build
```

2. **Install production dependencies only:**
```bash
npm ci --production
```

3. **Start with PM2:**
```bash
pm2 start ecosystem.config.js --env production
```

4. **Monitor the application:**
```bash
pm2 logs
pm2 status
```

5. **Run sync command in production:**
```bash
# Method 1: Using npm script (recommended)
npm run artisan:prod sync:affiliate-orders

# Method 2: Direct node command
node ./dist/console.js sync:affiliate-orders

# Method 3: With environment variables
NODE_ENV=production node ./dist/console.js sync:affiliate-orders
```

6. **Schedule sync command with PM2 (optional):**
```bash
# Create a cron job with PM2
pm2 install pm2-cron-restart

# Add cron to run sync every day at 2 AM
pm2 restart affiliate-service-v2 --cron="0 2 * * *"
```

## Docker Deployment

```bash
# Build Docker image
docker build -t affiliate-integration-service-v2 .

# Run Docker container
docker run -p 3000:3000 affiliate-integration-service-v2
```

## API Documentation

Once the application is running, visit:
- Development: `http://localhost:3000/api`
- Production: `https://your-domain.com/api`

## Troubleshooting

### Build Issues

```bash
# Clean build
npm run prebuild
npm run build

# Check if build was successful
ls -la dist/
```

### Console Commands Not Working

```bash
# Ensure dependencies are installed
npm install

# For production, make sure the app is built
npm run build

# Check if console.js exists
ls -la dist/console.js
```

### Environment Issues

```bash
# Development: Check if .dev.env exists
ls -la .dev.env

# Production: Set environment variables manually
export NODE_ENV=production
export MONGODB_URI=your_mongodb_connection_string
node ./dist/console.js sync:affiliate-orders
```

### Database Connection Issues

1. Ensure MongoDB is running and accessible
2. Check connection string in environment variables
3. Verify network connectivity to database
4. Check authentication credentials

## Performance Notes

- The `sync:affiliate-orders` command processes orders in batches of 500
- Large datasets may take significant time to process
- Monitor memory usage during sync operations
- Consider running sync operations during low-traffic periods

## Monitoring

### Check Application Status

```bash
# PM2 status
pm2 status

# View logs
pm2 logs affiliate-service-v2

# Monitor resources
pm2 monit
```

### Sync Command Logs

The sync command provides detailed logging:
- Progress updates every 100 processed orders
- Error reporting for failed operations
- Success/failure statistics at completion
