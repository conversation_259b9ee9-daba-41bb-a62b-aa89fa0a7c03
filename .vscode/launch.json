{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:debug"], "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal", "restart": true, "sourceMaps": true, "envFile": "${workspaceFolder}/.dev.env"}, {"name": "Attach to nestjs", "request": "attach", "cwd": "${workspaceFolder}", "type": "node", "port": 9229}]}