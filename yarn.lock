# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  integrity sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==

"@ampproject/remapping@^2.2.0":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.1.tgz"
  integrity sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@angular-devkit/core@16.0.1":
  version "16.0.1"
  resolved "https://registry.npmjs.org/@angular-devkit/core/-/core-16.0.1.tgz"
  integrity sha512-2uz98IqkKJlgnHbWQ7VeL4pb+snGAZXIama2KXi+k9GsRntdcw+udX8rL3G9SdUGUF+m6+147Y1oRBMHsO/v4w==
  dependencies:
    ajv "8.12.0"
    ajv-formats "2.1.1"
    jsonc-parser "3.2.0"
    rxjs "7.8.1"
    source-map "0.7.4"

"@angular-devkit/schematics-cli@16.0.1":
  version "16.0.1"
  resolved "https://registry.npmjs.org/@angular-devkit/schematics-cli/-/schematics-cli-16.0.1.tgz"
  integrity sha512-6KLA125dpgd6oJGtiO2JpZAb92uOG3njQGIt7NFcuQGW/5GO7J41vMXH9cBAfdtbV8SIggSmR/cIEE9ijfj6YQ==
  dependencies:
    "@angular-devkit/core" "16.0.1"
    "@angular-devkit/schematics" "16.0.1"
    ansi-colors "4.1.3"
    inquirer "8.2.4"
    symbol-observable "4.0.0"
    yargs-parser "21.1.1"

"@angular-devkit/schematics@16.0.1":
  version "16.0.1"
  resolved "https://registry.npmjs.org/@angular-devkit/schematics/-/schematics-16.0.1.tgz"
  integrity sha512-A9D0LTYmiqiBa90GKcSuWb7hUouGIbm/AHbJbjL85WLLRbQA2PwKl7P5Mpd6nS/ZC0kfG4VQY3VOaDvb3qpI9g==
  dependencies:
    "@angular-devkit/core" "16.0.1"
    jsonc-parser "3.2.0"
    magic-string "0.30.0"
    ora "5.4.1"
    rxjs "7.8.1"

"@aws-crypto/crc32@3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-3.0.0.tgz"
  integrity sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/ie11-detection@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/ie11-detection/-/ie11-detection-3.0.0.tgz"
  integrity sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/sha256-browser@3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/sha256-browser/-/sha256-browser-3.0.0.tgz"
  integrity sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==
  dependencies:
    "@aws-crypto/ie11-detection" "^3.0.0"
    "@aws-crypto/sha256-js" "^3.0.0"
    "@aws-crypto/supports-web-crypto" "^3.0.0"
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-locate-window" "^3.0.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-crypto/sha256-js@3.0.0", "@aws-crypto/sha256-js@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/sha256-js/-/sha256-js-3.0.0.tgz"
  integrity sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==
  dependencies:
    "@aws-crypto/util" "^3.0.0"
    "@aws-sdk/types" "^3.222.0"
    tslib "^1.11.1"

"@aws-crypto/supports-web-crypto@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/supports-web-crypto/-/supports-web-crypto-3.0.0.tgz"
  integrity sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==
  dependencies:
    tslib "^1.11.1"

"@aws-crypto/util@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz"
  integrity sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==
  dependencies:
    "@aws-sdk/types" "^3.222.0"
    "@aws-sdk/util-utf8-browser" "^3.0.0"
    tslib "^1.11.1"

"@aws-sdk/client-cognito-identity@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.476.0.tgz"
  integrity sha512-DWBLcCyuL9PvM7XTSf6osVmi1jQ0pCxeOOD/o+4KINdYy7vridYVPNIknWi7yKalm5INNt6Ggp98XuMHQJqeQA==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/client-sts" "3.476.0"
    "@aws-sdk/core" "3.476.0"
    "@aws-sdk/credential-provider-node" "3.476.0"
    "@aws-sdk/middleware-host-header" "3.468.0"
    "@aws-sdk/middleware-logger" "3.468.0"
    "@aws-sdk/middleware-recursion-detection" "3.468.0"
    "@aws-sdk/middleware-signing" "3.468.0"
    "@aws-sdk/middleware-user-agent" "3.470.0"
    "@aws-sdk/region-config-resolver" "3.470.0"
    "@aws-sdk/types" "3.468.0"
    "@aws-sdk/util-endpoints" "3.470.0"
    "@aws-sdk/util-user-agent-browser" "3.468.0"
    "@aws-sdk/util-user-agent-node" "3.470.0"
    "@smithy/config-resolver" "^2.0.21"
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/hash-node" "^2.0.17"
    "@smithy/invalid-dependency" "^2.0.15"
    "@smithy/middleware-content-length" "^2.0.17"
    "@smithy/middleware-endpoint" "^2.2.3"
    "@smithy/middleware-retry" "^2.0.24"
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/middleware-stack" "^2.0.9"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    "@smithy/util-base64" "^2.0.1"
    "@smithy/util-body-length-browser" "^2.0.1"
    "@smithy/util-body-length-node" "^2.1.0"
    "@smithy/util-defaults-mode-browser" "^2.0.22"
    "@smithy/util-defaults-mode-node" "^2.0.29"
    "@smithy/util-endpoints" "^1.0.7"
    "@smithy/util-retry" "^2.0.8"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@aws-sdk/client-sso@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.476.0.tgz"
  integrity sha512-vcGGumQplAtzOhg3MbYmktl69v7BXGtzfpiw4w7i0KjBy/QBy0vt6xQpS7H/24s17/kCw+UXlZR6sFQ/Vj73ag==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/core" "3.476.0"
    "@aws-sdk/middleware-host-header" "3.468.0"
    "@aws-sdk/middleware-logger" "3.468.0"
    "@aws-sdk/middleware-recursion-detection" "3.468.0"
    "@aws-sdk/middleware-user-agent" "3.470.0"
    "@aws-sdk/region-config-resolver" "3.470.0"
    "@aws-sdk/types" "3.468.0"
    "@aws-sdk/util-endpoints" "3.470.0"
    "@aws-sdk/util-user-agent-browser" "3.468.0"
    "@aws-sdk/util-user-agent-node" "3.470.0"
    "@smithy/config-resolver" "^2.0.21"
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/hash-node" "^2.0.17"
    "@smithy/invalid-dependency" "^2.0.15"
    "@smithy/middleware-content-length" "^2.0.17"
    "@smithy/middleware-endpoint" "^2.2.3"
    "@smithy/middleware-retry" "^2.0.24"
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/middleware-stack" "^2.0.9"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    "@smithy/util-base64" "^2.0.1"
    "@smithy/util-body-length-browser" "^2.0.1"
    "@smithy/util-body-length-node" "^2.1.0"
    "@smithy/util-defaults-mode-browser" "^2.0.22"
    "@smithy/util-defaults-mode-node" "^2.0.29"
    "@smithy/util-endpoints" "^1.0.7"
    "@smithy/util-retry" "^2.0.8"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@aws-sdk/client-sts@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/client-sts/-/client-sts-3.476.0.tgz"
  integrity sha512-duMs4tTy3hNuSdV2YFzT6QNlE0PX2RzZqAfO4dTITiEf6QZW/N3UojSZwDRTKZzH+CFKL2gjVhkv7d4ZCy5QvQ==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/core" "3.476.0"
    "@aws-sdk/credential-provider-node" "3.476.0"
    "@aws-sdk/middleware-host-header" "3.468.0"
    "@aws-sdk/middleware-logger" "3.468.0"
    "@aws-sdk/middleware-recursion-detection" "3.468.0"
    "@aws-sdk/middleware-user-agent" "3.470.0"
    "@aws-sdk/region-config-resolver" "3.470.0"
    "@aws-sdk/types" "3.468.0"
    "@aws-sdk/util-endpoints" "3.470.0"
    "@aws-sdk/util-user-agent-browser" "3.468.0"
    "@aws-sdk/util-user-agent-node" "3.470.0"
    "@smithy/config-resolver" "^2.0.21"
    "@smithy/core" "^1.1.0"
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/hash-node" "^2.0.17"
    "@smithy/invalid-dependency" "^2.0.15"
    "@smithy/middleware-content-length" "^2.0.17"
    "@smithy/middleware-endpoint" "^2.2.3"
    "@smithy/middleware-retry" "^2.0.24"
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/middleware-stack" "^2.0.9"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    "@smithy/util-base64" "^2.0.1"
    "@smithy/util-body-length-browser" "^2.0.1"
    "@smithy/util-body-length-node" "^2.1.0"
    "@smithy/util-defaults-mode-browser" "^2.0.22"
    "@smithy/util-defaults-mode-node" "^2.0.29"
    "@smithy/util-endpoints" "^1.0.7"
    "@smithy/util-middleware" "^2.0.8"
    "@smithy/util-retry" "^2.0.8"
    "@smithy/util-utf8" "^2.0.2"
    fast-xml-parser "4.2.5"
    tslib "^2.5.0"

"@aws-sdk/core@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/core/-/core-3.476.0.tgz"
  integrity sha512-G9CLcxxrSR1FWI1o+Hf/XwIERiQFRhuYxydU7C/QnRP9g5FdE0dxWcIg1U/RJnmkiWTrIG3gRWBXvIw5DCecPw==
  dependencies:
    "@smithy/core" "^1.1.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/signature-v4" "^2.0.0"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-cognito-identity@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.476.0.tgz"
  integrity sha512-BbbirpIPGnRkPUA2nwD/mXKItCWhmTRIf/CKu8sY+X79cB9mOURX0yVRp/bdWHyrooOKw3MBsmq5nDJDBG1tlw==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.476.0"
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-env@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.468.0.tgz"
  integrity sha512-k/1WHd3KZn0EQYjadooj53FC0z24/e4dUZhbSKTULgmxyO62pwh9v3Brvw4WRa/8o2wTffU/jo54tf4vGuP/ZA==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-http@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.468.0.tgz"
  integrity sha512-pUF+gmeCr4F1De69qEsWgnNeF7xzlLcjiGcbpO6u9k6NQdRR7Xr3wTQnQt1+3MgoIdbgoXpCfQYNZ4LfX6B/sA==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/util-stream" "^2.0.23"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-ini@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.476.0.tgz"
  integrity sha512-wAeXkCDW0qq/1suVTxGIW2RMQTBKwuhL1dwXt+Fmay0hQe4CKzmlTKFY9bN3CnTuwUCN8ozURimpeFFQ7rmKBw==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.468.0"
    "@aws-sdk/credential-provider-process" "3.468.0"
    "@aws-sdk/credential-provider-sso" "3.476.0"
    "@aws-sdk/credential-provider-web-identity" "3.468.0"
    "@aws-sdk/types" "3.468.0"
    "@smithy/credential-provider-imds" "^2.0.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/shared-ini-file-loader" "^2.0.6"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-node@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.476.0.tgz"
  integrity sha512-BOkFBHYDgH+o6YRkk+QgQz3ro9Ly3RhNGzK5HeH37eyWWWgL1BTgY/cHgX3VNRmuKfIoph3yB2C5+eHKf41XYw==
  dependencies:
    "@aws-sdk/credential-provider-env" "3.468.0"
    "@aws-sdk/credential-provider-ini" "3.476.0"
    "@aws-sdk/credential-provider-process" "3.468.0"
    "@aws-sdk/credential-provider-sso" "3.476.0"
    "@aws-sdk/credential-provider-web-identity" "3.468.0"
    "@aws-sdk/types" "3.468.0"
    "@smithy/credential-provider-imds" "^2.0.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/shared-ini-file-loader" "^2.0.6"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-process@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.468.0.tgz"
  integrity sha512-OYSn1A/UsyPJ7Z8Q2cNhTf55O36shPmSsvOfND04nSfu1nPaR+VUvvsP7v+brhGpwC/GAKTIdGAo4blH31BS6A==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/shared-ini-file-loader" "^2.0.6"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-sso@3.476.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.476.0.tgz"
  integrity sha512-jOTaH/T2xm94ebgw2xqPgPzB2OUirgL2YWSE3xCqeFJK0c9J64jz4LORI7/uXVZB4l+20axneUhoyEygQMBxOw==
  dependencies:
    "@aws-sdk/client-sso" "3.476.0"
    "@aws-sdk/token-providers" "3.470.0"
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/shared-ini-file-loader" "^2.0.6"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-provider-web-identity@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.468.0.tgz"
  integrity sha512-rexymPmXjtkwCPfhnUq3EjO1rSkf39R4Jz9CqiM7OsqK2qlT5Y/V3gnMKn0ZMXsYaQOMfM3cT5xly5R+OKDHlw==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/credential-providers@^3.186.0":
  version "3.476.0"
  resolved "https://registry.npmjs.org/@aws-sdk/credential-providers/-/credential-providers-3.476.0.tgz"
  integrity sha512-0+e1M1FfWVVfy6btEd1aof89u+AsExcSeGkiE2PbRtLsHRJAKdV9Iaajmb3XFLm5HLngdVQjwTQDPrhneZ/+tw==
  dependencies:
    "@aws-sdk/client-cognito-identity" "3.476.0"
    "@aws-sdk/client-sso" "3.476.0"
    "@aws-sdk/client-sts" "3.476.0"
    "@aws-sdk/credential-provider-cognito-identity" "3.476.0"
    "@aws-sdk/credential-provider-env" "3.468.0"
    "@aws-sdk/credential-provider-http" "3.468.0"
    "@aws-sdk/credential-provider-ini" "3.476.0"
    "@aws-sdk/credential-provider-node" "3.476.0"
    "@aws-sdk/credential-provider-process" "3.468.0"
    "@aws-sdk/credential-provider-sso" "3.476.0"
    "@aws-sdk/credential-provider-web-identity" "3.468.0"
    "@aws-sdk/types" "3.468.0"
    "@smithy/credential-provider-imds" "^2.0.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-host-header@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.468.0.tgz"
  integrity sha512-gwQ+/QhX+lhof304r6zbZ/V5l5cjhGRxLL3CjH1uJPMcOAbw9wUlMdl+ibr8UwBZ5elfKFGiB1cdW/0uMchw0w==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-logger@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.468.0.tgz"
  integrity sha512-X5XHKV7DHRXI3f29SAhJPe/OxWRFgDWDMMCALfzhmJfCi6Jfh0M14cJKoC+nl+dk9lB+36+jKjhjETZaL2bPlA==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-recursion-detection@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.468.0.tgz"
  integrity sha512-vch9IQib2Ng9ucSyRW2eKNQXHUPb5jUPCLA5otTW/8nGjcOU37LxQG4WrxO7uaJ9Oe8hjHO+hViE3P0KISUhtA==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/middleware-signing@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-signing/-/middleware-signing-3.468.0.tgz"
  integrity sha512-s+7fSB1gdnnTj5O0aCCarX3z5Vppop8kazbNSZADdkfHIDWCN80IH4ZNjY3OWqaAz0HmR4LNNrovdR304ojb4Q==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/signature-v4" "^2.0.0"
    "@smithy/types" "^2.7.0"
    "@smithy/util-middleware" "^2.0.8"
    tslib "^2.5.0"

"@aws-sdk/middleware-user-agent@3.470.0":
  version "3.470.0"
  resolved "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.470.0.tgz"
  integrity sha512-s0YRGgf4fT5KwwTefpoNUQfB5JghzXyvmPfY1QuFEMeVQNxv0OPuydzo3rY2oXPkZjkulKDtpm5jzIHwut75hA==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@aws-sdk/util-endpoints" "3.470.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/region-config-resolver@3.470.0":
  version "3.470.0"
  resolved "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.470.0.tgz"
  integrity sha512-C1o1J06iIw8cyAAOvHqT4Bbqf+PgQ/RDlSyjt2gFfP2OovDpc2o2S90dE8f8iZdSGpg70N5MikT1DBhW9NbhtQ==
  dependencies:
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/types" "^2.7.0"
    "@smithy/util-config-provider" "^2.0.0"
    "@smithy/util-middleware" "^2.0.8"
    tslib "^2.5.0"

"@aws-sdk/token-providers@3.470.0":
  version "3.470.0"
  resolved "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.470.0.tgz"
  integrity sha512-rzxnJxEUJiV69Cxsf0AHXTqJqTACITwcSH/PL4lWP4uvtzdrzSi3KA3u2aWHWpOcdE6+JFvdICscsbBSo3/TOg==
  dependencies:
    "@aws-crypto/sha256-browser" "3.0.0"
    "@aws-crypto/sha256-js" "3.0.0"
    "@aws-sdk/middleware-host-header" "3.468.0"
    "@aws-sdk/middleware-logger" "3.468.0"
    "@aws-sdk/middleware-recursion-detection" "3.468.0"
    "@aws-sdk/middleware-user-agent" "3.470.0"
    "@aws-sdk/region-config-resolver" "3.470.0"
    "@aws-sdk/types" "3.468.0"
    "@aws-sdk/util-endpoints" "3.470.0"
    "@aws-sdk/util-user-agent-browser" "3.468.0"
    "@aws-sdk/util-user-agent-node" "3.470.0"
    "@smithy/config-resolver" "^2.0.21"
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/hash-node" "^2.0.17"
    "@smithy/invalid-dependency" "^2.0.15"
    "@smithy/middleware-content-length" "^2.0.17"
    "@smithy/middleware-endpoint" "^2.2.3"
    "@smithy/middleware-retry" "^2.0.24"
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/middleware-stack" "^2.0.9"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/property-provider" "^2.0.0"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/shared-ini-file-loader" "^2.0.6"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    "@smithy/util-base64" "^2.0.1"
    "@smithy/util-body-length-browser" "^2.0.1"
    "@smithy/util-body-length-node" "^2.1.0"
    "@smithy/util-defaults-mode-browser" "^2.0.22"
    "@smithy/util-defaults-mode-node" "^2.0.29"
    "@smithy/util-endpoints" "^1.0.7"
    "@smithy/util-retry" "^2.0.8"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@aws-sdk/types@3.468.0", "@aws-sdk/types@^3.222.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/types/-/types-3.468.0.tgz"
  integrity sha512-rx/9uHI4inRbp2tw3Y4Ih4PNZkVj32h7WneSg3MVgVjAoVD5Zti9KhS5hkvsBxfgmQmg0AQbE+b1sy5WGAgntA==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/util-endpoints@3.470.0":
  version "3.470.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.470.0.tgz"
  integrity sha512-6N6VvPCmu+89p5Ez/+gLf+X620iQ9JpIs8p8ECZiCodirzFOe8NC1O2S7eov7YiG9IHSuodqn/0qNq+v+oLe0A==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/util-endpoints" "^1.0.7"
    tslib "^2.5.0"

"@aws-sdk/util-locate-window@^3.0.0":
  version "3.465.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-locate-window/-/util-locate-window-3.465.0.tgz"
  integrity sha512-f+QNcWGswredzC1ExNAB/QzODlxwaTdXkNT5cvke2RLX8SFU5pYk6h4uCtWC0vWPELzOfMfloBrJefBzlarhsw==
  dependencies:
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-browser@3.468.0":
  version "3.468.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.468.0.tgz"
  integrity sha512-OJyhWWsDEizR3L+dCgMXSUmaCywkiZ7HSbnQytbeKGwokIhD69HTiJcibF/sgcM5gk4k3Mq3puUhGnEZ46GIig==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/types" "^2.7.0"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@aws-sdk/util-user-agent-node@3.470.0":
  version "3.470.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.470.0.tgz"
  integrity sha512-QxsZ9iVHcBB/XRdYvwfM5AMvNp58HfqkIrH88mY0cmxuvtlIGDfWjczdDrZMJk9y0vIq+cuoCHsGXHu7PyiEAQ==
  dependencies:
    "@aws-sdk/types" "3.468.0"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@aws-sdk/util-utf8-browser@^3.0.0":
  version "3.259.0"
  resolved "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz"
  integrity sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==
  dependencies:
    tslib "^2.3.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.7", "@babel/code-frame@^7.22.13", "@babel/code-frame@^7.23.5":
  version "7.23.5"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.23.5.tgz"
  integrity sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==
  dependencies:
    "@babel/highlight" "^7.23.4"
    chalk "^2.4.2"

"@babel/compat-data@^7.23.5":
  version "7.23.5"
  resolved "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.23.5.tgz"
  integrity sha512-uU27kfDRlhfKl+w1U6vp16IuvSLtjAxdArVXPa9BvLkrr7CYIsxH5adpHObeAGY/41+syctUWOZ140a2Rvkgjw==

"@babel/core@^7.11.6", "@babel/core@^7.12.3":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/core/-/core-7.23.6.tgz"
  integrity sha512-FxpRyGjrMJXh7X3wGLGhNDCRiwpWEF74sKjTLDJSG5Kyvow3QZaG0Adbqzi9ZrVjTWpsX+2cxWXD71NMg93kdw==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.23.5"
    "@babel/generator" "^7.23.6"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helpers" "^7.23.6"
    "@babel/parser" "^7.23.6"
    "@babel/template" "^7.22.15"
    "@babel/traverse" "^7.23.6"
    "@babel/types" "^7.23.6"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.23.6", "@babel/generator@^7.7.2":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/generator/-/generator-7.23.6.tgz"
  integrity sha512-qrSfCYxYQB5owCmGLbl8XRpX1ytXlpueOb0N0UmQwA073KZxejgQTzAmJezxvpwQD9uGtK2shHdi55QT+MbjIw==
  dependencies:
    "@babel/types" "^7.23.6"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    jsesc "^2.5.1"

"@babel/helper-compilation-targets@^7.23.6":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz"
  integrity sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==
  dependencies:
    "@babel/compat-data" "^7.23.5"
    "@babel/helper-validator-option" "^7.23.5"
    browserslist "^4.22.2"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-environment-visitor@^7.22.20":
  version "7.22.20"
  resolved "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz"
  integrity sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==

"@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz"
  integrity sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz"
  integrity sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-module-imports@^7.22.15":
  version "7.22.15"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz"
  integrity sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.23.3":
  version "7.23.3"
  resolved "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.23.3.tgz"
  integrity sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-module-imports" "^7.22.15"
    "@babel/helper-simple-access" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/helper-validator-identifier" "^7.22.20"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.8.0":
  version "7.22.5"
  resolved "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz"
  integrity sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==

"@babel/helper-simple-access@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz"
  integrity sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.22.6":
  version "7.22.6"
  resolved "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz"
  integrity sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-string-parser@^7.23.4":
  version "7.23.4"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz"
  integrity sha512-803gmbQdqwdf4olxrX4AJyFBV/RTr3rSmOj0rKwesmzlfhYNDEs+/iOcznzpNWlJlIlTJC2QfPFcHB6DlzdVLQ==

"@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz"
  integrity sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==

"@babel/helper-validator-option@^7.23.5":
  version "7.23.5"
  resolved "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz"
  integrity sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==

"@babel/helpers@^7.23.6":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/helpers/-/helpers-7.23.6.tgz"
  integrity sha512-wCfsbN4nBidDRhpDhvcKlzHWCTlgJYUUdSJfzXb2NuBssDSIjc3xcb+znA7l+zYsFljAcGM0aFkN40cR3lXiGA==
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/traverse" "^7.23.6"
    "@babel/types" "^7.23.6"

"@babel/highlight@^7.23.4":
  version "7.23.4"
  resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.23.4.tgz"
  integrity sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A==
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.22.15", "@babel/parser@^7.23.6":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/parser/-/parser-7.23.6.tgz"
  integrity sha512-Z2uID7YJ7oNvAI20O9X0bblw7Qqs8Q2hFy0R9tAfnfLkp5MW0UH9eUvnDSnFwKZ0AvgS1ucqR4KzvVHgnke1VQ==

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.7.2":
  version "7.23.3"
  resolved "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.23.3.tgz"
  integrity sha512-9EiNjVJOMwCO+43TqoTrgQ8jMwcAd0sWyXi9RPfIsLTj4R2MADDDQXELhffaUx/uJv2AYcxBgPwH6j4TIA4ytQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/template@^7.22.15", "@babel/template@^7.3.3":
  version "7.22.15"
  resolved "https://registry.npmjs.org/@babel/template/-/template-7.22.15.tgz"
  integrity sha512-QPErUVm4uyJa60rkI73qneDacvdvzxshT3kksGqlGWYdOTIUOwJ7RDUL8sGqslY1uXWSL6xMFKEXDS3ox2uF0w==
  dependencies:
    "@babel/code-frame" "^7.22.13"
    "@babel/parser" "^7.22.15"
    "@babel/types" "^7.22.15"

"@babel/traverse@^7.23.6", "@babel/traverse@^7.7.2":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/traverse/-/traverse-7.23.6.tgz"
  integrity sha512-czastdK1e8YByZqezMPFiZ8ahwVMh/ESl9vPgvgdB9AmFMGP5jfpFax74AQgl5zj4XHzqeYAg2l8PuUeRS1MgQ==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/generator" "^7.23.6"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.22.6"
    "@babel/parser" "^7.23.6"
    "@babel/types" "^7.23.6"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.22.15", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.23.6", "@babel/types@^7.3.3":
  version "7.23.6"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.23.6.tgz"
  integrity sha512-+uarb83brBzPKN38NX1MkB6vb6+mwvR6amUulqAE7ccQw1pEl+bCia9TbdG1lsnFP7lZySvUn37CHyXQdfTwzg==
  dependencies:
    "@babel/helper-string-parser" "^7.23.4"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz"
  integrity sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==

"@bull-board/api@4.12.2", "@bull-board/api@^4.1.1":
  version "4.12.2"
  resolved "https://registry.npmjs.org/@bull-board/api/-/api-4.12.2.tgz"
  integrity sha512-efF8K1pvfEEft2ELQwCBe/a225vHufCjM2hfcyvmIG/SUX6TlKQFUFZ1+KV0wenD9wxvUVb5wxUu6on+HrZiVg==
  dependencies:
    redis-info "^3.0.8"

"@bull-board/express@^4.1.1":
  version "4.12.2"
  resolved "https://registry.npmjs.org/@bull-board/express/-/express-4.12.2.tgz"
  integrity sha512-Y/pzCtQoxvLyioiNYLKKgxslccV+7dyTy4qCGAXyUbAuOaav+U4tbfWNcZYaMSkr/1gg0m/6S/qf+xn2vAsDkQ==
  dependencies:
    "@bull-board/api" "4.12.2"
    "@bull-board/ui" "4.12.2"
    ejs "3.1.7"
    express "4.17.3"

"@bull-board/ui@4.12.2":
  version "4.12.2"
  resolved "https://registry.npmjs.org/@bull-board/ui/-/ui-4.12.2.tgz"
  integrity sha512-jB/OOEhg+DUL6ssmtQYTK+iYd3iy68bbozOp+q2xVUC4V7zeFmYF25sIApYFTNfbjuUMesAVOiX4u0gNEo/J7w==
  dependencies:
    "@bull-board/api" "4.12.2"

"@colors/colors@1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz"
  integrity sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz"
  integrity sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@elastic/ecs-helpers@^2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@elastic/ecs-helpers/-/ecs-helpers-2.1.1.tgz"
  integrity sha512-ItoNazMnYdlUCmkBYTXc3SG6PF7UlVTbvMdHPvXkfTMPdwGv2G1Xtp5CjDHaGHGOZSwaDrW4RSCXvA/lMSU+rg==

"@elastic/ecs-pino-format@^1.5.0":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@elastic/ecs-pino-format/-/ecs-pino-format-1.5.0.tgz"
  integrity sha512-7MMVmT50ucEl7no8mUgCIl+pffBVNRl36uZi0vmalWa2xPWISBxM9k9WSP/WTgOkmGj9G35e5g3UfCS1zxshBg==
  dependencies:
    "@elastic/ecs-helpers" "^2.1.1"

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.0"
  resolved "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.4.0.tgz"
  integrity sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.10.0"
  resolved "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.10.0.tgz"
  integrity sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz"
  integrity sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.56.0":
  version "8.56.0"
  resolved "https://registry.npmjs.org/@eslint/js/-/js-8.56.0.tgz"
  integrity sha512-gMsVel9D7f2HLkBma9VbtzZRehRogVRfbr++f06nL2vnCGCNlzOD+/MUov/F4p8myyAHspEhVobgjpX64q5m6A==

"@fig/complete-commander@^3.0.0":
  version "3.2.0"
  resolved "https://registry.npmjs.org/@fig/complete-commander/-/complete-commander-3.2.0.tgz"
  integrity sha512-1Holl3XtRiANVKURZwgpjCnPuV4RsHp+XC0MhgvyAX/avQwj7F2HUItYOvGi/bXjJCkEzgBZmVfCr0HBA+q+Bw==
  dependencies:
    prettier "^3.2.5"

"@golevelup/nestjs-discovery@4.0.1":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@golevelup/nestjs-discovery/-/nestjs-discovery-4.0.1.tgz"
  integrity sha512-HFXBJayEkYcU/bbxOztozONdWaZR34ZeJ2zRbZIWY8d5K26oPZQTvJ4L0STW3XVRGWtoE0WBpmx2YPNgYvcmJQ==
  dependencies:
    lodash "^4.17.21"

"@hapi/hoek@^9.0.0":
  version "9.3.0"
  resolved "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz"
  integrity sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==

"@hapi/topo@^5.0.0":
  version "5.1.0"
  resolved "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz"
  integrity sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.11.13":
  version "0.11.13"
  resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.13.tgz"
  integrity sha512-JSBDMiDKSzQVngfRjOdFXgFfklaXI4K9nLF49Auh21lmBWRLIK3+xTErTWD4KU54pb6coM6ESE7Awz/FNU3zgQ==
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.1"
    debug "^4.1.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  integrity sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==

"@humanwhocodes/object-schema@^2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.1.tgz"
  integrity sha512-dvuCeX5fC9dXgJn9t+X5atfmgQAzUOWqS1254Gh0m6i8wKd10ebXkfNKiRK+1GWi/yTvvLDHpoxLr0xxxeslWw==

"@ioredis/commands@^1.1.1":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@ioredis/commands/-/commands-1.2.0.tgz"
  integrity sha512-Sx1pU8EM64o2BrqNpEO1CNLtKQwyhuXuqyfH7oGKCk+1a33d2r5saW8zNwm3j6BTExtjrv2BxTgzzkMwts6vGg==

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz"
  integrity sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz"
  integrity sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==

"@jest/console@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/console/-/console-28.1.3.tgz"
  integrity sha512-QPAkP5EwKdK/bxIr6C1I4Vs0rm2nHiANzj/Z5X2JQkrZo6IqvC4ldZ9K95tF0HdidhA8Bo6egxSzUFPYKcEXLw==
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    slash "^3.0.0"

"@jest/core@^28.1.2", "@jest/core@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/core/-/core-28.1.3.tgz"
  integrity sha512-CIKBrlaKOzA7YG19BEqCw3SLIsEwjZkeJzf5bdooVnW4bH5cktqe3JX+G2YV1aK5vP8N9na1IGWFzYaTp6k6NA==
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/reporters" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^28.1.3"
    jest-config "^28.1.3"
    jest-haste-map "^28.1.3"
    jest-message-util "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-resolve-dependencies "^28.1.3"
    jest-runner "^28.1.3"
    jest-runtime "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    jest-watcher "^28.1.3"
    micromatch "^4.0.4"
    pretty-format "^28.1.3"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/environment/-/environment-28.1.3.tgz"
  integrity sha512-1bf40cMFTEkKyEf585R9Iz1WayDjHoHqvts0XFYEqyKM3cFWDpeMoqKKTAF9LSYQModPUlh8FKptoM2YcMWAXA==
  dependencies:
    "@jest/fake-timers" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    jest-mock "^28.1.3"

"@jest/expect-utils@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-28.1.3.tgz"
  integrity sha512-wvbi9LUrHJLn3NlDW6wF2hvIMtd4JUl2QNVrjq+IBSHirgfrR3o9RnVtxzdEGO2n9JyIWwHnLfby5KzqBGg2YA==
  dependencies:
    jest-get-type "^28.0.2"

"@jest/expect@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/expect/-/expect-28.1.3.tgz"
  integrity sha512-lzc8CpUbSoE4dqT0U+g1qODQjBRHPpCPXissXD4mS9+sWQdmmpeJ9zSH1rS1HEkrsMN0fb7nKrJ9giAR1d3wBw==
  dependencies:
    expect "^28.1.3"
    jest-snapshot "^28.1.3"

"@jest/fake-timers@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-28.1.3.tgz"
  integrity sha512-D/wOkL2POHv52h+ok5Oj/1gOG9HSywdoPtFsRCUmlCILXNn5eIWmcnd3DIiWlJnpGvQtmajqBP95Ei0EimxfLw==
  dependencies:
    "@jest/types" "^28.1.3"
    "@sinonjs/fake-timers" "^9.1.2"
    "@types/node" "*"
    jest-message-util "^28.1.3"
    jest-mock "^28.1.3"
    jest-util "^28.1.3"

"@jest/globals@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/globals/-/globals-28.1.3.tgz"
  integrity sha512-XFU4P4phyryCXu1pbcqMO0GSQcYe1IsalYCDzRNyhetyeyxMcIxa11qPNDpVNLeretItNqEmYYQn1UYz/5x1NA==
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/expect" "^28.1.3"
    "@jest/types" "^28.1.3"

"@jest/reporters@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/reporters/-/reporters-28.1.3.tgz"
  integrity sha512-JuAy7wkxQZVNU/V6g9xKzCGC5LVXx9FDcABKsSXp5MiKPEE2144a/vXTEDoyzjUpZKfVwp08Wqg5A4WfTMAzjg==
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@jridgewell/trace-mapping" "^0.3.13"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^5.1.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    jest-worker "^28.1.3"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    terminal-link "^2.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/schemas/-/schemas-28.1.3.tgz"
  integrity sha512-/l/VWsdt/aBXgjshLWOFyFt3IVdYypu5y2Wn2rOO1un6nkqIn8SLXzgIMYXFyYsRWDyF5EthmKJMIdJvk08grg==
  dependencies:
    "@sinclair/typebox" "^0.24.1"

"@jest/source-map@^28.1.2":
  version "28.1.2"
  resolved "https://registry.npmjs.org/@jest/source-map/-/source-map-28.1.2.tgz"
  integrity sha512-cV8Lx3BeStJb8ipPHnqVw/IM2VCMWO3crWZzYodSIkxXnRcXJipCdx1JCK0K5MsJJouZQTH73mzf4vgxRaH9ww==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.13"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.3.tgz"
  integrity sha512-kZAkxnSE+FqE8YjW8gNuoVkkC9I7S1qmenl8sGcDOLropASP+BkcGKwhXoyqQuGOGeYY0y/ixjrd/iERpEXHNg==
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-28.1.3.tgz"
  integrity sha512-NIMPEqqa59MWnDi1kvXXpYbqsfQmSJsIbnd85mdVGkiDfQ9WQQTXOLsvISUfonmnBT+w85WEgneCigEEdHDFxw==
  dependencies:
    "@jest/test-result" "^28.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    slash "^3.0.0"

"@jest/transform@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/transform/-/transform-28.1.3.tgz"
  integrity sha512-u5dT5di+oFI6hfcLOHGTAfmUxFRrjK+vnaP0kkVow9Md/M7V/MxqQMOz/VV25UZO8pzeA9PjfTpOu6BDuwSPQA==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^28.1.3"
    "@jridgewell/trace-mapping" "^0.3.13"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-util "^28.1.3"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.1"

"@jest/types@^28.1.1", "@jest/types@^28.1.3":
  version "28.1.3"
  resolved "https://registry.npmjs.org/@jest/types/-/types-28.1.3.tgz"
  integrity sha512-RyjiyMUZrKz/c+zlMFO1pm70DcIlST8AeWTkoUdZevew44wcNZQHsEVOiCVtgVnlFFD82FPaXycys58cf2muVQ==
  dependencies:
    "@jest/schemas" "^28.1.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  version "0.3.3"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz"
  integrity sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.1"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz"
  integrity sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/source-map@^0.3.3":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.5.tgz"
  integrity sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.4.15"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz"
  integrity sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.13", "@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.20", "@jridgewell/trace-mapping@^0.3.9":
  version "0.3.20"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.20.tgz"
  integrity sha512-R8LcPeWZol2zR8mmH3JeKQ6QRCFb7XgUhV9ZlGhHLGyg4wpPiPZNQOOWhFZhxKw8u//yTbNGI42Bx/3paXEQ+Q==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@lukeed/csprng@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmjs.org/@lukeed/csprng/-/csprng-1.1.0.tgz"
  integrity sha512-Z7C/xXCiGWsg0KuKsHTKJxbWhpI3Vs5GwLfOean7MGyVFGqdRgBbAjOCh6u4bbjPc/8MJ2pZmK/0DLdCbivLDA==

"@mongodb-js/saslprep@^1.1.0":
  version "1.1.1"
  resolved "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.1.1.tgz"
  integrity sha512-t7c5K033joZZMspnHg/gWPE4kandgc2OxE74aYOtGKfgB9VPuVJPix0H6fhmm2erj5PBJ21mqcx34lpIGtUCsQ==
  dependencies:
    sparse-bitfield "^3.0.3"

"@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.2":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@msgpackr-extract/msgpackr-extract-darwin-arm64/-/msgpackr-extract-darwin-arm64-3.0.2.tgz#44d752c1a2dc113f15f781b7cc4f53a307e3fa38"
  integrity sha512-9bfjwDxIDWmmOKusUcqdS4Rw+SETlp9Dy39Xui9BEGEk19dDwH0jhipwFzEff/pFg95NKymc6TOTbRKcWeRqyQ==

"@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.2":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@msgpackr-extract/msgpackr-extract-darwin-x64/-/msgpackr-extract-darwin-x64-3.0.2.tgz#f954f34355712212a8e06c465bc06c40852c6bb3"
  integrity sha512-lwriRAHm1Yg4iDf23Oxm9n/t5Zpw1lVnxYU3HnJPTi2lJRkKTrps1KVgvL6m7WvmhYVt/FIsssWay+k45QHeuw==

"@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.2":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@msgpackr-extract/msgpackr-extract-linux-arm64/-/msgpackr-extract-linux-arm64-3.0.2.tgz#45c63037f045c2b15c44f80f0393fa24f9655367"
  integrity sha512-FU20Bo66/f7He9Fp9sP2zaJ1Q8L9uLPZQDub/WlUip78JlPeMbVL8546HbZfcW9LNciEXc8d+tThSJjSC+tmsg==

"@msgpackr-extract/msgpackr-extract-linux-arm@3.0.2":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@msgpackr-extract/msgpackr-extract-linux-arm/-/msgpackr-extract-linux-arm-3.0.2.tgz#35707efeafe6d22b3f373caf9e8775e8920d1399"
  integrity sha512-MOI9Dlfrpi2Cuc7i5dXdxPbFIgbDBGgKR5F2yWEa6FVEtSWncfVNKW5AKjImAQ6CZlBK9tympdsZJ2xThBiWWA==

"@msgpackr-extract/msgpackr-extract-linux-x64@3.0.2":
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/@msgpackr-extract/msgpackr-extract-linux-x64/-/msgpackr-extract-linux-x64-3.0.2.tgz#091b1218b66c341f532611477ef89e83f25fae4f"
  integrity sha512-gsWNDCklNy7Ajk0vBBf9jEx04RUxuDQfBse918Ww+Qb9HCPoGzS+XJTLe96iN3BVK7grnLiYghP/M4L8VsaHeA==

"@msgpackr-extract/msgpackr-extract-win32-x64@3.0.2":
  version "3.0.2"
  resolved "https://registry.npmjs.org/@msgpackr-extract/msgpackr-extract-win32-x64/-/msgpackr-extract-win32-x64-3.0.2.tgz"
  integrity sha512-O+6Gs8UeDbyFpbSh2CPEz/UOrrdWPTBYNblZK5CxxLisYt4kGX3Sc+czffFonyjiGSq3jWLwJS/CCJc7tBr4sQ==

"@nestjs/bull-shared@^0.1.3":
  version "0.1.3"
  resolved "https://registry.npmjs.org/@nestjs/bull-shared/-/bull-shared-0.1.3.tgz"
  integrity sha512-K0a1ERpnl/ZnTmm0UtYSSClDlDkQwNNwJYM6PogzpeflD64oqwVIn8Pj8rdS+BOYUxqdDy55q3p67ytO5oaVDA==
  dependencies:
    tslib "2.5.0"

"@nestjs/bull@^0.6.0":
  version "0.6.3"
  resolved "https://registry.npmjs.org/@nestjs/bull/-/bull-0.6.3.tgz"
  integrity sha512-CckH9O3t9qSiO4RCzdYvtFSaaMfIhTXMYagV/rtmVvI1SX5XNnxEaQXvtjxDBXF9DB1JE/5AejIl6ICym+MJIw==
  dependencies:
    "@nestjs/bull-shared" "^0.1.3"
    tslib "2.5.0"

"@nestjs/cli@^9.0.0":
  version "9.5.0"
  resolved "https://registry.npmjs.org/@nestjs/cli/-/cli-9.5.0.tgz"
  integrity sha512-Z7q+3vNsQSG2d2r2Hl/OOj5EpfjVx3OfnJ9+KuAsOdw1sKLm7+Zc6KbhMFTd/eIvfx82ww3Nk72xdmfPYCulWA==
  dependencies:
    "@angular-devkit/core" "16.0.1"
    "@angular-devkit/schematics" "16.0.1"
    "@angular-devkit/schematics-cli" "16.0.1"
    "@nestjs/schematics" "^9.0.4"
    chalk "4.1.2"
    chokidar "3.5.3"
    cli-table3 "0.6.3"
    commander "4.1.1"
    fork-ts-checker-webpack-plugin "8.0.0"
    inquirer "8.2.5"
    node-emoji "1.11.0"
    ora "5.4.1"
    os-name "4.0.1"
    rimraf "4.4.1"
    shelljs "0.8.5"
    source-map-support "0.5.21"
    tree-kill "1.2.2"
    tsconfig-paths "4.2.0"
    tsconfig-paths-webpack-plugin "4.0.1"
    typescript "4.9.5"
    webpack "5.82.1"
    webpack-node-externals "3.0.0"

"@nestjs/common@^9.0.0", "@nestjs/common@^9.0.11":
  version "9.4.3"
  resolved "https://registry.npmjs.org/@nestjs/common/-/common-9.4.3.tgz"
  integrity sha512-Gd6D4IaYj01o14Bwv81ukidn4w3bPHCblMUq+SmUmWLyosK+XQmInCS09SbDDZyL8jy86PngtBLTdhJ2bXSUig==
  dependencies:
    uid "2.0.2"
    iterare "1.2.1"
    tslib "2.5.3"

"@nestjs/config@^2.2.0":
  version "2.3.4"
  resolved "https://registry.npmjs.org/@nestjs/config/-/config-2.3.4.tgz"
  integrity sha512-IGdSF+0F9MJO6dCRTEahdxPz4iVijjtolcFBxnY+2QYM3bXYQvAgzskGZi+WkAFJN/VzR3TEp60gN5sI74GxPA==
  dependencies:
    dotenv "16.1.4"
    dotenv-expand "10.0.0"
    lodash "4.17.21"
    uuid "9.0.0"

"@nestjs/core@^9.0.0":
  version "9.4.3"
  resolved "https://registry.npmjs.org/@nestjs/core/-/core-9.4.3.tgz"
  integrity sha512-Qi63+wi55Jh4sDyaj5Hhx2jOpKqT386aeo+VOKsxnd+Ql9VvkO/FjmuwBGUyzkJt29ENYc+P0Sx/k5LtstNpPQ==
  dependencies:
    uid "2.0.2"
    "@nuxtjs/opencollective" "0.3.2"
    fast-safe-stringify "2.1.1"
    iterare "1.2.1"
    path-to-regexp "3.2.0"
    tslib "2.5.3"

"@nestjs/event-emitter@^2.1.1":
  version "2.1.1"
  resolved "https://registry.npmjs.org/@nestjs/event-emitter/-/event-emitter-2.1.1.tgz"
  integrity sha512-6L6fBOZTyfFlL7Ih/JDdqlCzZeCW0RjCX28wnzGyg/ncv5F/EOeT1dfopQr1loBRQ3LTgu8OWM7n4zLN4xigsg==
  dependencies:
    eventemitter2 "6.4.9"

"@nestjs/mapped-types@*":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@nestjs/mapped-types/-/mapped-types-2.0.4.tgz"
  integrity sha512-xl+gUSp0B+ln1VSNoUftlglk8dfpUes3DHGxKZ5knuBxS5g2H/8p9/DSBOYWUfO5f4u9s6ffBPZ71WO+tbe5SA==

"@nestjs/mapped-types@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@nestjs/mapped-types/-/mapped-types-1.2.2.tgz"
  integrity sha512-3dHxLXs3M0GPiriAcCFFJQHoDFUuzTD5w6JDhE7TyfT89YKpe6tcCCIqOZWdXmt9AZjjK30RkHRSFF+QEnWFQg==

"@nestjs/mongoose@^9.2.0":
  version "9.2.2"
  resolved "https://registry.npmjs.org/@nestjs/mongoose/-/mongoose-9.2.2.tgz"
  integrity sha512-szNuSUCwwbQSSeiTh8+tZ9fHV4nuzHwBDROb0hX0s7crwY15TunCfwyKbB2XjqkEQWUAasDeCBuKOJSL9N6tTg==

"@nestjs/platform-express@^9.4.3":
  version "9.4.3"
  resolved "https://registry.npmjs.org/@nestjs/platform-express/-/platform-express-9.4.3.tgz"
  integrity sha512-FpdczWoRSC0zz2dNL9u2AQLXKXRVtq4HgHklAhbL59X0uy+mcxhlSThG7DHzDMkoSnuuHY8ojDVf7mDxk+GtCw==
  dependencies:
    body-parser "1.20.2"
    cors "2.8.5"
    express "4.18.2"
    multer "1.4.4-lts.1"
    tslib "2.5.3"

"@nestjs/schedule@^4.1.1":
  version "4.1.1"
  resolved "https://registry.npmjs.org/@nestjs/schedule/-/schedule-4.1.1.tgz"
  integrity sha512-VxAnCiU4HP0wWw8IdWAVfsGC/FGjyToNjjUtXDEQL6oj+w/N5QDd2VT9k6d7Jbr8PlZuBZNdWtDKSkH5bZ+RXQ==
  dependencies:
    cron "3.1.7"
    uuid "10.0.0"

"@nestjs/schematics@^9.0.0", "@nestjs/schematics@^9.0.4":
  version "9.2.0"
  resolved "https://registry.npmjs.org/@nestjs/schematics/-/schematics-9.2.0.tgz"
  integrity sha512-wHpNJDPzM6XtZUOB3gW0J6mkFCSJilzCM3XrHI1o0C8vZmFE1snbmkIXNyoi1eV0Nxh1BMymcgz5vIMJgQtTqw==
  dependencies:
    "@angular-devkit/core" "16.0.1"
    "@angular-devkit/schematics" "16.0.1"
    jsonc-parser "3.2.0"
    pluralize "8.0.0"

"@nestjs/swagger@^6.0.4":
  version "6.3.0"
  resolved "https://registry.npmjs.org/@nestjs/swagger/-/swagger-6.3.0.tgz"
  integrity sha512-Gnig189oa1tD+h0BYIfUwhp/wvvmTn6iO3csR2E4rQrDTgCxSxZDlNdfZo3AC+Rmf8u0KX4ZAX1RZN1qXTtC7A==
  dependencies:
    "@nestjs/mapped-types" "1.2.2"
    js-yaml "4.1.0"
    lodash "4.17.21"
    path-to-regexp "3.2.0"
    swagger-ui-dist "4.18.2"

"@nestjs/testing@^9.0.0":
  version "9.4.3"
  resolved "https://registry.npmjs.org/@nestjs/testing/-/testing-9.4.3.tgz"
  integrity sha512-LDT8Ai2eKnTzvnPaJwWOK03qTaFap5uHHsJCv6dL0uKWk6hyF9jms8DjyVaGsaujCaXDG8izl1mDEER0OmxaZA==
  dependencies:
    tslib "2.5.3"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxtjs/opencollective@0.3.2":
  version "0.3.2"
  resolved "https://registry.npmjs.org/@nuxtjs/opencollective/-/opencollective-0.3.2.tgz"
  integrity sha512-um0xL3fO7Mf4fDxcqx9KryrB7zgRM5JSlvGN5AGkP6JLM5XEKyjeAiPbNxdXVXQ16isuAhYpvP88NgL2BGd6aA==
  dependencies:
    chalk "^4.1.0"
    consola "^2.15.0"
    node-fetch "^2.6.1"

"@opentelemetry/api@^1.4.1":
  version "1.7.0"
  resolved "https://registry.npmjs.org/@opentelemetry/api/-/api-1.7.0.tgz"
  integrity sha512-AdY5wvN0P2vXBi3b29hxZgSFvdhdxPB9+f0B6s//P9Q8nibRWeA3cHm8UmLpio9ABigkVHJ5NMPk+Mz8VCCyrw==

"@opentelemetry/core@1.19.0", "@opentelemetry/core@^1.11.0":
  version "1.19.0"
  resolved "https://registry.npmjs.org/@opentelemetry/core/-/core-1.19.0.tgz"
  integrity sha512-w42AukJh3TP8R0IZZOVJVM/kMWu8g+lm4LzT70WtuKqhwq7KVhcDzZZuZinWZa6TtQCl7Smt2wolEYzpHabOgw==
  dependencies:
    "@opentelemetry/semantic-conventions" "1.19.0"

"@opentelemetry/resources@1.19.0":
  version "1.19.0"
  resolved "https://registry.npmjs.org/@opentelemetry/resources/-/resources-1.19.0.tgz"
  integrity sha512-RgxvKuuMOf7nctOeOvpDjt2BpZvZGr9Y0vf7eGtY5XYZPkh2p7e2qub1S2IArdBMf9kEbz0SfycqCviOu9isqg==
  dependencies:
    "@opentelemetry/core" "1.19.0"
    "@opentelemetry/semantic-conventions" "1.19.0"

"@opentelemetry/sdk-metrics@^1.12.0":
  version "1.19.0"
  resolved "https://registry.npmjs.org/@opentelemetry/sdk-metrics/-/sdk-metrics-1.19.0.tgz"
  integrity sha512-FiMii40zr0Fmys4F1i8gmuCvbinBnBsDeGBr4FQemOf0iPCLytYQm5AZJ/nn4xSc71IgKBQwTFQRAGJI7JvZ4Q==
  dependencies:
    "@opentelemetry/core" "1.19.0"
    "@opentelemetry/resources" "1.19.0"
    lodash.merge "^4.6.2"

"@opentelemetry/semantic-conventions@1.19.0":
  version "1.19.0"
  resolved "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.19.0.tgz"
  integrity sha512-14jRpC8f5c0gPSwoZ7SbEJni1PqI+AhAE8m1bMz6v+RPM4OlP1PT2UHBJj5Qh/ALLPjhVU/aZUK3YyjTUqqQVg==

"@sideway/address@^4.1.3":
  version "4.1.4"
  resolved "https://registry.npmjs.org/@sideway/address/-/address-4.1.4.tgz"
  integrity sha512-7vwq+rOHVWjyXxVlR76Agnvhy8I9rpzjosTESvmhNeXOXdZZB15Fl+TI9x1SiHZH5Jv2wTGduSxFDIaq0m3DUw==
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  version "3.0.1"
  resolved "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz"
  integrity sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz"
  integrity sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==

"@sinclair/typebox@^0.24.1":
  version "0.24.51"
  resolved "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.24.51.tgz"
  integrity sha512-1P1OROm/rdubP5aFDSZQILU0vrLCJ4fvHt6EoqHEM+2D/G5MK3bIaymUKLit8Js9gbns5UyJnkP/TZROLw4tUA==

"@sinonjs/commons@^1.7.0":
  version "1.8.6"
  resolved "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz"
  integrity sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^9.1.2":
  version "9.1.2"
  resolved "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.1.2.tgz"
  integrity sha512-BPS4ynJW/o92PUR4wgriz2Ud5gpST5vz6GQfMixEDK0Z8ZCUv2M7SkBLykH56T++Xs+8ln9zTGbOvNGIe02/jw==
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@smithy/abort-controller@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-2.0.15.tgz"
  integrity sha512-JkS36PIS3/UCbq/MaozzV7jECeL+BTt4R75bwY8i+4RASys4xOyUS1HsRyUNSqUXFP4QyCz5aNnh3ltuaxv+pw==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/config-resolver@^2.0.21":
  version "2.0.21"
  resolved "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-2.0.21.tgz"
  integrity sha512-rlLIGT+BeqjnA6C2FWumPRJS1UW07iU5ZxDHtFuyam4W65gIaOFMjkB90ofKCIh+0mLVQrQFrl/VLtQT/6FWTA==
  dependencies:
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/types" "^2.7.0"
    "@smithy/util-config-provider" "^2.0.0"
    "@smithy/util-middleware" "^2.0.8"
    tslib "^2.5.0"

"@smithy/core@^1.1.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@smithy/core/-/core-1.2.0.tgz"
  integrity sha512-l8R89X7+hlt2FEFg+OrNq29LP3h9DfGPmO6ObwT9IXWHD6V7ycpj5u2rVQyIis26ovrgOYakl6nfgmPMm8m1IQ==
  dependencies:
    "@smithy/middleware-endpoint" "^2.2.3"
    "@smithy/middleware-retry" "^2.0.24"
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/util-middleware" "^2.0.8"
    tslib "^2.5.0"

"@smithy/credential-provider-imds@^2.0.0", "@smithy/credential-provider-imds@^2.1.4":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-2.1.4.tgz"
  integrity sha512-cwPJN1fa1YOQzhBlTXRavABEYRRchci1X79QRwzaNLySnIMJfztyv1Zkst0iZPLMnpn8+CnHu3wOHS11J5Dr3A==
  dependencies:
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/property-provider" "^2.0.16"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    tslib "^2.5.0"

"@smithy/eventstream-codec@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-2.0.15.tgz"
  integrity sha512-crjvz3j1gGPwA0us6cwS7+5gAn35CTmqu/oIxVbYJo2Qm/sGAye6zGJnMDk3BKhWZw5kcU1G4MxciTkuBpOZPg==
  dependencies:
    "@aws-crypto/crc32" "3.0.0"
    "@smithy/types" "^2.7.0"
    "@smithy/util-hex-encoding" "^2.0.0"
    tslib "^2.5.0"

"@smithy/fetch-http-handler@^2.3.1":
  version "2.3.1"
  resolved "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-2.3.1.tgz"
  integrity sha512-6MNk16fqb8EwcYY8O8WxB3ArFkLZ2XppsSNo1h7SQcFdDDwIumiJeO6wRzm7iB68xvsOQzsdQKbdtTieS3hfSQ==
  dependencies:
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/querystring-builder" "^2.0.15"
    "@smithy/types" "^2.7.0"
    "@smithy/util-base64" "^2.0.1"
    tslib "^2.5.0"

"@smithy/hash-node@^2.0.17":
  version "2.0.17"
  resolved "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-2.0.17.tgz"
  integrity sha512-Il6WuBcI1nD+e2DM7tTADMf01wEPGK8PAhz4D+YmDUVaoBqlA+CaH2uDJhiySifmuKBZj748IfygXty81znKhw==
  dependencies:
    "@smithy/types" "^2.7.0"
    "@smithy/util-buffer-from" "^2.0.0"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@smithy/invalid-dependency@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-2.0.15.tgz"
  integrity sha512-dlEKBFFwVfzA5QroHlBS94NpgYjXhwN/bFfun+7w3rgxNvVy79SK0w05iGc7UAeC5t+D7gBxrzdnD6hreZnDVQ==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/is-array-buffer@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-2.0.0.tgz"
  integrity sha512-z3PjFjMyZNI98JFRJi/U0nGoLWMSJlDjAW4QUX2WNZLas5C0CmVV6LJ01JI0k90l7FvpmixjWxPFmENSClQ7ug==
  dependencies:
    tslib "^2.5.0"

"@smithy/middleware-content-length@^2.0.17":
  version "2.0.17"
  resolved "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-2.0.17.tgz"
  integrity sha512-OyadvMcKC7lFXTNBa8/foEv7jOaqshQZkjWS9coEXPRZnNnihU/Ls+8ZuJwGNCOrN2WxXZFmDWhegbnM4vak8w==
  dependencies:
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/middleware-endpoint@^2.2.3":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-2.2.3.tgz"
  integrity sha512-nYfxuq0S/xoAjdLbyn1ixeVB6cyH9wYCMtbbOCpcCRYR5u2mMtqUtVjjPAZ/DIdlK3qe0tpB0Q76szFGNuz+kQ==
  dependencies:
    "@smithy/middleware-serde" "^2.0.15"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/shared-ini-file-loader" "^2.2.7"
    "@smithy/types" "^2.7.0"
    "@smithy/url-parser" "^2.0.15"
    "@smithy/util-middleware" "^2.0.8"
    tslib "^2.5.0"

"@smithy/middleware-retry@^2.0.24":
  version "2.0.24"
  resolved "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-2.0.24.tgz"
  integrity sha512-q2SvHTYu96N7lYrn3VSuX3vRpxXHR/Cig6MJpGWxd0BWodUQUWlKvXpWQZA+lTaFJU7tUvpKhRd4p4MU3PbeJg==
  dependencies:
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/service-error-classification" "^2.0.8"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    "@smithy/util-middleware" "^2.0.8"
    "@smithy/util-retry" "^2.0.8"
    tslib "^2.5.0"
    uuid "^8.3.2"

"@smithy/middleware-serde@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-2.0.15.tgz"
  integrity sha512-FOZRFk/zN4AT4wzGuBY+39XWe+ZnCFd0gZtyw3f9Okn2CJPixl9GyWe98TIaljeZdqWkgrzGyPre20AcW2UMHQ==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/middleware-stack@^2.0.9":
  version "2.0.9"
  resolved "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-2.0.9.tgz"
  integrity sha512-bCB5dUtGQ5wh7QNL2ELxmDc6g7ih7jWU3Kx6MYH1h4mZbv9xL3WyhKHojRltThCB1arLPyTUFDi+x6fB/oabtA==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/node-config-provider@^2.1.8":
  version "2.1.8"
  resolved "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-2.1.8.tgz"
  integrity sha512-+w26OKakaBUGp+UG+dxYZtFb5fs3tgHg3/QrRrmUZj+rl3cIuw840vFUXX35cVPTUCQIiTqmz7CpVF7+hdINdQ==
  dependencies:
    "@smithy/property-provider" "^2.0.16"
    "@smithy/shared-ini-file-loader" "^2.2.7"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/node-http-handler@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-2.2.1.tgz"
  integrity sha512-8iAKQrC8+VFHPAT8pg4/j6hlsTQh+NKOWlctJBrYtQa4ExcxX7aSg3vdQ2XLoYwJotFUurg/NLqFCmZaPRrogw==
  dependencies:
    "@smithy/abort-controller" "^2.0.15"
    "@smithy/protocol-http" "^3.0.11"
    "@smithy/querystring-builder" "^2.0.15"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/property-provider@^2.0.0", "@smithy/property-provider@^2.0.16":
  version "2.0.16"
  resolved "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-2.0.16.tgz"
  integrity sha512-28Ky0LlOqtEjwg5CdHmwwaDRHcTWfPRzkT6HrhwOSRS2RryAvuDfJrZpM+BMcrdeCyEg1mbcgIMoqTla+rdL8Q==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/protocol-http@^3.0.11":
  version "3.0.11"
  resolved "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-3.0.11.tgz"
  integrity sha512-3ziB8fHuXIRamV/akp/sqiWmNPR6X+9SB8Xxnozzj+Nq7hSpyKdFHd1FLpBkgfGFUTzzcBJQlDZPSyxzmdcx5A==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/querystring-builder@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-2.0.15.tgz"
  integrity sha512-e1q85aT6HutvouOdN+dMsN0jcdshp50PSCvxDvo6aIM57LqeXimjfONUEgfqQ4IFpYWAtVixptyIRE5frMp/2A==
  dependencies:
    "@smithy/types" "^2.7.0"
    "@smithy/util-uri-escape" "^2.0.0"
    tslib "^2.5.0"

"@smithy/querystring-parser@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-2.0.15.tgz"
  integrity sha512-jbBvoK3cc81Cj1c1TH1qMYxNQKHrYQ2DoTntN9FBbtUWcGhc+T4FP6kCKYwRLXyU4AajwGIZstvNAmIEgUUNTQ==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/service-error-classification@^2.0.8":
  version "2.0.8"
  resolved "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-2.0.8.tgz"
  integrity sha512-jCw9+005im8tsfYvwwSc4TTvd29kXRFkH9peQBg5R/4DD03ieGm6v6Hpv9nIAh98GwgYg1KrztcINC1s4o7/hg==
  dependencies:
    "@smithy/types" "^2.7.0"

"@smithy/shared-ini-file-loader@^2.0.6", "@smithy/shared-ini-file-loader@^2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-2.2.7.tgz"
  integrity sha512-0Qt5CuiogIuvQIfK+be7oVHcPsayLgfLJGkPlbgdbl0lD28nUKu4p11L+UG3SAEsqc9UsazO+nErPXw7+IgDpQ==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/signature-v4@^2.0.0":
  version "2.0.18"
  resolved "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-2.0.18.tgz"
  integrity sha512-SJRAj9jT/l9ocm8D0GojMbnA1sp7I4JeStOQ4lEXI8A5eHE73vbjlzlqIFB7cLvIgau0oUl4cGVpF9IGCrvjlw==
  dependencies:
    "@smithy/eventstream-codec" "^2.0.15"
    "@smithy/is-array-buffer" "^2.0.0"
    "@smithy/types" "^2.7.0"
    "@smithy/util-hex-encoding" "^2.0.0"
    "@smithy/util-middleware" "^2.0.8"
    "@smithy/util-uri-escape" "^2.0.0"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@smithy/smithy-client@^2.1.18":
  version "2.1.18"
  resolved "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-2.1.18.tgz"
  integrity sha512-7FqdbaJiVaHJDD9IfDhmzhSDbpjyx+ZsfdYuOpDJF09rl8qlIAIlZNoSaflKrQ3cEXZN2YxGPaNWGhbYimyIRQ==
  dependencies:
    "@smithy/middleware-stack" "^2.0.9"
    "@smithy/types" "^2.7.0"
    "@smithy/util-stream" "^2.0.23"
    tslib "^2.5.0"

"@smithy/types@^2.7.0":
  version "2.7.0"
  resolved "https://registry.npmjs.org/@smithy/types/-/types-2.7.0.tgz"
  integrity sha512-1OIFyhK+vOkMbu4aN2HZz/MomREkrAC/HqY5mlJMUJfGrPRwijJDTeiN8Rnj9zUaB8ogXAfIOtZrrgqZ4w7Wnw==
  dependencies:
    tslib "^2.5.0"

"@smithy/url-parser@^2.0.15":
  version "2.0.15"
  resolved "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-2.0.15.tgz"
  integrity sha512-sADUncUj9rNbOTrdDGm4EXlUs0eQ9dyEo+V74PJoULY4jSQxS+9gwEgsPYyiu8PUOv16JC/MpHonOgqP/IEDZA==
  dependencies:
    "@smithy/querystring-parser" "^2.0.15"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/util-base64@^2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-2.0.1.tgz"
  integrity sha512-DlI6XFYDMsIVN+GH9JtcRp3j02JEVuWIn/QOZisVzpIAprdsxGveFed0bjbMRCqmIFe8uetn5rxzNrBtIGrPIQ==
  dependencies:
    "@smithy/util-buffer-from" "^2.0.0"
    tslib "^2.5.0"

"@smithy/util-body-length-browser@^2.0.1":
  version "2.0.1"
  resolved "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-2.0.1.tgz"
  integrity sha512-NXYp3ttgUlwkaug4bjBzJ5+yIbUbUx8VsSLuHZROQpoik+gRkIBeEG9MPVYfvPNpuXb/puqodeeUXcKFe7BLOQ==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-body-length-node@^2.1.0":
  version "2.1.0"
  resolved "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-2.1.0.tgz"
  integrity sha512-/li0/kj/y3fQ3vyzn36NTLGmUwAICb7Jbe/CsWCktW363gh1MOcpEcSO3mJ344Gv2dqz8YJCLQpb6hju/0qOWw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-buffer-from@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-2.0.0.tgz"
  integrity sha512-/YNnLoHsR+4W4Vf2wL5lGv0ksg8Bmk3GEGxn2vEQt52AQaPSCuaO5PM5VM7lP1K9qHRKHwrPGktqVoAHKWHxzw==
  dependencies:
    "@smithy/is-array-buffer" "^2.0.0"
    tslib "^2.5.0"

"@smithy/util-config-provider@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-2.0.0.tgz"
  integrity sha512-xCQ6UapcIWKxXHEU4Mcs2s7LcFQRiU3XEluM2WcCjjBtQkUN71Tb+ydGmJFPxMUrW/GWMgQEEGipLym4XG0jZg==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-defaults-mode-browser@^2.0.22":
  version "2.0.22"
  resolved "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-2.0.22.tgz"
  integrity sha512-qcF20IHHH96FlktvBRICDXDhLPtpVmtksHmqNGtotb9B0DYWXsC6jWXrkhrrwF7tH26nj+npVTqh9isiFV1gdA==
  dependencies:
    "@smithy/property-provider" "^2.0.16"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    bowser "^2.11.0"
    tslib "^2.5.0"

"@smithy/util-defaults-mode-node@^2.0.29":
  version "2.0.29"
  resolved "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-2.0.29.tgz"
  integrity sha512-+uG/15VoUh6JV2fdY9CM++vnSuMQ1VKZ6BdnkUM7R++C/vLjnlg+ToiSR1FqKZbMmKBXmsr8c/TsDWMAYvxbxQ==
  dependencies:
    "@smithy/config-resolver" "^2.0.21"
    "@smithy/credential-provider-imds" "^2.1.4"
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/property-provider" "^2.0.16"
    "@smithy/smithy-client" "^2.1.18"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/util-endpoints@^1.0.7":
  version "1.0.7"
  resolved "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-1.0.7.tgz"
  integrity sha512-Q2gEind3jxoLk6hdKWyESMU7LnXz8aamVwM+VeVjOYzYT1PalGlY/ETa48hv2YpV4+YV604y93YngyzzzQ4IIA==
  dependencies:
    "@smithy/node-config-provider" "^2.1.8"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/util-hex-encoding@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-2.0.0.tgz"
  integrity sha512-c5xY+NUnFqG6d7HFh1IFfrm3mGl29lC+vF+geHv4ToiuJCBmIfzx6IeHLg+OgRdPFKDXIw6pvi+p3CsscaMcMA==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-middleware@^2.0.8":
  version "2.0.8"
  resolved "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-2.0.8.tgz"
  integrity sha512-qkvqQjM8fRGGA8P2ydWylMhenCDP8VlkPn8kiNuFEaFz9xnUKC2irfqsBSJrfrOB9Qt6pQsI58r3zvvumhFMkw==
  dependencies:
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/util-retry@^2.0.8":
  version "2.0.8"
  resolved "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-2.0.8.tgz"
  integrity sha512-cQTPnVaVFMjjS6cb44WV2yXtHVyXDC5icKyIbejMarJEApYeJWpBU3LINTxHqp/tyLI+MZOUdosr2mZ3sdziNg==
  dependencies:
    "@smithy/service-error-classification" "^2.0.8"
    "@smithy/types" "^2.7.0"
    tslib "^2.5.0"

"@smithy/util-stream@^2.0.23":
  version "2.0.23"
  resolved "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-2.0.23.tgz"
  integrity sha512-OJMWq99LAZJUzUwTk+00plyxX3ESktBaGPhqNIEVab+53gLULiWN9B/8bRABLg0K6R6Xg4t80uRdhk3B/LZqMQ==
  dependencies:
    "@smithy/fetch-http-handler" "^2.3.1"
    "@smithy/node-http-handler" "^2.2.1"
    "@smithy/types" "^2.7.0"
    "@smithy/util-base64" "^2.0.1"
    "@smithy/util-buffer-from" "^2.0.0"
    "@smithy/util-hex-encoding" "^2.0.0"
    "@smithy/util-utf8" "^2.0.2"
    tslib "^2.5.0"

"@smithy/util-uri-escape@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-2.0.0.tgz"
  integrity sha512-ebkxsqinSdEooQduuk9CbKcI+wheijxEb3utGXkCoYQkJnwTnLbH1JXGimJtUkQwNQbsbuYwG2+aFVyZf5TLaw==
  dependencies:
    tslib "^2.5.0"

"@smithy/util-utf8@^2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-2.0.2.tgz"
  integrity sha512-qOiVORSPm6Ce4/Yu6hbSgNHABLP2VMv8QOC3tTDNHHlWY19pPyc++fBTbZPtx6egPXi4HQxKDnMxVxpbtX2GoA==
  dependencies:
    "@smithy/util-buffer-from" "^2.0.0"
    tslib "^2.5.0"

"@streamparser/json@^0.0.6":
  version "0.0.6"
  resolved "https://registry.npmjs.org/@streamparser/json/-/json-0.0.6.tgz"
  integrity sha512-vL9EVn/v+OhZ+Wcs6O4iKE9EUpwHUqHmCtNUMWjqp+6dr85+XPOSGTEsqYNq1Vn04uk9SWlOVmx9J48ggJVT2Q==

"@taptap-discovery/alerts@^1.0.5":
  version "1.0.7"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-discovery/alerts/-/alerts-1.0.7.tgz"
  integrity sha512-yeu5AdO51gbE+a6ZRtqkL+dEP8ey3Un7y0eL8dYhuuVBSWCuYooLTNZxYwbnKhUlSlVPp8p2ca9cENFcKIs72g==
  dependencies:
    axios "^1.6.2"
    lru-cache "^10.1.0"
    md5 "^2.3.0"

"@taptap-discovery/api-logger@^1.0.0":
  version "1.0.2"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-discovery/api-logger/-/api-logger-1.0.2.tgz"
  integrity sha512-9bRf9kAUiwXUuXsrrTEcYgoqYR+uB/PaB0aIzRqmT90TqtR/JJYYM5vl1hiMiOXhkBJGOjbohAZZQoRgp1yIMg==
  dependencies:
    "@types/node" "^20.10.0"
    express "^4.18.2"
    g "^2.0.1"

"@taptap-discovery/credentials@^2.0.5":
  version "2.0.5"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-discovery/credentials/-/credentials-2.0.5.tgz"
  integrity sha512-tZLTVyCQspVMxA3d9m98isq749mXOTtxrhYEStINuc9kiqXtbcudFYqidkhEgJCVp1dn8oqI2OW4AHZvOqA91g==
  dependencies:
    axios "^1.6.2"
    jsonwebtoken "^9.0.2"
    lru-cache "^10.1.0"

"@taptap-discovery/http-exception-filter@^1.0.2":
  version "1.0.2"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-discovery/http-exception-filter/-/http-exception-filter-1.0.2.tgz"
  integrity sha512-AVjBWHJkOgNVEM1H/GbKOOXjCSLaSugmtu+PJstW76wQZCJwlm8RON0cZQIxYMEMabjQaZ9gCrLugN7sfovrhQ==
  dependencies:
    "@nestjs/common" "^9.0.11"
    "@taptap-discovery/alerts" "^1.0.5"
    class-validator "^0.14.0"

"@taptap-discovery/http-request-utils@^1.0.1", "@taptap-discovery/http-request-utils@^1.0.5":
  version "1.0.5"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-discovery/http-request-utils/-/http-request-utils-1.0.5.tgz"
  integrity sha512-PKUqMAV+bmNPwzFKSRIOxmoIWaw5ayA8BxreVea2vI+YsuSXem+Vo0gSn805FhRfxeZ6D1ED+8vOQxWvh2k6IQ==
  dependencies:
    ajv "^8.12.0"
    axios "^1.6.2"
    g "^2.0.1"
    lru-cache "^10.1.0"

"@taptap-lyt/sdk@^0.1.3":
  version "0.1.3"
  resolved "https://cm-nexus.taptap.com.vn/repository/taptap-npm/@taptap-lyt/sdk/-/sdk-0.1.3.tgz"
  integrity sha512-ejwIMWkm2O8aPluw2tZqzEsqXEF/KfKeJnwglL1J/fkIR+/8Ll34WgSdu+8sKJP1AnpZB1qOsKDWMVThqqLFcA==
  dependencies:
    "@taptap-discovery/http-request-utils" "^1.0.5"
    axios "^1.7.7"
    uuid "^11.0.3"

"@tsconfig/node10@^1.0.7":
  version "1.0.9"
  resolved "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.9.tgz"
  integrity sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA==

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz"
  integrity sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz"
  integrity sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz"
  integrity sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==

"@types/babel__core@^7.1.14":
  version "7.20.5"
  resolved "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  integrity sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.8"
  resolved "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz"
  integrity sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  integrity sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.20.4"
  resolved "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.4.tgz"
  integrity sha512-mSM/iKUk5fDDrEV/e83qY+Cr3I1+Q3qqTuEn++HAWYjEa1+NxZr6CNrcJGf2ZTnq4HoFGC3zaTPZTobCzCFukA==
  dependencies:
    "@babel/types" "^7.20.7"

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bull@^3.15.9":
  version "3.15.9"
  resolved "https://registry.npmjs.org/@types/bull/-/bull-3.15.9.tgz"
  integrity sha512-MPUcyPPQauAmynoO3ezHAmCOhbB0pWmYyijr/5ctaCqhbKWsjW0YCod38ZcLzUBprosfZ9dPqfYIcfdKjk7RNQ==
  dependencies:
    "@types/ioredis" "*"
    "@types/redis" "^2.8.0"

"@types/cache-manager@^4.0.1":
  version "4.0.6"
  resolved "https://registry.npmjs.org/@types/cache-manager/-/cache-manager-4.0.6.tgz"
  integrity sha512-8qL93MF05/xrzFm/LSPtzNEOE1eQF3VwGHAcQEylgp5hDSTe41jtFwbSYAPfyYcVa28y1vYSjIt0c1fLLUiC/Q==

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/cookiejar@*":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@types/cookiejar/-/cookiejar-2.1.5.tgz"
  integrity sha512-he+DHOWReW0nghN24E1WUqM0efK4kI9oTqDm6XmK8ZPe2djZ90BSNdGnIyCLzCPw7/pogPlGbzI2wHGGmi4O/Q==

"@types/eslint-scope@^3.7.3":
  version "3.7.7"
  resolved "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  integrity sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.44.9"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-8.44.9.tgz"
  integrity sha512-6yBxcvwnnYoYT1Uk2d+jvIfsuP4mb2EdIxFnrPABj5a/838qe5bGkNLFOiipX4ULQ7XVQvTxOh7jO+BTAiqsEw==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.5"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz"
  integrity sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==

"@types/express-serve-static-core@^4.17.33":
  version "4.17.41"
  resolved "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.41.tgz"
  integrity sha512-OaJ7XLaelTgrvlZD8/aa0vvvxZdUmlCn6MtWeB7TkiKW70BQLc9XEPpDLPdbo52ZhXUCrznlWdCHWxJWtdyajA==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.21":
  version "4.17.21"
  resolved "https://registry.npmjs.org/@types/express/-/express-4.17.21.tgz"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz"
  integrity sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==
  dependencies:
    "@types/node" "*"

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/ioredis@*":
  version "5.0.0"
  resolved "https://registry.npmjs.org/@types/ioredis/-/ioredis-5.0.0.tgz"
  integrity sha512-zJbJ3FVE17CNl5KXzdeSPtdltc4tMT3TzC6fxQS0sQngkbFZ6h+0uTafsRqu+eSLIugf6Yb0Ea0SUuRr42Nk9g==
  dependencies:
    ioredis "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz"
  integrity sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz"
  integrity sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz"
  integrity sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@28.1.4":
  version "28.1.4"
  resolved "https://registry.npmjs.org/@types/jest/-/jest-28.1.4.tgz"
  integrity sha512-telv6G5N7zRJiLcI3Rs3o+ipZ28EnE+7EvF0pSrt2pZOMnAVI/f+6/LucDxOvcBcTeTL3JMF744BbVQAVBUQRA==
  dependencies:
    jest-matcher-utils "^28.0.0"
    pretty-format "^28.0.0"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/json2csv@^5.0.7":
  version "5.0.7"
  resolved "https://registry.npmjs.org/@types/json2csv/-/json2csv-5.0.7.tgz"
  integrity sha512-Ma25zw9G9GEBnX8b12R4EYvnFT6dBh8L3jwsN5EUFXa+fl2dqmbLDbNWN0XuQU3rSXdsbBeCYjI9uHU2PUBxhA==
  dependencies:
    "@types/node" "*"

"@types/lodash@^4.17.10":
  version "4.17.13"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.13.tgz"
  integrity sha512-lfx+dftrEZcdBPczf9d0Qv0x+j/rfNCMuC6OcfXmO8gkfeNAY88PgKUbvG56whcN23gc27yenwF6oJZXGFpYxg==

"@types/luxon@~3.4.0":
  version "3.4.2"
  resolved "https://registry.npmjs.org/@types/luxon/-/luxon-3.4.2.tgz"
  integrity sha512-TifLZlFudklWlMBfhubvgqTXRzLDI5pCbGa4P8a3wPyUQSW+***************************/nwundroWcA==

"@types/mime@*", "@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/multer@^1.4.12":
  version "1.4.12"
  resolved "https://registry.npmjs.org/@types/multer/-/multer-1.4.12.tgz"
  integrity sha512-pQ2hoqvXiJt2FP9WQVLPRO+AmiIm/ZYkavPlIQnx282u4ZrVdztx0pkh3jjpQt0Kz+YI0YhSG264y08UJKoUQg==
  dependencies:
    "@types/express" "*"

"@types/node@*", "@types/node@^16.0.0":
  version "16.18.68"
  resolved "https://registry.npmjs.org/@types/node/-/node-16.18.68.tgz"
  integrity sha512-sG3hPIQwJLoewrN7cr0dwEy+yF5nD4D/4FxtQpFciRD/xwUzgD+G05uxZHv5mhfXo4F9Jkp13jjn0CC2q325sg==

"@types/node@^20.10.0":
  version "20.10.5"
  resolved "https://registry.npmjs.org/@types/node/-/node-20.10.5.tgz"
  integrity sha512-nNPsNE65wjMxEKI93yOP+NPGGBJz/PoN3kZsVLee0XMiJolxSekEVD8wRwBUBqkwc7UWop0edW50yrCQW4CyRw==
  dependencies:
    undici-types "~5.26.4"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/prettier@^2.1.5":
  version "2.7.3"
  resolved "https://registry.npmjs.org/@types/prettier/-/prettier-2.7.3.tgz"
  integrity sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA==

"@types/qs@*":
  version "6.9.10"
  resolved "https://registry.npmjs.org/@types/qs/-/qs-6.9.10.tgz"
  integrity sha512-3Gnx08Ns1sEoCrWssEgTSJs/rsT2vhGP+Ja9cnnk9k4ALxinORlQneLXFeFKOTJMOeZUFD1s7w+w2AphTpvzZw==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/redis@^2.8.0":
  version "2.8.32"
  resolved "https://registry.npmjs.org/@types/redis/-/redis-2.8.32.tgz"
  integrity sha512-7jkMKxcGq9p242exlbsVzuJb57KqHRhNl4dHoQu2Y5v9bCAbtIXXH0R3HleSQW4CTOqpHIYUW3t6tpUj4BVQ+w==
  dependencies:
    "@types/node" "*"

"@types/semver@^7.3.12":
  version "7.5.6"
  resolved "https://registry.npmjs.org/@types/semver/-/semver-7.5.6.tgz"
  integrity sha512-dn1l8LaMea/IjDoHNd9J52uBbInB796CDffS6VdIxvqYCPSG0V0DzHp76GpaWnlhg88uYyPbXCDIowa86ybd5A==

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-static@*":
  version "1.15.5"
  resolved "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.5.tgz"
  integrity sha512-PDRk21MnK70hja/YF8AHfC7yIsiQHn1rcXx7ijCFBX/k+XQJhQT/gw3xekXKJvx+5SXaMMS8oqQy09Mzvz2TuQ==
  dependencies:
    "@types/http-errors" "*"
    "@types/mime" "*"
    "@types/node" "*"

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz"
  integrity sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==

"@types/superagent@*":
  version "4.1.24"
  resolved "https://registry.npmjs.org/@types/superagent/-/superagent-4.1.24.tgz"
  integrity sha512-mEafCgyKiMFin24SDzWN7yAADt4gt6YawFiNMp0QS5ZPboORfyxFt0s3VzJKhTaKg9py/4FUmrHLTNfJKt9Rbw==
  dependencies:
    "@types/cookiejar" "*"
    "@types/node" "*"

"@types/supertest@^2.0.11":
  version "2.0.16"
  resolved "https://registry.npmjs.org/@types/supertest/-/supertest-2.0.16.tgz"
  integrity sha512-6c2ogktZ06tr2ENoZivgm7YnprnhYE4ZoXGMY+oA7IuAf17M8FWvujXZGmxLv8y0PTyts4x5A+erSwVUFA8XSg==
  dependencies:
    "@types/superagent" "*"

"@types/uuid@^10.0.0":
  version "10.0.0"
  resolved "https://registry.npmjs.org/@types/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-7gqG38EyHgyP1S+7+xomFtL+ZNHcKv6DwNaCZmJmo1vgMugyF3TCnXVg4t1uk89mLNwnLtnY3TpOpCOyp1/xHQ==

"@types/validator@^13.7.10":
  version "13.11.7"
  resolved "https://registry.npmjs.org/@types/validator/-/validator-13.11.7.tgz"
  integrity sha512-q0JomTsJ2I5Mv7dhHhQLGjMvX0JJm5dyZ1DXQySIUzU1UlwzB8bt+R6+LODUbz0UDIOvEzGc28tk27gBJw2N8Q==

"@types/webidl-conversions@*":
  version "7.0.3"
  resolved "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz"
  integrity sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA==

"@types/whatwg-url@^8.2.1":
  version "8.2.2"
  resolved "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.2.tgz"
  integrity sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA==
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz"
  integrity sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==

"@types/yargs@^17.0.8":
  version "17.0.32"
  resolved "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.32.tgz"
  integrity sha512-xQ67Yc/laOG5uMfX/093MRlGGCIBzZMarVa+gfNKJxWAIgykYpVGkBdbqEzGDDfCrVUj6Hiff4mTZ5BA6TmAog==
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^5.0.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz"
  integrity sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.0.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.62.0.tgz"
  integrity sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  integrity sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz"
  integrity sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.62.0.tgz"
  integrity sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  integrity sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.62.0.tgz"
  integrity sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  integrity sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.2.0.tgz"
  integrity sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==

"@webassemblyjs/ast@1.12.1", "@webassemblyjs/ast@^1.11.5":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.12.1.tgz"
  integrity sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"

"@webassemblyjs/floating-point-hex-parser@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.6.tgz"
  integrity sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==

"@webassemblyjs/helper-api-error@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.6.tgz"
  integrity sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==

"@webassemblyjs/helper-buffer@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.12.1.tgz"
  integrity sha512-nzJwQw99DNDKr9BVCOZcLuJJUlqkJh+kVzVl6Fmq/tI5ZtEyWT1KZMyOXltXLZJmDtvLCDgwsyrkohEtopTXCw==

"@webassemblyjs/helper-numbers@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.6.tgz"
  integrity sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.6.tgz"
  integrity sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==

"@webassemblyjs/helper-wasm-section@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.12.1.tgz"
  integrity sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.12.1"

"@webassemblyjs/ieee754@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.6.tgz"
  integrity sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.6.tgz"
  integrity sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.6.tgz"
  integrity sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==

"@webassemblyjs/wasm-edit@^1.11.5":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.12.1.tgz"
  integrity sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/helper-wasm-section" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-opt" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"
    "@webassemblyjs/wast-printer" "1.12.1"

"@webassemblyjs/wasm-gen@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.12.1.tgz"
  integrity sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wasm-opt@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.12.1.tgz"
  integrity sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"

"@webassemblyjs/wasm-parser@1.12.1", "@webassemblyjs/wasm-parser@^1.11.5":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.12.1.tgz"
  integrity sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wast-printer@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.12.1.tgz"
  integrity sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz"
  integrity sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==
  dependencies:
    event-target-shim "^5.0.0"

accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.7.6, acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz"
  integrity sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-walk@^8.1.1:
  version "8.3.1"
  resolved "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.1.tgz"
  integrity sha512-TgUZgYvqZprrl7YldZNoa9OciCAyZR+Ejm9eXzKCmjsF5IKp/wgQ7Z/ZpjpGTIUPwrHQIcYeI8qDh4PsEwxMbw==

acorn@^8.4.1, acorn@^8.7.1, acorn@^8.8.2, acorn@^8.9.0:
  version "8.11.2"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz"
  integrity sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==

add@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/add/-/add-2.0.6.tgz"
  integrity sha512-j5QzrmsokwWWp6kUcJQySpbG+xfOBqqKnup3OIk1pz+kB/80SLorZ9V8zHFLO92Lcd+hbvq8bT+zOGoPkmBV0Q==

after-all-results@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/after-all-results/-/after-all-results-2.0.0.tgz"
  integrity sha512-2zHEyuhSJOuCrmas9YV0YL/MFCWLxe1dS6k/ENhgYrb/JqyMnadLN4iIAc9kkZrbElMDyyAGH/0J18OPErOWLg==

agentkeepalive@^4.2.1:
  version "4.5.0"
  resolved "https://registry.npmjs.org/agentkeepalive/-/agentkeepalive-4.5.0.tgz"
  integrity sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==
  dependencies:
    humanize-ms "^1.2.1"

ajv-formats@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv@8.12.0, ajv@^8.0.0, ajv@^8.12.0:
  version "8.12.0"
  resolved "https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz"
  integrity sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-colors@4.1.3:
  version "4.1.3"
  resolved "https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz"
  integrity sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  integrity sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz"
  integrity sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-field@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz"
  integrity sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw==

arg@^4.1.0:
  version "4.1.3"
  resolved "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz"
  integrity sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz"
  integrity sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

arraybuffer.prototype.slice@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.2.tgz"
  integrity sha512-yMBKppFur/fbHu9/6USUe03bZ4knMYiwFBcyiaXB8Go0qNehwX6inYPzK9U0NeQvGxKthcmHcaR8P5MStSRBAw==
  dependencies:
    array-buffer-byte-length "^1.0.0"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-array-buffer "^3.0.2"
    is-shared-array-buffer "^1.0.2"

asap@^2.0.0:
  version "2.0.6"
  resolved "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz"
  integrity sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==

async-value-promise@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/async-value-promise/-/async-value-promise-1.1.1.tgz"
  integrity sha512-c2RFDKjJle1rHa0YxN9Ysu97/QBu3Wa+NOejJxsX+1qVDJrkD3JL/GN1B3gaILAEXJXbu/4Z1lcoCHFESe/APA==
  dependencies:
    async-value "^1.2.2"

async-value@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/async-value/-/async-value-1.2.2.tgz"
  integrity sha512-8rwtYe32OAS1W9CTwvknoyts+mc3ta8N7Pi0h7AjkMaKvsFbr39K+gEfZ7Z81aPXQ1sK5M23lgLy1QfZpcpadQ==

async@3.2.3, async@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/async/-/async-3.2.3.tgz"
  integrity sha512-spZRyzKL5l5BZQrr/6m/SqFdBN0q3OCI0f9rjfBzCMBIP4p75P620rR3gTmaksNOhmzgdxcaxdNfMy6anrbM0g==

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atomic-sleep@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz"
  integrity sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
  integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==

axios@^0.27.2:
  version "0.27.2"
  resolved "https://registry.npmjs.org/axios/-/axios-0.27.2.tgz"
  integrity sha512-t+yRIyySRTp/wua5xEr+z1q60QmLq8ABsS5O9Me1AsE5dfKqgnCFzwiCZZ/cGNd1lq4/7akDWMxdhVlucjmnOQ==
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

axios@^1.6.2:
  version "1.6.2"
  resolved "https://registry.npmjs.org/axios/-/axios-1.6.2.tgz"
  integrity sha512-7i24Ri4pmDRfJTR7LDBhsOTtcm+9kjX5WiY1X3wIisx6G9So3pfMkEiU7emUBe46oceVImccTEM3k6C5dbVW8A==
  dependencies:
    follow-redirects "^1.15.0"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^1.7.7:
  version "1.7.7"
  resolved "https://registry.npmjs.org/axios/-/axios-1.7.7.tgz"
  integrity sha512-S4kL7XrjgBmvdGut0sN3yJxqYzrDOnivkBiN0OFs6hLiUam3UPvswUo0kqGyhqUZGEOytHyumEdXsAkgCOUf3Q==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

babel-jest@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/babel-jest/-/babel-jest-28.1.3.tgz"
  integrity sha512-epUaPOEWMk3cWX0M/sPvCHHCe9fMFAa/9hXEgKP8nFfNl/jlGkE9ucq9NqkZGXLDduCJYS0UvSlPUwC0S+rH6Q==
  dependencies:
    "@jest/transform" "^28.1.3"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^28.1.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.1.3.tgz"
  integrity sha512-Ys3tUKAmfnkRUpPdpa98eYrAR0nV+sSFUZZEGuQ2EbFd1y4SOLtD5QDNHAq+bb9a+bbXvYQC4b+ID/THIMcU6Q==
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-jest@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-28.1.3.tgz"
  integrity sha512-L+fupJvlWAHbQfn74coNX3zf60LXMJsezNvvx8eIh7iOR1luJ1poxYgQk1F8PYtNq/6QODDHCqsSnTFSWC491A==
  dependencies:
    babel-plugin-jest-hoist "^28.1.3"
    babel-preset-current-node-syntax "^1.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

basic-auth@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz"
  integrity sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==
  dependencies:
    safe-buffer "5.1.2"

bignumber.js@^9.0.0:
  version "9.1.2"
  resolved "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.1.2.tgz"
  integrity sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug==

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.2.0.tgz"
  integrity sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==

binary-search@^1.3.3:
  version "1.3.6"
  resolved "https://registry.npmjs.org/binary-search/-/binary-search-1.3.6.tgz"
  integrity sha512-nbE1WxOTTrUWIfsfZ4aHGYu5DOuNkbxGokjV6Z2kxfJK3uaAb8zNK1muzOeipoLHZjInT4Br88BHpzevc681xA==

bl@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.7.2:
  version "3.7.2"
  resolved "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

body-parser@1.19.2:
  version "1.19.2"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.19.2.tgz"
  integrity sha512-SAAwOxgoCKMGs9uUAUFHygfLAyaniaoun6I8mFY9pRAJL9+Kec34aU+oIjDhTycub1jozEfEwx1W1IuOYxVSFw==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.9.7"
    raw-body "2.4.3"
    type-is "~1.6.18"

body-parser@1.20.1:
  version "1.20.1"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz"
  integrity sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.1"
    type-is "~1.6.18"
    unpipe "1.0.0"

body-parser@1.20.2:
  version "1.20.2"
  resolved "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz"
  integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bowser@^2.11.0:
  version "2.11.0"
  resolved "https://registry.npmjs.org/bowser/-/bowser-2.11.0.tgz"
  integrity sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

breadth-filter@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/breadth-filter/-/breadth-filter-2.0.0.tgz"
  integrity sha512-thQShDXnFWSk2oVBixRCyrWsFoV5tfOpWKHmxwafHQDNxCfDBk539utpvytNjmlFrTMqz41poLwJvA1MW3z0MQ==
  dependencies:
    object.entries "^1.0.4"

browserslist@^4.14.5, browserslist@^4.22.2:
  version "4.22.2"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.22.2.tgz"
  integrity sha512-0UgcrvQmBDvZHFGdYUehrCNIazki7/lUP3kkoi/r3YB2amZbFM9J43ZRkJTXBUZK4gmx56+Sqk9+Vs9mwZx9+A==
  dependencies:
    caniuse-lite "^1.0.30001565"
    electron-to-chromium "^1.4.601"
    node-releases "^2.0.14"
    update-browserslist-db "^1.0.13"

bs-logger@0.x:
  version "0.2.6"
  resolved "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz"
  integrity sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==
  dependencies:
    fast-json-stable-stringify "2.x"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz"
  integrity sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==
  dependencies:
    node-int64 "^0.4.0"

bson@^4.7.2:
  version "4.7.2"
  resolved "https://registry.npmjs.org/bson/-/bson-4.7.2.tgz"
  integrity sha512-Ry9wCtIZ5kGqkJoi6aD8KjxFZEx78guTQDnpXWiNthsxzrxAK/i8E6pCHAIZTbaEFWcOCvbecMukfK7XUvyLpQ==
  dependencies:
    buffer "^5.6.0"

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer@^5.5.0, buffer@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

buffer@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz"
  integrity sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

bull@^4.8.5:
  version "4.12.0"
  resolved "https://registry.npmjs.org/bull/-/bull-4.12.0.tgz"
  integrity sha512-+GCM3KayIZvgiwAq5YC1qDcuncQbRLusLULOBZYRky7a7ttf4tlKWaFxTFtOfRrcb0erzFw6aWy73waorvR5pw==
  dependencies:
    cron-parser "^4.2.1"
    get-port "^5.1.1"
    ioredis "^5.3.2"
    lodash "^4.17.21"
    msgpackr "^1.5.2"
    semver "^7.5.2"
    uuid "^8.3.0"

busboy@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  integrity sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==
  dependencies:
    streamsearch "^1.1.0"

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

cache-manager-redis-store@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/cache-manager-redis-store/-/cache-manager-redis-store-2.0.0.tgz"
  integrity sha512-bWLWlUg6nCYHiJLCCYxY2MgvwvKnvlWwrbuynrzpjEIhfArD2GC9LtutIHFEPeyGVQN6C+WEw+P3r+BFBwhswg==
  dependencies:
    redis "^3.0.2"

cache-manager@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/cache-manager/-/cache-manager-4.1.0.tgz"
  integrity sha512-ZGM6dLxrP65bfOZmcviWMadUOCICqpLs92+P/S5tj8onz+k+tB7Gr+SAgOUHCQtfm2gYEQDHiKeul4+tYPOJ8A==
  dependencies:
    async "3.2.3"
    lodash.clonedeep "^4.5.0"
    lru-cache "^7.10.1"

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.4, call-bind@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.5.tgz"
  integrity sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ==
  dependencies:
    function-bind "^1.1.2"
    get-intrinsic "^1.2.1"
    set-function-length "^1.1.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-lite@^1.0.30001565:
  version "1.0.30001570"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001570.tgz"
  integrity sha512-+3e0ASu4sw1SWaoCtvPeyXp+5PsjigkSt8OXZbF9StH5pQWbxEjLAZE3n8Aup5udop1uRiKA7a4utUk/uoSpUw==

chalk@4.1.2, chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz"
  integrity sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz"
  integrity sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==

charenc@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/charenc/-/charenc-0.0.2.tgz"
  integrity sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==

chokidar@3.5.3, chokidar@^3.5.3:
  version "3.5.3"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.5.3.tgz"
  integrity sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  integrity sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==

ci-info@^3.2.0:
  version "3.9.0"
  resolved "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz"
  integrity sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==

cjs-module-lexer@^1.0.0, cjs-module-lexer@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz"
  integrity sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==

class-transformer@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmjs.org/class-transformer/-/class-transformer-0.5.1.tgz"
  integrity sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==

class-validator@^0.13.2:
  version "0.13.2"
  resolved "https://registry.npmjs.org/class-validator/-/class-validator-0.13.2.tgz"
  integrity sha512-yBUcQy07FPlGzUjoLuUfIOXzgynnQPPruyK1Ge2B74k9ROwnle1E+NxLWnUv5OLU8hA/qL5leAE9XnXq3byaBw==
  dependencies:
    libphonenumber-js "^1.9.43"
    validator "^13.7.0"

class-validator@^0.14.0:
  version "0.14.0"
  resolved "https://registry.npmjs.org/class-validator/-/class-validator-0.14.0.tgz"
  integrity sha512-ct3ltplN8I9fOwUd8GrP8UQixwff129BkEtuWDKL5W45cQuLd19xqmTLu5ge78YDm/fdje6FMt0hGOhl0lii3A==
  dependencies:
    "@types/validator" "^13.7.10"
    libphonenumber-js "^1.10.14"
    validator "^13.7.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

cli-table3@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.3.tgz"
  integrity sha512-w5Jac5SykAeZJKntOxJCrm63Eg5/4dhMWIcuTbo9rpE+brgaSZo0RuNJZeOyMgsUdhDeojvgyQLmjI+K50ZGyg==
  dependencies:
    string-width "^4.2.0"
  optionalDependencies:
    "@colors/colors" "1.5.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz"
  integrity sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz"
  integrity sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

cluster-key-slot@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/cluster-key-slot/-/cluster-key-slot-1.1.2.tgz"
  integrity sha512-RMr0FhtfXemyinomL4hrWcYJxmX6deFdCxpJzhDttxgO1+bcCnkk+9drydLVDmAMG7NE6aN/fl4F7ucU/90gAA==

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/co/-/co-4.6.0.tgz"
  integrity sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz"
  integrity sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz"
  integrity sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==

commander@4.1.1, commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^6.2.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/commander/-/commander-6.2.1.tgz"
  integrity sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==

component-emitter@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmjs.org/component-emitter/-/component-emitter-1.3.1.tgz"
  integrity sha512-T0+barUSQRTUQASh8bx02dl+DhF54GtIDY13Y3m9oWTklKbb3Wv974meRpeZ3lp1JpLVECWWNHC4vaG2XHXouQ==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

concat-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.npmjs.org/concat-stream/-/concat-stream-1.6.2.tgz"
  integrity sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

consola@^2.15.0:
  version "2.15.3"
  resolved "https://registry.npmjs.org/consola/-/consola-2.15.3.tgz"
  integrity sha512-9vAdYbHj6x2fLKC4+oPH0kFzY/orMZyG2Aj+kNylHxKGJ/Ed4dpNyAQYwJOdqO4zdM7XpVHmyejQDcQHrnuXbw==

console-log-level@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/console-log-level/-/console-log-level-1.4.1.tgz"
  integrity sha512-VZzbIORbP+PPcN/gg3DXClTLPLg5Slwd5fL2MIc+o1qZ4BXBvWyc6QxPk6T/Mkr6IVjRpoAGf32XxP3ZWMVRcQ==

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

convert-source-map@^1.4.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie-parser@^1.4.6:
  version "1.4.6"
  resolved "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.4.6.tgz"
  integrity sha512-z3IzaNjdwUC2olLIB5/ITd0/setiaFMLYiZJle7xg5Fe9KWAceil7xszYfHHBtDFYLSgJduS2Ty0P1uJdPDJeA==
  dependencies:
    cookie "0.4.1"
    cookie-signature "1.0.6"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==

cookie@0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz"
  integrity sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==

cookie@0.4.2:
  version "0.4.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz"
  integrity sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==

cookie@0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

cookie@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.6.0.tgz"
  integrity sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==

cookiejar@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmjs.org/cookiejar/-/cookiejar-2.1.4.tgz"
  integrity sha512-LDx6oHrK+PhzLKJU9j5S7/Y3jM/mUHvD/DeI1WQmJn652iPC5Y4TBzC9l+5OMOXlyTTA+SmVUPm0HQUwpD5Jqw==

core-util-is@^1.0.2, core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cors@2.8.5:
  version "2.8.5"
  resolved "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  integrity sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig@8.3.6:
  version "8.3.6"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-8.3.6.tgz"
  integrity sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

cosmiconfig@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz"
  integrity sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==

cron-parser@^4.2.1:
  version "4.9.0"
  resolved "https://registry.npmjs.org/cron-parser/-/cron-parser-4.9.0.tgz"
  integrity sha512-p0SaNjrHOnQeR8/VnfGbmg9te2kfyYSQ7Sc/j/6DtPL3JQvKxmjO9TSjNFpujqV3vEYYBvNNvXSxzyksBWAx1Q==
  dependencies:
    luxon "^3.2.1"

cron@3.1.7:
  version "3.1.7"
  resolved "https://registry.npmjs.org/cron/-/cron-3.1.7.tgz"
  integrity sha512-tlBg7ARsAMQLzgwqVxy8AZl/qlTc5nibqYwtNGoCrd+cV+ugI+tvZC1oT/8dFH8W455YrywGykx/KMmAqOr7Jw==
  dependencies:
    "@types/luxon" "~3.4.0"
    luxon "~3.4.0"

cross-env@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  integrity sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==
  dependencies:
    cross-spawn "^7.0.1"

cross-spawn@^7.0.0, cross-spawn@^7.0.1, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/crypt/-/crypt-0.0.2.tgz"
  integrity sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==

csv-parser@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/csv-parser/-/csv-parser-3.0.0.tgz"
  integrity sha512-s6OYSXAK3IdKqYO33y09jhypG/bSDHPuyCme/IdEHfWpLf/jKcpitVFyOC6UemgGk8v7Q5u2XE0vvwmanxhGlQ==
  dependencies:
    minimist "^1.2.0"

date-fns@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz"
  integrity sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@4.x, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz"
  integrity sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.1.tgz"
  integrity sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ==
  dependencies:
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

define-properties@^1.1.3, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

denque@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/denque/-/denque-1.5.1.tgz"
  integrity sha512-XwE+iZ4D6ZUB7mfYRMb5wByE8L74HCn30FBN7sWnXksWc1LO1bPDl67pBR9o/kC4z/xSNAwkMYcGgqDV3BE3Hw==

denque@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/denque/-/denque-2.1.0.tgz"
  integrity sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz"
  integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/destroy/-/destroy-1.0.4.tgz"
  integrity sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz"
  integrity sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==

dezalgo@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/dezalgo/-/dezalgo-1.0.4.tgz"
  integrity sha512-rXSP0bf+5n0Qonsb+SVVfNfIsimO4HEtmnIpPHY8Q1UCzKlQrDMfdobr8nJOOsRgWCyMRqeSBQzmWUMq7zvVig==
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff-sequences@^28.1.1:
  version "28.1.1"
  resolved "https://registry.npmjs.org/diff-sequences/-/diff-sequences-28.1.1.tgz"
  integrity sha512-FU0iFaH/E23a+a718l8Qa/19bF9p06kgE0KipMOMadwa3SjnaElKzPaUC0vnibs6/B/9ni97s61mcejk8W1fQw==

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz"
  integrity sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-walk@^0.1.0:
  version "0.1.2"
  resolved "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz"
  integrity sha512-6QvTW9mrGeIegrFXdtQi9pk7O/nSK6lSdXW2eqUspN5LWD7UTji2Fqw5V2YLjBpHEoU9Xl/eUWNpDeZvoyOv2w==

dotenv-expand@10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-10.0.0.tgz"
  integrity sha512-GopVGCpVS1UKH75VKHGuQFqS1Gusej0z4FyQkPdwjil2gNIv+LNsqBlboOzpJFZKVT95GkCyWJbBSdFEFUWI2A==

dotenv@16.1.4:
  version "16.1.4"
  resolved "https://registry.npmjs.org/dotenv/-/dotenv-16.1.4.tgz"
  integrity sha512-m55RtE8AsPeJBpOIFKihEmqUcoVncQIwo7x9U8ZwLEZw9ZpXboz2c+rvog+jUaJvVrZ5kBOeYQBX5+8Aa/OZQw==

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

ejs@3.1.7:
  version "3.1.7"
  resolved "https://registry.npmjs.org/ejs/-/ejs-3.1.7.tgz"
  integrity sha512-BIar7R6abbUxDA3bfXrO4DSgwo8I+fB5/1zgujl3HLLjwd6+9iOnrT+t3grn2qbk9vOgBubXOFwX2m9axoFaGw==
  dependencies:
    jake "^10.8.5"

elastic-apm-node@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/elastic-apm-node/-/elastic-apm-node-4.3.0.tgz"
  integrity sha512-LLB+RH12cFIdKZxxW6oOt3aL+8oEYNaHILol31dFb2SFdCKCWceMfUSzESTuPiImdyRTi5A+2Iw+46Fgt4caPQ==
  dependencies:
    "@elastic/ecs-pino-format" "^1.5.0"
    "@opentelemetry/api" "^1.4.1"
    "@opentelemetry/core" "^1.11.0"
    "@opentelemetry/sdk-metrics" "^1.12.0"
    after-all-results "^2.0.0"
    agentkeepalive "^4.2.1"
    async-value-promise "^1.1.1"
    basic-auth "^2.0.1"
    breadth-filter "^2.0.0"
    cookie "^0.6.0"
    core-util-is "^1.0.2"
    end-of-stream "^1.4.4"
    error-callsites "^2.0.4"
    error-stack-parser "^2.0.6"
    escape-string-regexp "^4.0.0"
    fast-safe-stringify "^2.0.7"
    fast-stream-to-buffer "^1.0.0"
    http-headers "^3.0.2"
    import-in-the-middle "1.5.0"
    json-bigint "^1.0.0"
    lru-cache "^10.0.1"
    measured-reporting "^1.51.1"
    module-details-from-path "^1.0.3"
    monitor-event-loop-delay "^1.0.0"
    object-filter-sequence "^1.0.0"
    object-identity-map "^1.0.2"
    original-url "^1.2.3"
    pino "^8.15.0"
    readable-stream "^3.6.2"
    relative-microtime "^2.0.0"
    require-in-the-middle "^7.1.1"
    semver "^7.5.4"
    shallow-clone-shim "^2.0.0"
    source-map "^0.8.0-beta.0"
    sql-summary "^1.0.1"
    stream-chopper "^3.0.1"
    unicode-byte-truncate "^1.0.0"

electron-to-chromium@^1.4.601:
  version "1.4.615"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.615.tgz"
  integrity sha512-/bKPPcgZVUziECqDc+0HkT87+0zhaWSZHNXqF8FLd2lQcptpmUFwoCSWjCdOng9Gdq+afKArPdEg/0ZW461Eng==

emittery@^0.10.2:
  version "0.10.2"
  resolved "https://registry.npmjs.org/emittery/-/emittery-0.10.2.tgz"
  integrity sha512-aITqOwnLanpHLNXZJENbOgjUBeHocD+xsSJmNrjovKBW5HbSpW3d1pEls7GFQPUWXiwG9+0P4GtHfEqC/4M0Iw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

end-of-stream@^1.1.0, end-of-stream@^1.4.1, end-of-stream@^1.4.4:
  version "1.4.4"
  resolved "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

enhanced-resolve@^5.0.0, enhanced-resolve@^5.14.0, enhanced-resolve@^5.7.0:
  version "5.17.1"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz"
  integrity sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

env-cmd@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/env-cmd/-/env-cmd-10.1.0.tgz"
  integrity sha512-mMdWTT9XKN7yNth/6N6g2GuKuJTsKMDHlQFUDacb/heQRRWOTIZ42t1rMHnQu4jYxU1ajdTeJM+9eEETlqToMA==
  dependencies:
    commander "^4.0.0"
    cross-spawn "^7.0.0"

error-callsites@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmjs.org/error-callsites/-/error-callsites-2.0.4.tgz"
  integrity sha512-V877Ch4FC4FN178fDK1fsrHN4I1YQIBdtjKrHh3BUHMnh3SMvwUVrqkaOgDpUuevgSNna0RBq6Ox9SGlxYrigA==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
  dependencies:
    stackframe "^1.3.4"

es-abstract@^1.22.1:
  version "1.22.3"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.22.3.tgz"
  integrity sha512-eiiY8HQeYfYH2Con2berK+To6GrK2RxbPawDkGq4UiCQQfZHb6wX9qQqkbpPqaxQFcl8d9QzZqo0tGE0VcrdwA==
  dependencies:
    array-buffer-byte-length "^1.0.0"
    arraybuffer.prototype.slice "^1.0.2"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.5"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.2"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.12"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.5.1"
    safe-array-concat "^1.0.1"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.8"
    string.prototype.trimend "^1.0.7"
    string.prototype.trimstart "^1.0.7"
    typed-array-buffer "^1.0.0"
    typed-array-byte-length "^1.0.0"
    typed-array-byte-offset "^1.0.0"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.13"

es-module-lexer@^1.2.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.4.1.tgz"
  integrity sha512-cXLGjP0c4T3flZJKQSuziYoq7MlT+rnvfZjfp7h+I7K9BNX54kP9nyWvdbwjQ4u1iWbOL4u96fgeZLToQlZC7w==

es-set-tostringtag@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.2.tgz"
  integrity sha512-BuDyupZt65P9D2D2vA/zqcI3G5xRsklm5N3xCwuiy+/vKy8i0ifdsQP1sLgO4tZDSCaQUSnmC48khknGMV3D2Q==
  dependencies:
    get-intrinsic "^1.2.2"
    has-tostringtag "^1.0.0"
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz"
  integrity sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz"
  integrity sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-prettier@^8.3.0:
  version "8.10.0"
  resolved "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz"
  integrity sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg==

eslint-plugin-prettier@^4.0.0:
  version "4.2.1"
  resolved "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-4.2.1.tgz"
  integrity sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@^8.0.1:
  version "8.56.0"
  resolved "https://registry.npmjs.org/eslint/-/eslint-8.56.0.tgz"
  integrity sha512-Go19xM6T9puCOWntie1/P997aXxFsOi37JIHRWI514Hc6ZnaHGKY9xFhrU65RT6CcBEzZoGG1e6Nq+DT04ZtZQ==
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.56.0"
    "@humanwhocodes/config-array" "^0.11.13"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.4.2:
  version "1.5.0"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

event-target-shim@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz"
  integrity sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==

eventemitter2@6.4.9:
  version "6.4.9"
  resolved "https://registry.npmjs.org/eventemitter2/-/eventemitter2-6.4.9.tgz"
  integrity sha512-JEPTiaOt9f04oa6NOkc4aH+nVp5I3wEjpHbIPqfgCdD5v5bUzy7xQqwcVO2aDQgOWhI28da57HksMrzK9HlRxg==

events@^3.2.0, events@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^4.0.2:
  version "4.1.0"
  resolved "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz"
  integrity sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz"
  integrity sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==

expect@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/expect/-/expect-28.1.3.tgz"
  integrity sha512-eEh0xn8HlsuOBxFgIss+2mX85VAS4Qy3OSkjV7rlBWljtA4oWH37glVGyOZSZvErDT/yBywZdPGwCXuTvSG85g==
  dependencies:
    "@jest/expect-utils" "^28.1.3"
    jest-get-type "^28.0.2"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"

express-basic-auth@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/express-basic-auth/-/express-basic-auth-1.2.1.tgz"
  integrity sha512-L6YQ1wQ/mNjVLAmK3AG1RK6VkokA1BIY6wmiH304Xtt/cLTps40EusZsU1Uop+v9lTDPxdtzbFmdXfFO3KEnwA==
  dependencies:
    basic-auth "^2.0.1"

express@4.17.3:
  version "4.17.3"
  resolved "https://registry.npmjs.org/express/-/express-4.17.3.tgz"
  integrity sha512-yuSQpz5I+Ch7gFrPCk4/c+dIBKlQUxtgwqzph132bsT6qhuzss6I8cLJQz7B3rFblzd6wtcI0ZbGltH/C4LjUg==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.19.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.4.2"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.9.7"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.17.2"
    serve-static "1.14.2"
    setprototypeof "1.2.0"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

express@4.18.2, express@^4.18.2:
  version "4.18.2"
  resolved "https://registry.npmjs.org/express/-/express-4.18.2.tgz"
  integrity sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.1"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.5.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz"
  integrity sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==

fast-glob@^3.2.9:
  version "3.3.2"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@2.x, fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fast-redact@^3.1.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/fast-redact/-/fast-redact-3.3.0.tgz"
  integrity sha512-6T5V1QK1u4oF+ATxs1lWUmlEk6P2T9HqJG3e2DnHOdVgZy2rFJBoEnrIedcTXlkAHU/zKC+7KETJ+KGGKwxgMQ==

fast-safe-stringify@2.1.1, fast-safe-stringify@^2.0.7, fast-safe-stringify@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/fast-safe-stringify/-/fast-safe-stringify-2.1.1.tgz"
  integrity sha512-W+KJc2dmILlPplD/H4K9l9LcAHAfPtP6BY84uVLXQ6Evcz9Lcg33Y2z1IVblT6xdY54PXYVHEv+0Wpq8Io6zkA==

fast-stream-to-buffer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fast-stream-to-buffer/-/fast-stream-to-buffer-1.0.0.tgz"
  integrity sha512-bI/544WUQlD2iXBibQbOMSmG07Hay7YrpXlKaeGTPT7H7pC0eitt3usak5vUwEvCGK/O7rUAM3iyQValGU22TQ==
  dependencies:
    end-of-stream "^1.4.1"

fast-xml-parser@4.2.5:
  version "4.2.5"
  resolved "https://registry.npmjs.org/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz"
  integrity sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==
  dependencies:
    strnum "^1.0.5"

fastq@^1.6.0:
  version "1.16.0"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.16.0.tgz"
  integrity sha512-ifCoaXsDrsdkWTtiNJX5uzHDsrck5TzfKKDcuFFTIrrc/BS076qgEIfoIy1VeZqViznfKiysPYTh/QeHtnIsYA==
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz"
  integrity sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==
  dependencies:
    bser "2.1.1"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz"
  integrity sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

filelist@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz"
  integrity sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz"
  integrity sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  integrity sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.2.9"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.2.9.tgz"
  integrity sha512-36yxDn5H7OFZQla0/jFJmbIKTdZAQHngCedGxiMmpNfEZM0sdEeT+WczLQrjK6D7o2aiyLYDnkw0R3JK0Qv1RQ==

follow-redirects@^1.14.9, follow-redirects@^1.15.0, follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

fork-ts-checker-webpack-plugin@8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-8.0.0.tgz"
  integrity sha512-mX3qW3idpueT2klaQXBzrIM/pHw+T0B/V9KHEvNrqijTq9NFnMZU6oreVxDYcf33P8a5cW+67PjodNHthGnNVg==
  dependencies:
    "@babel/code-frame" "^7.16.7"
    chalk "^4.1.2"
    chokidar "^3.5.3"
    cosmiconfig "^7.0.1"
    deepmerge "^4.2.2"
    fs-extra "^10.0.0"
    memfs "^3.4.1"
    minimatch "^3.0.4"
    node-abort-controller "^3.0.1"
    schema-utils "^3.1.1"
    semver "^7.3.5"
    tapable "^2.2.1"

form-data@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.0.tgz"
  integrity sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

formidable@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/formidable/-/formidable-2.1.2.tgz"
  integrity sha512-CM3GuJ57US06mlpQ47YcunuUZ9jpm8Vx+P2CGt2j7HpgkKZO/DJYQ0Bobim8G6PFQmK5lOqOOdUXboU+h73A4g==
  dependencies:
    dezalgo "^1.0.4"
    hexoid "^1.0.0"
    once "^1.4.0"
    qs "^6.11.0"

forwarded-parse@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/forwarded-parse/-/forwarded-parse-2.1.2.tgz"
  integrity sha512-alTFZZQDKMporBH77856pXgzhEzaUVmLCDk+egLgIgHst3Tpndzz8MnKe+GzRJRfvVdn69HhpW7cmXzvtLvJAw==

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-extra@^10.0.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz"
  integrity sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.5.tgz"
  integrity sha512-8uMbBjrhzW76TYgEV27Y5E//W2f/lTFmx78P2w19FZSxarhI/798APGQyuGCwmkNxgwGRhrLfvWyLBvNtuOmew==

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

fsevents@^2.3.2, fsevents@~2.3.2:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
  integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

g@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/g/-/g-2.0.1.tgz"
  integrity sha512-Fi6Ng5fZ/ANLQ15H11hCe+09sgUoNvDEBevVgx3KoYOhsH5iLNPn54hx0jPZ+3oSWr+xajnp2Qau9VmPsc7hTA==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.2.tgz"
  integrity sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA==
  dependencies:
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz"
  integrity sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==

get-port@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/get-port/-/get-port-5.1.1.tgz"
  integrity sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz"
  integrity sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  integrity sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^7.0.0, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^9.2.0:
  version "9.3.5"
  resolved "https://registry.npmjs.org/glob/-/glob-9.3.5.tgz"
  integrity sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==
  dependencies:
    fs.realpath "^1.0.0"
    minimatch "^8.0.2"
    minipass "^4.2.4"
    path-scurry "^1.6.1"

global@^4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/global/-/global-4.4.0.tgz"
  integrity sha512-wv/LAoHdRE3BeTGz53FAamhGlPLhlssK45usmGFThIi4XqnBmjKQ16u+RNbP7WvigRZDxUsM0J3gcQ5yicaL0w==
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.19.0:
  version "13.24.0"
  resolved "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.3.tgz"
  integrity sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz"
  integrity sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
  integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.1.tgz"
  integrity sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg==
  dependencies:
    get-intrinsic "^1.2.2"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz"
  integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
  dependencies:
    has-symbols "^1.0.2"

hasown@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz"
  integrity sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==
  dependencies:
    function-bind "^1.1.2"

hexoid@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/hexoid/-/hexoid-1.0.0.tgz"
  integrity sha512-QFLV0taWQOZtvIRIAdBChesmogZrtuXvVWsFHZTk2SU+anspqZ2vMnoLg7IE1+Uk16N19APic1BuF8bC8c2m5g==

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==

http-errors@1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-1.8.1.tgz"
  integrity sha512-Kpk9Sm7NmI+RHhnj6OIWDI1d6fIoFAtFt9RLaTMRlg/8w49juAStsrBgp0Dp4OdxdVbRIeKhtCUvoi/RuAhO4g==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.1"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-headers@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/http-headers/-/http-headers-3.0.2.tgz"
  integrity sha512-87E1I+2Wg4dxxz4rcxElo3dxO/w1ZtgL1yA0Sb6vH3qU16vRKq1NjWQv9SCY3ly2OQROcoxHZOUpmelS+k6wOw==
  dependencies:
    next-line "^1.1.0"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz"
  integrity sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/humanize-ms/-/humanize-ms-1.2.1.tgz"
  integrity sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==
  dependencies:
    ms "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13, ieee754@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.3.0.tgz"
  integrity sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg==

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-in-the-middle@1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/import-in-the-middle/-/import-in-the-middle-1.5.0.tgz"
  integrity sha512-H2hqR0jImhqe9+1k8pYewDKWJHnDeRsWZk5aSztv6MIWD5glmbEOqy1JZrMUC6SJiO1M4A+nVvUUYtWzP5wPYg==
  dependencies:
    acorn "^8.8.2"
    acorn-import-assertions "^1.9.0"
    cjs-module-lexer "^1.2.2"
    module-details-from-path "^1.0.3"

import-local@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/import-local/-/import-local-3.1.0.tgz"
  integrity sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inquirer@8.2.4:
  version "8.2.4"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-8.2.4.tgz"
  integrity sha512-nn4F01dxU8VeKfq192IjLsxu0/OmMZ4Lg3xKAns148rCaXP6ntAoEkVYZThWjwON8AlzdZZi6oqnhNbxUG9hVg==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^7.0.0"

inquirer@8.2.5:
  version "8.2.5"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-8.2.5.tgz"
  integrity sha512-QAgPDQMEgrDssk1XiwwHoOGYF9BAbUcc1+j+FhEvaOt8/cKRqyLn0U5qA6F74fGhTMGxf92pOvPBeh29jQJDTQ==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^7.0.0"

inquirer@8.2.6:
  version "8.2.6"
  resolved "https://registry.npmjs.org/inquirer/-/inquirer-8.2.6.tgz"
  integrity sha512-M1WuAmb7pn9zdFRtQYk26ZBoY043Sse0wVDdk4Bppr+JOXyQYybdtvK+l9wUibhtjdjvtoiNy8tk+EgsYIUqKg==
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

internal-slot@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.6.tgz"
  integrity sha512-Xj6dv+PsbtwyPpEflsejS+oIZxmMlV44zAhG479uYu89MsjcYOhCFnNyKrkJrihbsiasQyY0afoCl/9BLR65bg==
  dependencies:
    get-intrinsic "^1.2.2"
    hasown "^2.0.0"
    side-channel "^1.0.4"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz"
  integrity sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==

ioredis@*, ioredis@^5.3.2:
  version "5.4.1"
  resolved "https://registry.npmjs.org/ioredis/-/ioredis-5.4.1.tgz"
  integrity sha512-2YZsvl7jopIa1gaePkeMtd9rAcSjOOjPtpcLlOeusyO+XH2SK5ZcT+UCrElPP+WVIInh2TzeI4XW9ENaSLVVHA==
  dependencies:
    "@ioredis/commands" "^1.1.1"
    cluster-key-slot "^1.1.0"
    debug "^4.3.4"
    denque "^2.1.0"
    lodash.defaults "^4.2.0"
    lodash.isarguments "^3.1.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.1.0"

ip@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ip/-/ip-2.0.0.tgz"
  integrity sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz"
  integrity sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@~1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/is-buffer/-/is-buffer-1.1.6.tgz"
  integrity sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.13.0:
  version "2.13.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz"
  integrity sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==
  dependencies:
    hasown "^2.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finite@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/is-finite/-/is-finite-1.1.0.tgz"
  integrity sha512-cdyMtqX/BOqqNBBiKlIVkytNHm49MtMlYyn1zxzvJKWmFMlGzm+ry5BBfYyeY9YmNKbRSo/o7OX9w9ale0wg3w==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz"
  integrity sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-integer@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-integer/-/is-integer-1.0.7.tgz"
  integrity sha512-RPQc/s9yBHSvpi+hs9dYiJ2cuFeU6x3TyyIp8O2H6SKEltIvJOzRj9ToyvcStDvPR/pS4rxgr1oBFajQjZ2Szg==
  dependencies:
    is-finite "^1.0.0"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
  integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz"
  integrity sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  integrity sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==
  dependencies:
    call-bind "^1.0.2"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.12, is-typed-array@^1.1.9:
  version "1.1.12"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz"
  integrity sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==
  dependencies:
    which-typed-array "^1.1.11"

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz"
  integrity sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==

istanbul-lib-instrument@^5.0.4, istanbul-lib-instrument@^5.1.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz"
  integrity sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz"
  integrity sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.1.3:
  version "3.1.6"
  resolved "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.6.tgz"
  integrity sha512-TLgnMkKg3iTDsQ9PbPTdpfAK2DzjF9mqUG7RMgcQl8oFjad8ob4laGxv5XV5U9MAfx8D6tSJiUyuAwzLicaxlg==
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterare@1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/iterare/-/iterare-1.2.1.tgz"
  integrity sha512-RKYVTCjAnRthyJes037NX/IiqeidgN1xc3j1RjFfECFp28A1GVwK9nA+i0rJPaHqSZwygLzRnFlzUuHFoWWy+Q==

jake@^10.8.5:
  version "10.8.7"
  resolved "https://registry.npmjs.org/jake/-/jake-10.8.7.tgz"
  integrity sha512-ZDi3aP+fG/LchyBzUM804VjddnwfSfsdeYkwt8NcbKRvo4rFkjhs456iLFn3k2ZUWvNe4i48WACDbza8fhq2+w==
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-changed-files@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-28.1.3.tgz"
  integrity sha512-esaOfUWJXk2nfZt9SPyC8gA1kNfdKLkQWyzsMlqq8msYSlNKfmZxfRgZn4Cd4MGVUF+7v6dBs0d5TOAKa7iIiA==
  dependencies:
    execa "^5.0.0"
    p-limit "^3.1.0"

jest-circus@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-circus/-/jest-circus-28.1.3.tgz"
  integrity sha512-cZ+eS5zc79MBwt+IhQhiEp0OeBddpc1n8MBo1nMB8A7oPMKEO+Sre+wHaLJexQUj9Ya/8NOBY0RESUgYjB6fow==
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/expect" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    is-generator-fn "^2.0.0"
    jest-each "^28.1.3"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-runtime "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    p-limit "^3.1.0"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^28.1.2:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-cli/-/jest-cli-28.1.3.tgz"
  integrity sha512-roY3kvrv57Azn1yPgdTebPAXvdR2xfezaKKYzVxZ6It/5NCxzJym6tUI5P1zkdWhfUYkxEI9uZWcQdaFLo8mJQ==
  dependencies:
    "@jest/core" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    import-local "^3.0.2"
    jest-config "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    prompts "^2.0.1"
    yargs "^17.3.1"

jest-config@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-config/-/jest-config-28.1.3.tgz"
  integrity sha512-MG3INjByJ0J4AsNBm7T3hsuxKQqFIiRo/AUqb1q9LRKI5UU6Aar9JHbr9Ivn1TVwfUD9KirRoM/T6u8XlcQPHQ==
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^28.1.3"
    "@jest/types" "^28.1.3"
    babel-jest "^28.1.3"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^28.1.3"
    jest-environment-node "^28.1.3"
    jest-get-type "^28.0.2"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-runner "^28.1.3"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-diff/-/jest-diff-28.1.3.tgz"
  integrity sha512-8RqP1B/OXzjjTWkqMX67iqgwBVJRgCyKD3L9nq+6ZqJMdvjE8RgHktqZ6jNrkdMT+dJuYNI3rhQpxaz7drJHfw==
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^28.1.1"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-docblock@^28.1.1:
  version "28.1.1"
  resolved "https://registry.npmjs.org/jest-docblock/-/jest-docblock-28.1.1.tgz"
  integrity sha512-3wayBVNiOYx0cwAbl9rwm5kKFP8yHH3d/fkEaL02NPTkDojPtheGB7HZSFY4wzX+DxyrvhXz0KSCVksmCknCuA==
  dependencies:
    detect-newline "^3.0.0"

jest-each@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-each/-/jest-each-28.1.3.tgz"
  integrity sha512-arT1z4sg2yABU5uogObVPvSlSMQlDA48owx07BDPAiasW0yYpYHYOo4HHLz9q0BVzDVU4hILFjzJw0So9aCL/g==
  dependencies:
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    jest-get-type "^28.0.2"
    jest-util "^28.1.3"
    pretty-format "^28.1.3"

jest-environment-node@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-28.1.3.tgz"
  integrity sha512-ugP6XOhEpjAEhGYvp5Xj989ns5cB1K6ZdjBYuS30umT4CQEETaxSiPcZ/E1kFktX4GkrcM4qu07IIlDYX1gp+A==
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/fake-timers" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    jest-mock "^28.1.3"
    jest-util "^28.1.3"

jest-get-type@^28.0.2:
  version "28.0.2"
  resolved "https://registry.npmjs.org/jest-get-type/-/jest-get-type-28.0.2.tgz"
  integrity sha512-ioj2w9/DxSYHfOm5lJKCdcAmPJzQXmbM/Url3rhlghrPvT3tt+7a/+oXc9azkKmLvoiXjtV83bEWqi+vs5nlPA==

jest-haste-map@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-28.1.3.tgz"
  integrity sha512-3S+RQWDXccXDKSWnkHa/dPwt+2qwA8CJzR61w3FoYCvoo3Pn8tvGcysmMF0Bj0EX5RYvAI2EIvC57OmotfdtKA==
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^28.0.2"
    jest-util "^28.1.3"
    jest-worker "^28.1.3"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.1.3.tgz"
  integrity sha512-WFVJhnQsiKtDEo5lG2mM0v40QWnBM+zMdHHyJs8AWZ7J0QZJS59MsyKeJHWhpBZBH32S48FOVvGyOFT1h0DlqA==
  dependencies:
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-matcher-utils@^28.0.0, jest-matcher-utils@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-28.1.3.tgz"
  integrity sha512-kQeJ7qHemKfbzKoGjHHrRKH6atgxMk8Enkk2iPQ3XwO6oE/KYD8lMYOziCkeSB9G4adPM4nR1DE8Tf5JeWH6Bw==
  dependencies:
    chalk "^4.0.0"
    jest-diff "^28.1.3"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-message-util@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-message-util/-/jest-message-util-28.1.3.tgz"
  integrity sha512-PFdn9Iewbt575zKPf1286Ht9EPoJmYT7P0kY+RibeYZ2XtOr53pDLEFoTWXbd1h4JiGiWpTBC84fc8xMXQMb7g==
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^28.1.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^28.1.3"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-mock/-/jest-mock-28.1.3.tgz"
  integrity sha512-o3J2jr6dMMWYVH4Lh/NKmDXdosrsJgi4AviS8oXLujcjpCMBb1FMsblDnOXKZKfSiHLxYub1eS0IHuRXsio9eA==
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz"
  integrity sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==

jest-regex-util@^28.0.2:
  version "28.0.2"
  resolved "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.2.tgz"
  integrity sha512-4s0IgyNIy0y9FK+cjoVYoxamT7Zeo7MhzqRGx7YDYmaQn1wucY9rotiGkBzzcMXTtjrCAP/f7f+E0F7+fxPNdw==

jest-resolve-dependencies@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-28.1.3.tgz"
  integrity sha512-qa0QO2Q0XzQoNPouMbCc7Bvtsem8eQgVPNkwn9LnS+R2n8DaVDPL/U1gngC0LTl1RYXJU0uJa2BMC2DbTfFrHA==
  dependencies:
    jest-regex-util "^28.0.2"
    jest-snapshot "^28.1.3"

jest-resolve@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-resolve/-/jest-resolve-28.1.3.tgz"
  integrity sha512-Z1W3tTjE6QaNI90qo/BJpfnvpxtaFTFw5CDgwpyE/Kz8U/06N1Hjf4ia9quUhCh39qIGWF1ZuxFiBiJQwSEYKQ==
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-pnp-resolver "^1.2.2"
    jest-util "^28.1.3"
    jest-validate "^28.1.3"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-runner/-/jest-runner-28.1.3.tgz"
  integrity sha512-GkMw4D/0USd62OVO0oEgjn23TM+YJa2U2Wu5zz9xsQB1MxWKDOlrnykPxnMsN0tnJllfLPinHTka61u0QhaxBA==
  dependencies:
    "@jest/console" "^28.1.3"
    "@jest/environment" "^28.1.3"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.10.2"
    graceful-fs "^4.2.9"
    jest-docblock "^28.1.1"
    jest-environment-node "^28.1.3"
    jest-haste-map "^28.1.3"
    jest-leak-detector "^28.1.3"
    jest-message-util "^28.1.3"
    jest-resolve "^28.1.3"
    jest-runtime "^28.1.3"
    jest-util "^28.1.3"
    jest-watcher "^28.1.3"
    jest-worker "^28.1.3"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-runtime/-/jest-runtime-28.1.3.tgz"
  integrity sha512-NU+881ScBQQLc1JHG5eJGU7Ui3kLKrmwCPPtYsJtBykixrM2OhVQlpMmFWJjMyDfdkGgBMNjXCGB/ebzsgNGQw==
  dependencies:
    "@jest/environment" "^28.1.3"
    "@jest/fake-timers" "^28.1.3"
    "@jest/globals" "^28.1.3"
    "@jest/source-map" "^28.1.2"
    "@jest/test-result" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    execa "^5.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^28.1.3"
    jest-message-util "^28.1.3"
    jest-mock "^28.1.3"
    jest-regex-util "^28.0.2"
    jest-resolve "^28.1.3"
    jest-snapshot "^28.1.3"
    jest-util "^28.1.3"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-28.1.3.tgz"
  integrity sha512-4lzMgtiNlc3DU/8lZfmqxN3AYD6GGLbl+72rdBpXvcV+whX7mDrREzkPdp2RnmfIiWBg1YbuFSkXduF2JcafJg==
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^28.1.3"
    "@jest/transform" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/babel__traverse" "^7.0.6"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^28.1.3"
    graceful-fs "^4.2.9"
    jest-diff "^28.1.3"
    jest-get-type "^28.0.2"
    jest-haste-map "^28.1.3"
    jest-matcher-utils "^28.1.3"
    jest-message-util "^28.1.3"
    jest-util "^28.1.3"
    natural-compare "^1.4.0"
    pretty-format "^28.1.3"
    semver "^7.3.5"

jest-util@^28.0.0, jest-util@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-util/-/jest-util-28.1.3.tgz"
  integrity sha512-XdqfpHwpcSRko/C35uLYFM2emRAltIIKZiJ9eAmhjsj0CqZMa0p1ib0R5fWIqGhn1a103DebTbpqIaP1qCQ6tQ==
  dependencies:
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-validate/-/jest-validate-28.1.3.tgz"
  integrity sha512-SZbOGBWEsaTxBGCOpsRWlXlvNkvTkY0XxRfh7zYmvd8uL5Qzyg0CHAXiXKROflh801quA6+/DsT4ODDthOC/OA==
  dependencies:
    "@jest/types" "^28.1.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^28.0.2"
    leven "^3.1.0"
    pretty-format "^28.1.3"

jest-watcher@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.3.tgz"
  integrity sha512-t4qcqj9hze+jviFPUN3YAtAEeFnr/azITXQEMARf5cMwKY2SMBRnCQTXLixTl20OR6mLh9KLMrgVJgJISym+1g==
  dependencies:
    "@jest/test-result" "^28.1.3"
    "@jest/types" "^28.1.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.10.2"
    jest-util "^28.1.3"
    string-length "^4.0.1"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz"
  integrity sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@28.1.2:
  version "28.1.2"
  resolved "https://registry.npmjs.org/jest/-/jest-28.1.2.tgz"
  integrity sha512-Tuf05DwLeCh2cfWCQbcz9UxldoDyiR1E9Igaei5khjonKncYdc6LDfynKCEWozK0oLE3GD+xKAo2u8x/0s6GOg==
  dependencies:
    "@jest/core" "^28.1.2"
    "@jest/types" "^28.1.1"
    import-local "^3.0.2"
    jest-cli "^28.1.2"

joi@^17.6.0:
  version "17.11.0"
  resolved "https://registry.npmjs.org/joi/-/joi-17.11.0.tgz"
  integrity sha512-NgB+lZLNoqISVy1rZocE9PZI36bL/77ie924Ri43yEvi9GUUMPeyVIr8KdFTMUlby1p0PBYMk9spIxEUQYqrJQ==
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.3"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@4.1.0, js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

json-bigint@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-bigint/-/json-bigint-1.0.0.tgz"
  integrity sha512-SiPv/8VpZuWbvLSMtTDU8hEfrZWg/mH/nV/b4o0CYbSxu1UIQPLdwKOCIyLQX+VIPO5vrLX3i8qtqFyhdPSUSQ==
  dependencies:
    bignumber.js "^9.0.0"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json2csv@^6.0.0-alpha.2:
  version "6.0.0-alpha.2"
  resolved "https://registry.npmjs.org/json2csv/-/json2csv-6.0.0-alpha.2.tgz"
  integrity sha512-nJ3oP6QxN8z69IT1HmrJdfVxhU1kLTBVgMfRnNZc37YEY+jZ4nU27rBGxT4vaqM/KUCavLRhntmTuBFqZLBUcA==
  dependencies:
    "@streamparser/json" "^0.0.6"
    commander "^6.2.0"
    lodash.get "^4.4.2"

json5@^2.2.1, json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonc-parser@3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.2.0.tgz"
  integrity sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonwebtoken@^9.0.2:
  version "9.0.2"
  resolved "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  integrity sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz"
  integrity sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  integrity sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

kafkajs@^2.1.0:
  version "2.2.4"
  resolved "https://registry.npmjs.org/kafkajs/-/kafkajs-2.2.4.tgz"
  integrity sha512-j/YeapB1vfPT2iOIUn/vxdyKEuhuY2PxMBvf5JWux6iSaukAccrMtXEY/Lb7OvavDhOWME589bpLrEdnVHjfjA==

kareem@2.5.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/kareem/-/kareem-2.5.1.tgz"
  integrity sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA==

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz"
  integrity sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz"
  integrity sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

libphonenumber-js@^1.10.14, libphonenumber-js@^1.9.43:
  version "1.10.52"
  resolved "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.10.52.tgz"
  integrity sha512-6vCuCHgem+OW1/VCAKgkasfegItCea8zIT7s9/CG/QxdCMIM7GfzbEBG5d7lGO3rzipjt5woOQL3DiHa8Fy78Q==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  integrity sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==
  dependencies:
    p-locate "^5.0.0"

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz"
  integrity sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==

lodash.defaults@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz"
  integrity sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz"
  integrity sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==

lodash.isarguments@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/lodash.isarguments/-/lodash.isarguments-3.1.0.tgz"
  integrity sha512-chi4NHZlZqZD18a0imDHnZPrDeBbTtVN7GXMwuGdRH9qotxAjYs3aVLKc7zNOG9eddR5Ksd8rvFEBc9SsggPpg==

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==

lodash.memoize@4.x:
  version "4.1.2"
  resolved "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz"
  integrity sha512-HDWXG8isMntAyRF5vZ7xKuEvOhT4AhlRt/3czTSjvGUxjYCBVRQY48ViDHyfYz9VIoBkW4TMGQNapx+l3RUwdA==

lodash@4.17.21, lodash@^4.17.11, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

lru-cache@^10.0.1, lru-cache@^10.1.0, "lru-cache@^9.1.1 || ^10.0.0":
  version "10.1.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.1.0.tgz"
  integrity sha512-/1clY/ui8CzjKFyjdvwPWJUYKiFVXG2I2cY0ssG7h4+hwk+XOIX7ZSG9Q7TW8TW3Kp3BUSqgFWBLgL4PJ+Blag==

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

lru-cache@^7.10.1:
  version "7.18.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-7.18.3.tgz"
  integrity sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==

luxon@^3.2.1, luxon@~3.4.0:
  version "3.4.4"
  resolved "https://registry.npmjs.org/luxon/-/luxon-3.4.4.tgz"
  integrity sha512-zobTr7akeGHnv7eBOXcRgMeCP6+uyYsczwmeRCauvpvaAltgNyTbLH/+VaEAPUeWBT+1GuNmz4wC/6jtQzbbVA==

macos-release@^2.5.0:
  version "2.5.1"
  resolved "https://registry.npmjs.org/macos-release/-/macos-release-2.5.1.tgz"
  integrity sha512-DXqXhEM7gW59OjZO8NIjBCz9AQ1BEMrfiOAl4AYByHCtVHRF4KoGNO8mqQeM8lRCtQe/UnJ4imO/d2HdkKsd+A==

magic-string@0.30.0:
  version "0.30.0"
  resolved "https://registry.npmjs.org/magic-string/-/magic-string-0.30.0.tgz"
  integrity sha512-LA+31JYDJLs82r2ScLrlz1GjSgu66ZV518eyWT+S8VhyQn/JL0u9MeBOvQMGYiPk1DBiSN9DDMOcXvigJZaViQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz"
  integrity sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==
  dependencies:
    semver "^7.5.3"

make-error@1.x, make-error@^1.1.1:
  version "1.3.6"
  resolved "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz"
  integrity sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz"
  integrity sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==
  dependencies:
    tmpl "1.0.5"

mapcap@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/mapcap/-/mapcap-1.0.0.tgz"
  integrity sha512-KcNlZSlFPx+r1jYZmxEbTVymG+dIctf10WmWkuhrhrblM+KMoF77HelwihL5cxYlORye79KoR4IlOOk99lUJ0g==

md5@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/md5/-/md5-2.3.0.tgz"
  integrity sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==
  dependencies:
    charenc "0.0.2"
    crypt "0.0.2"
    is-buffer "~1.1.6"

measured-core@^1.51.1:
  version "1.51.1"
  resolved "https://registry.npmjs.org/measured-core/-/measured-core-1.51.1.tgz"
  integrity sha512-DZQP9SEwdqqYRvT2slMK81D/7xwdxXosZZBtLVfPSo6y5P672FBTbzHVdN4IQyUkUpcVOR9pIvtUy5Ryl7NKyg==
  dependencies:
    binary-search "^1.3.3"
    optional-js "^2.0.0"

measured-reporting@^1.51.1:
  version "1.51.1"
  resolved "https://registry.npmjs.org/measured-reporting/-/measured-reporting-1.51.1.tgz"
  integrity sha512-JCt+2u6XT1I5lG3SuYqywE0e62DJuAzBcfMzWGUhIYtPQV2Vm4HiYt/durqmzsAbZV181CEs+o/jMKWJKkYIWw==
  dependencies:
    console-log-level "^1.4.1"
    mapcap "^1.0.0"
    measured-core "^1.51.1"
    optional-js "^2.0.0"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

memfs@^3.4.1:
  version "3.6.0"
  resolved "https://registry.npmjs.org/memfs/-/memfs-3.6.0.tgz"
  integrity sha512-EGowvkkgbMcIChjMTMkESFDbZeSh8xZ7kNSF0hAiAN4Jh6jgHCRS0Ga/+C8y6Au+oqpezRHCfPsmJ2+DwAgiwQ==
  dependencies:
    fs-monkey "^1.0.4"

memory-pager@^1.0.2:
  version "1.5.0"
  resolved "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz"
  integrity sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg==

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@^1.1.2, methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

micromatch@^4.0.0, micromatch@^4.0.4:
  version "4.0.5"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mime@2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz"
  integrity sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

min-document@^2.19.0:
  version "2.19.0"
  resolved "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz"
  integrity sha512-9Wy1B3m3f66bPPmU5hdA4DR4PB2OfDU/+GS3yAB7IQozE3tqXaVv2zOjgla7MEGSRv95+ILmOuvhLkOK6wJtCQ==
  dependencies:
    dom-walk "^0.1.0"

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz"
  integrity sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^8.0.2:
  version "8.0.4"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-8.0.4.tgz"
  integrity sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^4.2.4:
  version "4.2.8"
  resolved "https://registry.npmjs.org/minipass/-/minipass-4.2.8.tgz"
  integrity sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "7.0.4"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.0.4.tgz"
  integrity sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==

mkdirp@^0.5.4:
  version "0.5.6"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

module-details-from-path@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/module-details-from-path/-/module-details-from-path-1.0.3.tgz"
  integrity sha512-ySViT69/76t8VhE1xXHK6Ch4NcDd26gx0MzKXLO+F7NOtnqH68d9zF94nT8ZWSxXh8ELOERsnJO/sWt1xZYw5A==

moment-timezone@^0.5.46:
  version "0.5.46"
  resolved "https://registry.npmjs.org/moment-timezone/-/moment-timezone-0.5.46.tgz"
  integrity sha512-ZXm9b36esbe7OmdABqIWJuBBiLLwAjrN7CE+7sYdCCx82Nabt1wHDj8TVseS59QIlfFPbOoiBPm6ca9BioG4hw==
  dependencies:
    moment "^2.29.4"

moment@^2.29.4:
  version "2.29.4"
  resolved "https://registry.npmjs.org/moment/-/moment-2.29.4.tgz"
  integrity sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==

mongodb-connection-string-url@^2.6.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz"
  integrity sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ==
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    whatwg-url "^11.0.0"

mongodb@4.17.1:
  version "4.17.1"
  resolved "https://registry.npmjs.org/mongodb/-/mongodb-4.17.1.tgz"
  integrity sha512-MBuyYiPUPRTqfH2dV0ya4dcr2E5N52ocBuZ8Sgg/M030nGF78v855B3Z27mZJnp8PxjnUquEnAtjOsphgMZOlQ==
  dependencies:
    bson "^4.7.2"
    mongodb-connection-string-url "^2.6.0"
    socks "^2.7.1"
  optionalDependencies:
    "@aws-sdk/credential-providers" "^3.186.0"
    "@mongodb-js/saslprep" "^1.1.0"

mongoose@^6.12.3:
  version "6.12.3"
  resolved "https://registry.npmjs.org/mongoose/-/mongoose-6.12.3.tgz"
  integrity sha512-MNJymaaXali7w7rHBxVUoQ3HzHHMk/7I/+yeeoSa4rUzdjZwIWQznBNvVgc0A8ghuJwsuIkb5LyLV6gSjGjWyQ==
  dependencies:
    bson "^4.7.2"
    kareem "2.5.1"
    mongodb "4.17.1"
    mpath "0.9.0"
    mquery "4.0.3"
    ms "2.1.3"
    sift "16.0.1"

monitor-event-loop-delay@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/monitor-event-loop-delay/-/monitor-event-loop-delay-1.0.0.tgz"
  integrity sha512-YRIr1exCIfBDLZle8WHOfSo7Xg3M+phcZfq9Fx1L6Abo+atGp7cge5pM7PjyBn4s1oZI/BRD4EMrzQBbPpVb5Q==

mpath@0.9.0:
  version "0.9.0"
  resolved "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz"
  integrity sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew==

mquery@4.0.3:
  version "4.0.3"
  resolved "https://registry.npmjs.org/mquery/-/mquery-4.0.3.tgz"
  integrity sha512-J5heI+P08I6VJ2Ky3+33IpCdAvlYGTSUjwTPxkAr8i8EoduPMBX2OY/wa3IKZIQl7MU4SbFk8ndgSKyB/cl1zA==
  dependencies:
    debug "4.x"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.2, ms@^2.0.0, ms@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

msgpackr-extract@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/msgpackr-extract/-/msgpackr-extract-3.0.2.tgz"
  integrity sha512-SdzXp4kD/Qf8agZ9+iTu6eql0m3kWm1A2y1hkpTeVNENutaB0BwHlSvAIaMxwntmRUAUjon2V4L8Z/njd0Ct8A==
  dependencies:
    node-gyp-build-optional-packages "5.0.7"
  optionalDependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64" "3.0.2"
    "@msgpackr-extract/msgpackr-extract-darwin-x64" "3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-arm" "3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-arm64" "3.0.2"
    "@msgpackr-extract/msgpackr-extract-linux-x64" "3.0.2"
    "@msgpackr-extract/msgpackr-extract-win32-x64" "3.0.2"

msgpackr@^1.5.2:
  version "1.11.0"
  resolved "https://registry.npmjs.org/msgpackr/-/msgpackr-1.11.0.tgz"
  integrity sha512-I8qXuuALqJe5laEBYoFykChhSXLikZmUhccjGsPuSJ/7uPip2TJ7lwdIQwWSAi0jGZDXv4WOP8Qg65QZRuXxXw==
  optionalDependencies:
    msgpackr-extract "^3.0.2"

multer@1.4.4-lts.1:
  version "1.4.4-lts.1"
  resolved "https://registry.npmjs.org/multer/-/multer-1.4.4-lts.1.tgz"
  integrity sha512-WeSGziVj6+Z2/MwQo3GvqzgR+9Uc+qt8SwHKh3gvNPiISKfsMfG4SvCOFYlxxgkXt7yIV2i1yczehm0EOKIxIg==
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

multer@^1.4.5-lts.1:
  version "1.4.5-lts.1"
  resolved "https://registry.npmjs.org/multer/-/multer-1.4.5-lts.1.tgz"
  integrity sha512-ywPWvcDMeH+z9gQq5qYHCCy+ethsk4goepZ45GLD63fOu0YcNecQxi64nDs3qluZB+murG3/D4dJ7+dGctcCQQ==
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz"
  integrity sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz"
  integrity sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

nest-commander@^3.15.0:
  version "3.15.0"
  resolved "https://registry.npmjs.org/nest-commander/-/nest-commander-3.15.0.tgz"
  integrity sha512-o9VEfFj/w2nm+hQi6fnkxL1GAFZW/KmuGcIE7/B/TX0gwm0QVy8svAF75EQm8wrDjcvWS7Cx/ArnkFn2C+iM2w==
  dependencies:
    "@fig/complete-commander" "^3.0.0"
    "@golevelup/nestjs-discovery" "4.0.1"
    commander "11.1.0"
    cosmiconfig "8.3.6"
    inquirer "8.2.6"

nestjs-pino@^3.5.0:
  version "3.5.0"
  resolved "https://registry.npmjs.org/nestjs-pino/-/nestjs-pino-3.5.0.tgz"
  integrity sha512-IWJ3dzLVjg5istcd3Cz3rVO+gmvabfVAT1YmQgzL1HnC2hkc0H6qA6k6SZ7OIwQfewuRejYfPu3TlkxwRrqxHQ==

next-line@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/next-line/-/next-line-1.1.0.tgz"
  integrity sha512-+I10J3wKNoKddNxn0CNpoZ3eTZuqxjNM3b1GImVx22+ePI+Y15P8g/j3WsbP0fhzzrFzrtjOAoq5NCCucswXOQ==

node-abort-controller@^3.0.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/node-abort-controller/-/node-abort-controller-3.1.1.tgz"
  integrity sha512-AGK2yQKIjRuqnc6VkX2Xj5d+QW8xZ87pa1UK6yA6ouUyuxfHuMP6umE5QK7UmTeOAymo+Zx1Fxiuw9rVx8taHQ==

node-emoji@1.11.0:
  version "1.11.0"
  resolved "https://registry.npmjs.org/node-emoji/-/node-emoji-1.11.0.tgz"
  integrity sha512-wo2DpQkQp7Sjm2A0cq+sN7EHKO6Sl0ctXeBdFZrL9T9+UywORbufTcTZxom8YqpLQt/FqNMUkOpkZrJVYSKD3A==
  dependencies:
    lodash "^4.17.21"

node-fetch@^2.6.1:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-gyp-build-optional-packages@5.0.7:
  version "5.0.7"
  resolved "https://registry.npmjs.org/node-gyp-build-optional-packages/-/node-gyp-build-optional-packages-5.0.7.tgz"
  integrity sha512-YlCCc6Wffkx0kHkmam79GKvDQ6x+QZkMjFGrIMxgFNILFvGSbCp2fCBC55pGTT9gVaz8Na5CLmxt/urtzRv36w==

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz"
  integrity sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==

node-releases@^2.0.14:
  version "2.0.14"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.14.tgz"
  integrity sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

object-assign@^4, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-filter-sequence@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/object-filter-sequence/-/object-filter-sequence-1.0.0.tgz"
  integrity sha512-CsubGNxhIEChNY4cXYuA6KXafztzHqzLLZ/y3Kasf3A+sa3lL9thq3z+7o0pZqzEinjXT6lXDPAfVWI59dUyzQ==

object-identity-map@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/object-identity-map/-/object-identity-map-1.0.2.tgz"
  integrity sha512-a2XZDGyYTngvGS67kWnqVdpoaJWsY7C1GhPJvejWAFCsUioTAaiTu8oBad7c6cI4McZxr4CmvnZeycK05iav5A==
  dependencies:
    object.entries "^1.1.0"

object-inspect@^1.13.1, object-inspect@^1.9.0:
  version "1.13.1"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz"
  integrity sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.4:
  version "4.1.5"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz"
  integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.0.4, object.entries@^1.1.0:
  version "1.1.7"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.7.tgz"
  integrity sha512-jCBs/0plmPsOnrKAfFQXRG2NFjlhZgjjcBLSmTnEhU8U6vVTsVe8ANeQJCHTl3gSsI4J+0emOoCgoKlmQPMgmA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

on-exit-leak-free@^2.1.0:
  version "2.1.2"
  resolved "https://registry.npmjs.org/on-exit-leak-free/-/on-exit-leak-free-2.1.2.tgz"
  integrity sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz"
  integrity sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

optional-js@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/optional-js/-/optional-js-2.3.0.tgz"
  integrity sha512-B0LLi+Vg+eko++0z/b8zIv57kp7HKEzaPJo7LowJXMUKYdf+3XJGu/cw03h/JhIOsLnP+cG5QnTHAuicjA5fMw==

optionator@^0.9.3:
  version "0.9.3"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz"
  integrity sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

ora@5.4.1, ora@^5.4.1:
  version "5.4.1"
  resolved "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz"
  integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

original-url@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/original-url/-/original-url-1.2.3.tgz"
  integrity sha512-BYm+pKYLtS4mVe/mgT3YKGtWV5HzN/XKiaIu1aK4rsxyjuHeTW9N+xVBEpJcY1onB3nccfH0RbzUEoimMqFUHQ==
  dependencies:
    forwarded-parse "^2.1.0"

os-name@4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/os-name/-/os-name-4.0.1.tgz"
  integrity sha512-xl9MAoU97MH1Xt5K9ERft2YfCAoaO6msy1OBA0ozxEC0x0TmIoE6K3QvgJMMZA9yKGLmHXNY/YZoDbiGDj4zYw==
  dependencies:
    macos-release "^2.5.0"
    windows-release "^4.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  integrity sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  integrity sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  integrity sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.6.1:
  version "1.10.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.10.1.tgz"
  integrity sha512-MkhCqzzBEpPvxxQ71Md0b1Kk51W01lrYvlMzSUaIzNsODdd7mqhiimSZlr+VegAz5Z6Vzt9Xg2ttE//XBhH3EQ==
  dependencies:
    lru-cache "^9.1.1 || ^10.0.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==

path-to-regexp@3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-3.2.0.tgz"
  integrity sha512-jczvQbCUS7XmS7o+y1aEO9OBVFeZBQ1MDSEqmO7xSoPgOPoowY/SxLpZ6Vh97/8qHZOteiCKb7gkG9gA2ZUxJA==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pino-abstract-transport@v1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/pino-abstract-transport/-/pino-abstract-transport-1.1.0.tgz"
  integrity sha512-lsleG3/2a/JIWUtf9Q5gUNErBqwIu1tUKTT3dUzaf5DySw9ra1wcqKjJjLX1VTY64Wk1eEOYsVGSaGfCK85ekA==
  dependencies:
    readable-stream "^4.0.0"
    split2 "^4.0.0"

pino-http@^8.5.1:
  version "8.6.0"
  resolved "https://registry.npmjs.org/pino-http/-/pino-http-8.6.0.tgz"
  integrity sha512-SMkt0R7nzoX76BhTlmCWg0s2F0UYKGeln8y+thL2OUcCxZbbPB1AqgrEMuHAjHyc7oBefext3Af3ZT3k9ieETw==
  dependencies:
    get-caller-file "^2.0.5"
    pino "^8.17.0"
    pino-std-serializers "^6.0.0"
    process-warning "^2.0.0"

pino-std-serializers@^6.0.0:
  version "6.2.2"
  resolved "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-6.2.2.tgz"
  integrity sha512-cHjPPsE+vhj/tnhCy/wiMh3M3z3h/j15zHQX+S9GkTBgqJuTuJzYJ4gUyACLhDaJ7kk9ba9iRDmbH2tJU03OiA==

pino@^8.15.0, pino@^8.17.0:
  version "8.17.1"
  resolved "https://registry.npmjs.org/pino/-/pino-8.17.1.tgz"
  integrity sha512-YoN7/NJgnsJ+fkADZqjhRt96iepWBndQHeClmSBH0sQWCb8zGD74t00SK4eOtKFi/f8TUmQnfmgglEhd2kI1RQ==
  dependencies:
    atomic-sleep "^1.0.0"
    fast-redact "^3.1.1"
    on-exit-leak-free "^2.1.0"
    pino-abstract-transport v1.1.0
    pino-std-serializers "^6.0.0"
    process-warning "^2.0.0"
    quick-format-unescaped "^4.0.3"
    real-require "^0.2.0"
    safe-stable-stringify "^2.3.1"
    sonic-boom "^3.7.0"
    thread-stream "^2.0.0"

pirates@^4.0.4:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

pluralize@8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/pluralize/-/pluralize-8.0.0.tgz"
  integrity sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.3.2:
  version "2.8.8"
  resolved "https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

prettier@^3.2.5:
  version "3.3.3"
  resolved "https://registry.npmjs.org/prettier/-/prettier-3.3.3.tgz"
  integrity sha512-i2tDNA0O5IrMO757lfrdQZCc2jPNDVntV0m/+4whiDfWaTKfMNgR7Qz0NAeGz/nRqF4m5/6CLzbP4/liHt12Ew==

pretty-format@^28.0.0, pretty-format@^28.1.3:
  version "28.1.3"
  resolved "https://registry.npmjs.org/pretty-format/-/pretty-format-28.1.3.tgz"
  integrity sha512-8gFb/To0OmxHR9+ZTb14Df2vNxdGCX8g1xWGUTqUw5TiZvcQf5sHKObd5UcPyLLyowNwDAMTF3XWOG1B6mxl1Q==
  dependencies:
    "@jest/schemas" "^28.1.3"
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

process-warning@^2.0.0:
  version "2.3.2"
  resolved "https://registry.npmjs.org/process-warning/-/process-warning-2.3.2.tgz"
  integrity sha512-n9wh8tvBe5sFmsqlg+XQhaQLumwpqoAUruLwjCopgTmUBjJ/fjtBsJzKleCaIGBOMXYEhp1YfKl4d7rJ5ZKJGA==

process@^0.11.10:
  version "0.11.10"
  resolved "https://registry.npmjs.org/process/-/process-0.11.10.tgz"
  integrity sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz"
  integrity sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

qs@6.11.0, qs@^6.11.0:
  version "6.11.0"
  resolved "https://registry.npmjs.org/qs/-/qs-6.11.0.tgz"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

qs@6.9.7:
  version "6.9.7"
  resolved "https://registry.npmjs.org/qs/-/qs-6.9.7.tgz"
  integrity sha512-IhMFgUmuNpyRfxA90umL7ByLlgRXu6tIfKPpF5TmcfRLlLCckfP/g3IQmju6jjpu+Hh8rA+2p6A27ZSPOOHdKw==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

quick-format-unescaped@^4.0.3:
  version "4.0.4"
  resolved "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz"
  integrity sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.4.3:
  version "2.4.3"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.4.3.tgz"
  integrity sha512-UlTNLIcu0uzb4D2f4WltY6cVjLi+/jEN4lgEUj3E04tpMDpUlkBo/eSn6zou9hum2VMNpCCUone0O0WeJim07g==
  dependencies:
    bytes "3.1.2"
    http-errors "1.8.1"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz"
  integrity sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^18.0.0:
  version "18.2.0"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz"
  integrity sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==

readable-stream@^2.2.2:
  version "2.3.8"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.4.0, readable-stream@^3.6.2:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^4.0.0:
  version "4.5.1"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-4.5.1.tgz"
  integrity sha512-uQjbf34vmf/asGnOHQEw07Q4llgMACQZTWWa4MmICS0IKJoHbLwKCy71H3eR99Dw5iYejc6W+pqZZEeqRtUFAw==
  dependencies:
    abort-controller "^3.0.0"
    buffer "^6.0.3"
    events "^3.3.0"
    process "^0.11.10"
    string_decoder "^1.3.0"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

real-require@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/real-require/-/real-require-0.2.0.tgz"
  integrity sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz"
  integrity sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==
  dependencies:
    resolve "^1.1.6"

redis-commands@^1.7.0:
  version "1.7.0"
  resolved "https://registry.npmjs.org/redis-commands/-/redis-commands-1.7.0.tgz"
  integrity sha512-nJWqw3bTFy21hX/CPKHth6sfhZbdiHP6bTawSgQBlKOVRG7EZkfHbbHwQJnrE4vsQf0CMNE+3gJ4Fmm16vdVlQ==

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/redis-errors/-/redis-errors-1.2.0.tgz"
  integrity sha512-1qny3OExCf0UvUV/5wpYKf2YwPcOqXzkwKKSmKHiE6ZMQs5heeE/c8eXK+PNllPvmjgAbfnsbpkGZWy8cBpn9w==

redis-info@^3.0.8:
  version "3.1.0"
  resolved "https://registry.npmjs.org/redis-info/-/redis-info-3.1.0.tgz"
  integrity sha512-ER4L9Sh/vm63DkIE0bkSjxluQlioBiBgf5w1UuldaW/3vPcecdljVDisZhmnCMvsxHNiARTTDDHGg9cGwTfrKg==
  dependencies:
    lodash "^4.17.11"

redis-parser@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/redis-parser/-/redis-parser-3.0.0.tgz"
  integrity sha512-DJnGAeenTdpMEH6uAJRK/uiyEIH9WVsUmoLwzudwGJUwZPp80PDBWPHXSAGNPwNvIXAbe7MSUB1zQFugFml66A==
  dependencies:
    redis-errors "^1.0.0"

redis@^3.0.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/redis/-/redis-3.1.2.tgz"
  integrity sha512-grn5KoZLr/qrRQVwoSkmzdbw6pwF+/rwODtrOr6vuBRiR/f3rjSTGupbF90Zpqm2oenix8Do6RV7pYEkGwlKkw==
  dependencies:
    denque "^1.5.0"
    redis-commands "^1.7.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"

redlock@^5.0.0-beta.2:
  version "5.0.0-beta.2"
  resolved "https://registry.npmjs.org/redlock/-/redlock-5.0.0-beta.2.tgz"
  integrity sha512-2RDWXg5jgRptDrB1w9O/JgSZC0j7y4SlaXnor93H/UJm/QyDiFgBKNtrh0TI6oCXqYSaSoXxFh6Sd3VtYfhRXw==
  dependencies:
    node-abort-controller "^3.0.1"

reflect-metadata@^0.1.13:
  version "0.1.14"
  resolved "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.1.14.tgz"
  integrity sha512-ZhYeb6nRaXCfhnndflDK8qI6ZQ/YcWZCISRAWICW9XYqMUwjZM9Z0DveWX/ABN01oxSHwVxKQmxeYZSsm0jh5A==

regexp.prototype.flags@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.1.tgz"
  integrity sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    set-function-name "^2.0.0"

relative-microtime@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/relative-microtime/-/relative-microtime-2.0.0.tgz"
  integrity sha512-l18ha6HEZc+No/uK4GyAnNxgKW7nvEe35IaeN54sShMojtqik2a6GbTyuiezkjpPaqP874Z3lW5ysBo5irz4NA==

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

require-in-the-middle@^7.1.1:
  version "7.2.0"
  resolved "https://registry.npmjs.org/require-in-the-middle/-/require-in-the-middle-7.2.0.tgz"
  integrity sha512-3TLx5TGyAY6AOqLBoXmHkNql0HIf2RGbuMgCDT2WO/uGVAPJs6h7Kl+bN6TIZGd9bWhWPwnDnTHGtW8Iu77sdw==
  dependencies:
    debug "^4.1.1"
    module-details-from-path "^1.0.3"
    resolve "^1.22.1"

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz"
  integrity sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz"
  integrity sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==

resolve.exports@^1.1.0:
  version "1.1.1"
  resolved "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.1.tgz"
  integrity sha512-/NtpHNDN7jWhAaQ9BvBUYZ6YTXsRBgfqWFWP7BZBaoMJO/I3G5OFzvTuWNlZC3aPjins1F+TNrLKsGbH4rfsRQ==

resolve@^1.1.6, resolve@^1.20.0, resolve@^1.22.1:
  version "1.22.8"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@4.4.1:
  version "4.4.1"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-4.4.1.tgz"
  integrity sha512-Gk8NlF062+T9CqNGn6h4tls3k6T1+/nXdOcSZVikNVtlRdYpA7wRJJMoXmuvOnLW844rPjdQ7JgXCYM6PPC/og==
  dependencies:
    glob "^9.2.0"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz"
  integrity sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

rxjs@7.8.1, rxjs@^7.2.0, rxjs@^7.5.5:
  version "7.8.1"
  resolved "https://registry.npmjs.org/rxjs/-/rxjs-7.8.1.tgz"
  integrity sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.0.1.tgz"
  integrity sha512-6XbUAseYE2KtOuGueyeobCySj9L4+66Tn6KQMOPQJrAJEowYKW/YR/MGJZl7FdydUdaFu4LYyDZjxf4/Nmo23Q==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@5.1.2, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@5.2.1, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
  integrity sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

safe-stable-stringify@^2.3.1:
  version "2.4.3"
  resolved "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.4.3.tgz"
  integrity sha512-e2bDA2WJT0wxseVd4lsDP4+3ONX6HpMXQa1ZhFQ7SU+GjvORCmShbCMltrtIDfkYhVHrOcPtj+KhmDBdPdZD1g==

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

schema-utils@^3.1.1, schema-utils@^3.1.2:
  version "3.3.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

semver@7.x, semver@^7.3.4, semver@^7.3.5, semver@^7.3.7, semver@^7.3.8, semver@^7.5.2, semver@^7.5.3, semver@^7.5.4:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

send@0.17.2:
  version "0.17.2"
  resolved "https://registry.npmjs.org/send/-/send-0.17.2.tgz"
  integrity sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww==
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "1.8.1"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmjs.org/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.1.tgz"
  integrity sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==
  dependencies:
    randombytes "^2.1.0"

serve-static@1.14.2:
  version "1.14.2"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.14.2.tgz"
  integrity sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-function-length@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.1.1.tgz"
  integrity sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ==
  dependencies:
    define-data-property "^1.1.1"
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

set-function-name@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.1.tgz"
  integrity sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==
  dependencies:
    define-data-property "^1.0.1"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.0"

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shallow-clone-shim@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shallow-clone-shim/-/shallow-clone-shim-2.0.0.tgz"
  integrity sha512-YRNymdiL3KGOoS67d73TEmk4tdPTO9GSMCoiphQsTcC9EtC+AOmMPjkyBkRoCJfW9ASsaZw1craaiw1dPN2D3Q==

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shelljs@0.8.5:
  version "0.8.5"
  resolved "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz"
  integrity sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

sift@16.0.1:
  version "16.0.1"
  resolved "https://registry.npmjs.org/sift/-/sift-16.0.1.tgz"
  integrity sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ==

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz"
  integrity sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

smart-buffer@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  integrity sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==

socks@^2.7.1:
  version "2.7.1"
  resolved "https://registry.npmjs.org/socks/-/socks-2.7.1.tgz"
  integrity sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==
  dependencies:
    ip "^2.0.0"
    smart-buffer "^4.2.0"

sonic-boom@^3.7.0:
  version "3.7.0"
  resolved "https://registry.npmjs.org/sonic-boom/-/sonic-boom-3.7.0.tgz"
  integrity sha512-IudtNvSqA/ObjN97tfgNmOKyDOs4dNcg4cUUsHDebqsgb8wGBBwb31LIgShNO8fye0dFI52X1+tFoKKI6Rq1Gg==
  dependencies:
    atomic-sleep "^1.0.0"

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz"
  integrity sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@0.5.21, source-map-support@^0.5.20, source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.7.4, source-map@^0.7.4:
  version "0.7.4"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz"
  integrity sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==

source-map@^0.6.0, source-map@^0.6.1:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

source-map@^0.8.0-beta.0:
  version "0.8.0-beta.0"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz"
  integrity sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==
  dependencies:
    whatwg-url "^7.0.0"

sparse-bitfield@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz"
  integrity sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ==
  dependencies:
    memory-pager "^1.0.2"

split2@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz"
  integrity sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

sql-summary@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/sql-summary/-/sql-summary-1.0.1.tgz"
  integrity sha512-IpCr2tpnNkP3Jera4ncexsZUp0enJBLr+pHCyTweMUBrbJsTgQeLWx1FXLhoBj/MvcnUQpkgOn2EY8FKOkUzww==

stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz"
  integrity sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz"
  integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==

standard-as-callback@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/standard-as-callback/-/standard-as-callback-2.1.0.tgz"
  integrity sha512-qoRRSyROncaz1z0mvYqIE4lCd9p2R90i6GxW3uZv5ucSu8tU7B5HXUP1gG8pVZsYNVaXjk8ClXHPttLyxAL48A==

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

"statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz"
  integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==

stream-chopper@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/stream-chopper/-/stream-chopper-3.0.1.tgz"
  integrity sha512-f7h+ly8baAE26iIjcp3VbnBkbIRGtrvV0X0xxFM/d7fwLTYnLzDPTXRKNxa2HZzohOrc96NTrR+FaV3mzOelNA==
  dependencies:
    readable-stream "^3.0.6"

streamsearch@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  integrity sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz"
  integrity sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.trim@^1.2.8:
  version "1.2.8"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.8.tgz"
  integrity sha512-lfjY4HcixfQXOfaqCvcBuOIapyaroTXhbkfJN3gcB1OtyupngWK4sEET9Knd0cXd28kTUqu/kHoV4HKSJdnjiQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimend@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.7.tgz"
  integrity sha512-Ni79DqeB72ZFq1uH/L6zJ+DKZTkOtPIHovb3YZHQViE+HDouuU4mBrLOLDn5Dde3RF8qw5qVETEjhu9locMLvA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimstart@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.7.tgz"
  integrity sha512-NGhtDFu3jCEm7B4Fy0DpLewdJQOZcQ0rGbwQ/+stjnrp2i+rlKeCvos9hOIeCmqwratM47OBxY7uFZzjxHXmrg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string_decoder@^1.1.1, string_decoder@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz"
  integrity sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

strnum@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/strnum/-/strnum-1.0.5.tgz"
  integrity sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==

superagent@^8.0.5:
  version "8.1.2"
  resolved "https://registry.npmjs.org/superagent/-/superagent-8.1.2.tgz"
  integrity sha512-6WTxW1EB6yCxV5VFOIPQruWGHqc3yI7hEmZK6h+pyk69Lk/Ut7rLUY6W/ONF2MjBuGjvmMiIpsrVJ2vjrHlslA==
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.4"
    debug "^4.3.4"
    fast-safe-stringify "^2.1.1"
    form-data "^4.0.0"
    formidable "^2.1.2"
    methods "^1.1.2"
    mime "2.6.0"
    qs "^6.11.0"
    semver "^7.3.8"

supertest@^6.1.3:
  version "6.3.3"
  resolved "https://registry.npmjs.org/supertest/-/supertest-6.3.3.tgz"
  integrity sha512-EMCG6G8gDu5qEqRQ3JjjPs6+FYT1a7Hv5ApHvtSghmOFJYtsU5S+pSb6Y2EUeCEY3CmEL3mmQ8YWlPOzQomabA==
  dependencies:
    methods "^1.1.2"
    superagent "^8.0.5"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz"
  integrity sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

swagger-ui-dist@4.18.2:
  version "4.18.2"
  resolved "https://registry.npmjs.org/swagger-ui-dist/-/swagger-ui-dist-4.18.2.tgz"
  integrity sha512-oVBoBl9Dg+VJw8uRWDxlyUyHoNEDC0c1ysT6+Boy6CTgr2rUcLcfPon4RvxgS2/taNW6O0+US+Z/dlAsWFjOAQ==

symbol-observable@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/symbol-observable/-/symbol-observable-4.0.0.tgz"
  integrity sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ==

tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz"
  integrity sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser-webpack-plugin@^5.3.7:
  version "5.3.10"
  resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz"
  integrity sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.20"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.26.0"

terser@^5.26.0:
  version "5.31.3"
  resolved "https://registry.npmjs.org/terser/-/terser-5.31.3.tgz"
  integrity sha512-pAfYn3NIZLyZpa83ZKigvj6Rn9c/vd5KfYGX7cN1mnzqgDcxWvrU5ZtAfIKhEXz9nRecw4z3LXkjaq96/qZqAA==
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz"
  integrity sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thread-stream@^2.0.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/thread-stream/-/thread-stream-2.4.1.tgz"
  integrity sha512-d/Ex2iWd1whipbT681JmTINKw0ZwOUBZm7+Gjs64DHuX34mmw8vJL2bFAaNacaW72zYiTJxSHi5abUuOi5nsfg==
  dependencies:
    real-require "^0.2.0"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz"
  integrity sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz"
  integrity sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

tr46@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz"
  integrity sha512-dTpowEjclQ7Kgx5SdBkqRzVhERQXov8/l9Ft9dVM9fmg0W0KQSVaXX9T4i6twCPNtYiZM53lpSSUAwJbFPOHxA==
  dependencies:
    punycode "^2.1.0"

tr46@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz"
  integrity sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

tree-kill@1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz"
  integrity sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==

ts-jest@28.0.5:
  version "28.0.5"
  resolved "https://registry.npmjs.org/ts-jest/-/ts-jest-28.0.5.tgz"
  integrity sha512-Sx9FyP9pCY7pUzQpy4FgRZf2bhHY3za576HMKJFs+OnQ9jS96Du5vNsDKkyedQkik+sEabbKAnCliv9BEsHZgQ==
  dependencies:
    bs-logger "0.x"
    fast-json-stable-stringify "2.x"
    jest-util "^28.0.0"
    json5 "^2.2.1"
    lodash.memoize "4.x"
    make-error "1.x"
    semver "7.x"
    yargs-parser "^21.0.1"

ts-loader@^9.2.3:
  version "9.5.1"
  resolved "https://registry.npmjs.org/ts-loader/-/ts-loader-9.5.1.tgz"
  integrity sha512-rNH3sK9kGZcH9dYzC7CewQm4NtxJTjSEVRJ2DyBZR7f8/wcta+iV44UPCXc5+nzDzivKtlzV6c9P4e+oFhDLYg==
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"
    source-map "^0.7.4"

ts-node@^10.0.0:
  version "10.9.2"
  resolved "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz"
  integrity sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths-webpack-plugin@4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/tsconfig-paths-webpack-plugin/-/tsconfig-paths-webpack-plugin-4.0.1.tgz"
  integrity sha512-m5//KzLoKmqu2MVix+dgLKq70MnFi8YL8sdzQZ6DblmCdfuq/y3OqvJd5vMndg2KEVCOeNz8Es4WVZhYInteLw==
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.7.0"
    tsconfig-paths "^4.1.2"

tsconfig-paths@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.0.0.tgz"
  integrity sha512-SLBg2GBKlR6bVtMgJJlud/o3waplKtL7skmLkExomIiaAtLGtVsoXIqP3SYdjbcH9lq/KVv7pMZeCBpLYOit6Q==
  dependencies:
    json5 "^2.2.1"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tsconfig-paths@4.2.0, tsconfig-paths@^4.1.2:
  version "4.2.0"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz"
  integrity sha512-NoZ4roiN7LnbKn9QqE1amc9DJfzvZXxF4xDavcOWt1BPkdx+m+0gJuPM+S0vCe7zTJMYUP0R8pO2XMr+Y8oLIg==
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@2.5.0, tslib@^2.1.0, tslib@^2.3.1, tslib@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz"
  integrity sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==

tslib@2.5.3:
  version "2.5.3"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.5.3.tgz"
  integrity sha512-mSxlJJwl3BMEQCUNnxXBU9jP4JBktcEGhURcPR6VQVlnP0FdDEsIaz0C35dXNGLyRfrATNofF0F5p2KPxQgB+w==

tslib@^1.11.1, tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz"
  integrity sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz"
  integrity sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==

type-is@^1.6.4, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typed-array-buffer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.0.tgz"
  integrity sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    is-typed-array "^1.1.10"

typed-array-byte-length@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.0.tgz"
  integrity sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.0.tgz"
  integrity sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.4.tgz"
  integrity sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  integrity sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==

typescript@4.9.5, typescript@^4.3.5:
  version "4.9.5"
  resolved "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz"
  integrity sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==

uid@2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/uid/-/uid-2.0.2.tgz"
  integrity sha512-u3xV3X7uzvi5b1MncmZo3i2Aw222Zk1keqLA1YkHldREkAhAqi65wuPfe7lHx8H/Wzy+8CE7S7uS3jekIM5s8g==
  dependencies:
    "@lukeed/csprng" "^1.0.0"

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unicode-byte-truncate@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unicode-byte-truncate/-/unicode-byte-truncate-1.0.0.tgz"
  integrity sha512-GQgHk6DodEoKddKQdjnv7xKS9G09XCfHWX0R4RKht+EbUMSiVEmtWHGFO8HUm+6NvWik3E2/DG4MxTitOLL64A==
  dependencies:
    is-integer "^1.0.6"
    unicode-substring "^0.1.0"

unicode-substring@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/unicode-substring/-/unicode-substring-0.1.0.tgz"
  integrity sha512-36Xaw9wXi7MB/3/EQZZHkZyyiRNa9i3k9YtPAz2KfqMVH2xutdXyMHn4Igarmnvr+wOrfWa/6njhY+jPpXN2EQ==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

update-browserslist-db@^1.0.13:
  version "1.0.13"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.13.tgz"
  integrity sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==

uuid@10.0.0:
  version "10.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz"
  integrity sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==

uuid@9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/uuid/-/uuid-9.0.0.tgz"
  integrity sha512-MXcSTerfPa4uqyzStbRoTgt5XIe3x5+42+q1sDuy3R5MDk66URdLMOZe5aPX/SQd+kuYAh0FdP/pO28IkQyTeg==

uuid@^11.0.3:
  version "11.0.3"
  resolved "https://registry.npmjs.org/uuid/-/uuid-11.0.3.tgz"
  integrity sha512-d0z310fCWv5dJwnX1Y/MncBAqGMKEzlBb1AOf7z9K8ALnd0utBX/msg/fA0+sbyN1ihbMsLhrBlnl1ak7Wa0rg==

uuid@^8.3.0, uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz"
  integrity sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==

v8-to-istanbul@^9.0.1:
  version "9.2.0"
  resolved "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.2.0.tgz"
  integrity sha512-/EH/sDgxU2eGxajKdwLCDmQ4FWq+kpi3uCmBGpw1xJtnAxEjlD8j8PEiGWpCIMIs3ciNAgH0d3TTJiUkYzyZjA==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

validator@^13.7.0:
  version "13.11.0"
  resolved "https://registry.npmjs.org/validator/-/validator-13.11.0.tgz"
  integrity sha512-Ii+sehpSfZy+At5nPdnyMhx78fEoPDkR2XW/zimHEL3MyGJQOCQ7WeP20jPYRz7ZCpcKLB21NxuXHF3bxjStBQ==

vary@^1, vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

walker@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz"
  integrity sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==
  dependencies:
    makeerror "1.0.12"

watchpack@^2.4.0:
  version "2.4.1"
  resolved "https://registry.npmjs.org/watchpack/-/watchpack-2.4.1.tgz"
  integrity sha512-8wrBCMtVhqcXP2Sup1ctSkga6uc2Bx0IIvKyT7yTFier5AXHooSI+QyQQAtTb7+E0IUCCKyTFmXqdqgum2XWGg==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz"
  integrity sha512-YQ+BmxuTgd6UXZW3+ICGfyqRyHXVlD5GtQr5+qjiNW7bF0cqrzX500HVXPBOvgXb5YnzDd+h0zqyv61KUD7+Sg==

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  integrity sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==

webpack-node-externals@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/webpack-node-externals/-/webpack-node-externals-3.0.0.tgz"
  integrity sha512-LnL6Z3GGDPht/AigwRh2dvL9PQPFQ8skEpVrWZXLWBYmqcaojHNN0onvHzie6rq7EWKrrBfPYqNEzTJgiwEQDQ==

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack@5.82.1:
  version "5.82.1"
  resolved "https://registry.npmjs.org/webpack/-/webpack-5.82.1.tgz"
  integrity sha512-C6uiGQJ+Gt4RyHXXYt+v9f+SN1v83x68URwgxNQ98cvH8kxiuywWGP4XeNZ1paOzZ63aY3cTciCEQJNFUljlLw==
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.0"
    "@webassemblyjs/ast" "^1.11.5"
    "@webassemblyjs/wasm-edit" "^1.11.5"
    "@webassemblyjs/wasm-parser" "^1.11.5"
    acorn "^8.7.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.14.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.9"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.2"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.7"
    watchpack "^2.4.0"
    webpack-sources "^3.2.3"

whatwg-url@^11.0.0:
  version "11.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz"
  integrity sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==
  dependencies:
    tr46 "^3.0.0"
    webidl-conversions "^7.0.0"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz"
  integrity sha512-WUu7Rg1DroM7oQvGWfOiAK21n74Gg+T4elXEQYkOhtyLeWiJFoOGLXPKI/9gzIie9CtwVLm8wtw6YJdKyxSjeg==
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-typed-array@^1.1.11, which-typed-array@^1.1.13:
  version "1.1.13"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.13.tgz"
  integrity sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.4"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

windows-release@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/windows-release/-/windows-release-4.0.0.tgz"
  integrity sha512-OxmV4wzDKB1x7AZaZgXMVsdJ1qER1ed83ZrTYd5Bwq2HfJVg3DJS8nqlAG4sMoJ7mu8cuRmLEYyU13BKwctRAg==
  dependencies:
    execa "^4.0.2"

wrap-ansi@^6.0.1:
  version "6.2.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  integrity sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

write-file-atomic@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz"
  integrity sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  integrity sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yargs-parser@21.1.1, yargs-parser@^21.0.1, yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz"
  integrity sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==

yargs@^17.3.1:
  version "17.7.2"
  resolved "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz"
  integrity sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yarn@^1.22.19:
  version "1.22.21"
  resolved "https://registry.npmjs.org/yarn/-/yarn-1.22.21.tgz"
  integrity sha512-ynXaJsADJ9JiZ84zU25XkPGOvVMmZ5b7tmTSpKURYwgELdjucAOydqIOrOfTxVYcNXe91xvLZwcRh68SR3liCg==

yn@3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz"
  integrity sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  integrity sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==
