{"name": "affiliate-v2", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "env-cmd -f ./.dev.env nest start --watch", "start:worker": "env-cmd -f ./.env.worker nest start --debug --watch", "start:debug": "env-cmd -f ./.dev.env nest start --debug --watch", "worker:debug": "env-cmd -f ./.env.worker nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "artisan": "env-cmd -f ./.dev.env ts-node -r tsconfig-paths/register ./src/console.ts", "artisan:prod": "node ./dist/console.js"}, "dependencies": {"@bull-board/api": "^4.1.1", "@bull-board/express": "^4.1.1", "@elastic/ecs-pino-format": "^1.5.0", "@nestjs/bull": "^0.6.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "^9.0.0", "@nestjs/event-emitter": "^2.1.1", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^9.2.0", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^6.0.4", "@taptap-discovery/api-logger": "^1.0.0", "@taptap-discovery/credentials": "^2.0.5", "@taptap-discovery/http-exception-filter": "^1.0.2", "@taptap-discovery/http-request-utils": "^1.0.1", "@taptap-lyt/sdk": "^0.1.3", "add": "^2.0.6", "axios": "^0.27.2", "bluebird": "^3.7.2", "bull": "^4.8.5", "cache-manager": "^4.1.0", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "csv-parser": "^3.0.0", "date-fns": "^4.1.0", "elastic-apm-node": "^4.1.0", "env-cmd": "^10.1.0", "express-basic-auth": "^1.2.1", "global": "^4.4.0", "joi": "^17.6.0", "json2csv": "^6.0.0-alpha.2", "kafkajs": "^2.1.0", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.46", "mongoose": "^6.12.3", "multer": "^1.4.5-lts.1", "nest-commander": "^3.15.0", "nestjs-pino": "^3.5.0", "pino-http": "^8.5.1", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "yarn": "^1.22.19"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/bull": "^3.15.9", "@types/cache-manager": "^4.0.1", "@types/express": "^4.17.21", "@types/jest": "28.1.4", "@types/json2csv": "^5.0.7", "@types/lodash": "^4.17.10", "@types/multer": "^1.4.12", "@types/node": "^16.0.0", "@types/supertest": "^2.0.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "28.1.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "28.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.0.0", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}